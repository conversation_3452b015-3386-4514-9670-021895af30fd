export const EVENTS = {
  PRACTICE_MODULE: {
    VIEWED: 'view practice page',
    CHANGED_PRACTICE_CONFIG_TAB: 'changed practice category tab',
    ADDING_PRACTICE_CATEGORY_CONFIG: 'added practice category config',
    CLIC<PERSON>_ON_PRACTICE: 'click on start practice',

    // practice play
    VIEWED_PRACTICE_QUESTIONS: 'view practice questions page',

    // practice result
    VIEWED_PRACTICE_RESULT_PAGE: 'view practice result page',
    CLICKED_ON_PRACTICE_AGAIN: 'clicked on practice again',

    // practice result
    VIEWED_PRACTICE_SAVE_PRESET_PAGE: 'view practice save preset page',

    // Recent and Saved Tab
    VISITED_RECENT_AND_SAVED_PAGE: 'visited recent and saved page',
    CLICKED_ON_RECENT_TAB: 'clicked on recent presets tab',
    CLICKED_ON_SAVED_TAB: 'clicked on saved presets tab',

    CLICKED_ON_PRACTICE_RECENT_CONFIG: 'clicked on practice recent preset',
    CLICKED_ON_PRACTICE_SAVED_CONFIG: 'clicked on practice saved preset',

    UPDATE_PRESET_CONFIG_MODAL_SHOWN: 'update preset config modal shown',
    UPDATED_CONFIG_OF_SAVED_PRESET: 'updated config of saved preset',

    CLICK_ON_SAVE_PRESET: 'clicked on save preset',
    CLICKED_ON_GO_BACK: 'clicked on go back',

    CLICKED_ON_ANALYTICS_ICON: 'clicked on preset analytics icon',
    PRESET_ANALYTICS_PAGE_VIEWED: 'viewed preset analytics page',
    CHANGED_ANALYSIS_DURATION: 'changed preset analytics duration',
    CLICKED_ON_BOOKMARK_ICON: 'nets: Click on Bookmark',
  },
};
