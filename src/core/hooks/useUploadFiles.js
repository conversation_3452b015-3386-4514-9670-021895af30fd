import { gql, useMutation } from "@apollo/client"
import { useCallback } from "react"
import { base64ToFile } from "core/utils/base64DataToFile"
import _isEmpty from "lodash/isEmpty"
import uuid from 'react-native-uuid';

const EMPTY_OBJECT = {};

const UPLOAD_FILES = gql`
  mutation UploadFiles($files: [Upload!]!) {
    uploadFiles(files: $files) {
      url
    }
  }
`

const useUploadFiles = () => {

  const [uploadFiles, { loading, error }] = useMutation(
    UPLOAD_FILES
  )

  const uploadFile = useCallback(async ({ fileUri }) => {
    try {
      const imageFile = await base64ToFile({ fileUri: fileUri, fileName: uuid.v4() })
      const response = await uploadFiles({
        variables: {
          files: [imageFile]
        }
      })

      const { uploadFiles: uploadFilesRes } = response?.data ?? EMPTY_OBJECT

      if (!_isEmpty(uploadFilesRes)) {
        const { url } = uploadFilesRes[0] ?? EMPTY_OBJECT
        return url
      }
      return uploadFilesRes
    } catch (err) {
      console.error("Upload error:", err)
      throw err
    }

  }, [uploadFiles])

  return {
    uploadFile,
    loading,
    error,
  }
}

export default useUploadFiles