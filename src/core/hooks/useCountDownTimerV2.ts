import { useEffect, useRef, useState } from 'react';
import { formatTime } from 'core/utils/formatTime';

const useCountDownTimer = ({
  targetTime,
  targetTimeStamp,
}: {
  targetTime?: string;
  targetTimeStamp?: number;
}) => {
  const _targetTimeStamp =
    targetTimeStamp ?? new Date(targetTime ?? '').getTime();

  const [timeLeft, setTimeLeft] = useState({
    timer: _targetTimeStamp - getCurrentTime(),
    formattedTime: formatTime(_targetTimeStamp - getCurrentTime()),
  });

  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!_targetTimeStamp) return;
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);

    timerIntervalRef.current = setInterval(() => {
      const updatedTimeLeft = _targetTimeStamp - getCurrentTime();
      if (updatedTimeLeft > 0) {
        setTimeLeft({
          timer: updatedTimeLeft,
          formattedTime: formatTime(updatedTimeLeft),
        });
      } else if (timerIntervalRef.current)
        clearInterval(timerIntervalRef.current);
    }, 1000);

    return () => {
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    };
  }, [_targetTimeStamp]);

  return timeLeft;
};

export default useCountDownTimer;
