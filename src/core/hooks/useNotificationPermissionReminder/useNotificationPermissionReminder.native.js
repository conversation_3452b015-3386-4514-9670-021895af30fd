import {useCallback, useEffect, useRef, useState} from 'react';
import * as Notifications from 'expo-notifications';
import {Linking, Platform} from 'react-native';
import {useSession} from 'modules/auth/containers/AuthProvider';
import {openBottomSheet} from 'molecules/BottomSheet/BottomSheet';
import NotificationPermissionBottomSheet
  from 'shared/NotificationPermissionBottomSheet/NotificationPermissionBottomSheet';
import Analytics from 'core/analytics';
import {ANALYTICS_EVENTS} from 'core/analytics/const';
import {NOTIFICATION_CONFIG} from 'core/constants/appConstants';
import _get from 'lodash/get';
import {shouldAskForNotificationPermission} from 'core/utils/pushNotification';
import useLocalCache from 'core/hooks/useLocalCache';
import {NOTIFICATION_CONFIG_KEYS} from 'core/constants/notificationConfig';
import _toNumber from 'lodash/toNumber';

const useNotificationPermissionReminder = () => {
    const {userId, user} = useSession();
    const notificationConfigKey = `${NOTIFICATION_CONFIG}-${userId}`;

    const {data: notificationConfig, setData: setNotificationConfig} =
        useLocalCache(notificationConfigKey);
    const hasShownInitialReminder = _get(
        notificationConfig,
        NOTIFICATION_CONFIG_KEYS.HAS_SHOWN_INITIAL_REMINDER,
        false,
    );

    const [askForNotificationPermission, setAskForNotificationPermission] =
        useState(false);

    useEffect(() => {
        (async () => {
            const askForNotificationPermission =
                await shouldAskForNotificationPermission({userId});
            setAskForNotificationPermission(askForNotificationPermission);
        })();
    }, [userId]);

    const [permissionStatus, setPermissionStatus] = useState(null);
    const [isCheckingPermission, setIsCheckingPermission] = useState(false);
    const [shouldNavigateToSettings, setShouldNavigateToSettings] =
        useState(false);

    const checkNotificationDenialCount = useCallback(async () => {
        try {
            return _get(notificationConfig, 'notificationDeniedCount', 0);
        } catch (error) {
            console.error('Error parsing notification config:', error);
            return 0;
        }
    }, [notificationConfig]);

    const navigateToSettings = useCallback(async () => {
        try {
            Analytics.track(
                ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.NAVIGATE_TO_SETTINGS,
                {
                    userId,
                },
            );

            if (Platform.OS === 'ios') {
                await Linking.openURL('app-settings:');
            } else {
                await Linking.openSettings();
            }
        } catch (error) {
            console.error('Error opening settings:', error);
            Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.SETTINGS_ERROR, {
                error: error.message,
                userId,
            });
        }
    }, [userId]);

    const updateNotificationDenielCount = useCallback(() => {
        setAskForNotificationPermission(false);
        setNotificationConfig({
            ...notificationConfig,
            [NOTIFICATION_CONFIG_KEYS.NOTIFICATION_DENIED_COUNT]:
            _toNumber(
                _get(
                    notificationConfig,
                    [NOTIFICATION_CONFIG_KEYS.NOTIFICATION_DENIED_COUNT],
                    0,
                ),
            ) + 1,
            [NOTIFICATION_CONFIG_KEYS.LAST_NOTIFICATION_DENIED_TIME]:
                getCurrentTime(),
        });
    }, [setNotificationConfig, notificationConfig]);

    const handleEnableNotifications = useCallback(async () => {
        try {
            Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.ENABLE_PRESSED, {
                userId,
            });

            const {status} = await Notifications.requestPermissionsAsync({
                ios: {
                    allowAlert: true,
                    allowBadge: true,
                    allowSound: true,
                    allowAnnouncements: true,
                },
            });

            const isGranted = status === 'granted';
            setPermissionStatus(isGranted ? 'granted' : 'denied');

            Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.RESULT, {
                userId,
                granted: isGranted,
                source: 'reminder_bottom_sheet',
            });

            if (isGranted) {
                return true;
            }

            return false;
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.REQUEST_ERROR, {
                error: error.message,
                userId,
            });
            return false;
        }
    }, [userId]);

    // Handle dismissing the reminder
    const handleDismissReminder = useCallback(
        (closeBottomSheet) => {
            Analytics.track(
                ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.REMINDER_DISMISSED,
                {
                    userId,
                },
            );
            updateNotificationDenielCount();
            closeBottomSheet?.();
        },
        [userId, updateNotificationDenielCount],
    );

    const showNotificationReminder = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.REMINDER_SHOWN, {
            userId,
        });

        openBottomSheet({
            content: ({closeBottomSheet}) => (
                <NotificationPermissionBottomSheet
                    onEnableNotifications={
                        shouldNavigateToSettings
                            ? navigateToSettings
                            : handleEnableNotifications
                    }
                    onDismiss={() => handleDismissReminder(closeBottomSheet)}
                    closeBottomSheet={closeBottomSheet}
                    shouldNavigateToSettings={shouldNavigateToSettings}
                />
            ),
            styles: {
                closeButton: {
                    opacity: 1,
                    borderWidth: 4,
                },
                frame: {
                    borderTopLeftRadius: 24,
                    borderTopRightRadius: 24,
                },
            },
            dismissOnOverlayPress: false,
            onCloseBottomSheet: handleDismissReminder,
        });
    }, [
        handleDismissReminder,
        handleEnableNotifications,
        navigateToSettings,
        shouldNavigateToSettings,
        userId,
    ]);

    const checkNotificationPermission = useCallback(async () => {
        if (!userId || isCheckingPermission) return;

        setIsCheckingPermission(true);
        try {
            const {status, ios} = await Notifications.getPermissionsAsync();
            const isAllowed =
                status === 'granted' ||
                (Platform.OS === 'ios' &&
                    ios?.status === Notifications.IosAuthorizationStatus.PROVISIONAL);

            setPermissionStatus(isAllowed ? 'granted' : 'denied');

            if (!isAllowed) {
                const denialCount = await checkNotificationDenialCount();
                setShouldNavigateToSettings(denialCount >= 1);

                if (askForNotificationPermission) {
                    showNotificationReminder();
                }
            }
        } catch (error) {
            console.error('Error checking notification permission:', error);
            Analytics.track(ANALYTICS_EVENTS.NOTIFICATION_PERMISSION.CHECK_ERROR, {
                error: error.message,
                userId,
            });
        } finally {
            setIsCheckingPermission(false);
        }
    }, [
        userId,
        isCheckingPermission,
        checkNotificationDenialCount,
        askForNotificationPermission,
        showNotificationReminder,
    ]);

    const checkNotificationPermissionRef = useRef(checkNotificationPermission);
    checkNotificationPermissionRef.current = checkNotificationPermission;

    const timeOutRef = useRef(null);

    useEffect(() => {
        if (userId && user && !hasShownInitialReminder) {
            setNotificationConfig({
                ...notificationConfig,
                [NOTIFICATION_CONFIG_KEYS.HAS_SHOWN_INITIAL_REMINDER]: true,
            });
            if (timeOutRef.current) clearTimeout(timeOutRef.current);
            timeOutRef.current = setTimeout(() => {
                checkNotificationPermissionRef.current();
            }, 2000);

            return () => clearTimeout(timeOutRef.current);
        }
    }, [
        userId,
        user,
        hasShownInitialReminder,
        notificationConfig,
        setNotificationConfig,
    ]);

    return {
        permissionStatus,
        isCheckingPermission,
        askForNotificationPermission,
        shouldNavigateToSettings,
        recheckPermission: checkNotificationPermission,
        showNotificationReminder,
        navigateToSettings,
    };
};

export default useNotificationPermissionReminder;
