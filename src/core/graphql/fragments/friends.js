import { gql } from '@apollo/client';
import { USER_PUBLIC_DETAIL_FRAGMENT } from './userPublicDetail';

export const FOLLOWERS_AND_FOLLOWEE_FRAGMENT = gql`
  fragment FollowersAndFolloweeFragment on FollowersAndFolloweeOutput {
    _id
    followerId
    followeeId
    followedAt
    userInfo {
      ...UserPublicDetailFields
    }
  }
  ${USER_PUBLIC_DETAIL_FRAGMENT}
`;

export const FRIEND_REQUEST_FRAGMENT = gql`
  fragment FriendRequestFragment on FriendRequestOutput {
    _id
    senderId
    receiverId
    status
    sentAt
    respondedAt
    sender {
      ...UserPublicDetailFields
    }
  }
  ${USER_PUBLIC_DETAIL_FRAGMENT}
`;

export const FRIENDS_FRAGMENT = gql`
  fragment FriendsFragment on FriendsOutput {
    _id
    senderId
    receiverId
    acceptedAt
    isOnline
    currActivity
    friendInfo {
      ...UserPublicDetailFields
    }
  }
  ${USER_PUBLIC_DETAIL_FRAGMENT}
`;
