import { createContext, memo, useContext } from "react";

const contextFactory = (
    useContextState,
    ContextComponent
) => {
    const Context = createContext(Object.create(null));

    return {
        Provider: memo(({ children }) => (
            <Context.Provider value={useContextState()}>
                {children}
                {ContextComponent}
            </Context.Provider>
        )),
        useContext: () => useContext(Context),
    };
};

export default contextFactory;
