/* eslint-disable import/extensions */
import { useMutation, gql } from '@apollo/client';
import { useCallback } from 'react';

import { useStorageState } from 'core/hooks/useStorageState';
import { CURRENT_USER_FRAGMENT } from '../../graphql/fragments/user';

const LEGACY_GOOGLE_LOGIN_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  mutation LegacyGoogleLogin($idToken: String!, $guestId: ID) {
    legacyGoogleLogin(idToken: $idToken, guestId: $guestId) {
      ...CurrentUserFields
      isSignup
    }
  }
`;

const useGoogleLoginQuery = () => {
  const [login] = useMutation(LEGACY_GOOGLE_LOGIN_QUERY);
  const [guestId] = useStorageState('guestId');

  const loginWithGoogle = useCallback(
    ({ idToken }) =>
      login({
        variables: {
          idToken,
          guestId,
        },
      }),
    [login, guestId],
  );

  return {
    loginWithGoogle,
  };
};

export default useGoogleLoginQuery;
