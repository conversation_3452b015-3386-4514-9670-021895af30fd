import { CACHE_CONFIG } from 'core/constants/cacheConstants';
import { RiveCacheManager } from './common/RiveCacheManager';

export class WebRiveCacheManager extends RiveCacheManager {
  private dbName = 'RiveAnimationCache';

  private dbVersion = 1;

  private db: IDBDatabase | null = null;

  /**
   * Initialize the cache manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize IndexedDB
      await this.initializeDB();

      // Load existing metadata
      await this.loadMetadata();

      // Check if cleanup is needed
      if (await this.shouldCleanup()) {
        await this.cleanup();
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize web cache manager:', error);
      throw error;
    }
  }

  /**
   * Download a file and return blob URL
   */
  async downloadFile(
    url: string,
    onProgress?: (progress: number) => void,
  ): Promise<string> {
    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentLength = response.headers.get('content-length');
      const totalSize = contentLength ? parseInt(contentLength, 10) : 0;

      if (totalSize > CACHE_CONFIG.MAX_FILE_SIZE) {
        throw new Error(`File too large: ${totalSize} bytes`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const chunks: Uint8Array[] = [];
      let receivedLength = 0;

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        chunks.push(value);
        receivedLength += value.length;

        if (totalSize > 0 && onProgress) {
          const progress = (receivedLength / totalSize) * 100;
          onProgress(Math.round(progress));
        }
      }

      // Combine chunks into single array
      const allChunks = new Uint8Array(receivedLength);
      let position = 0;
      for (const chunk of chunks) {
        allChunks.set(chunk, position);
        position += chunk.length;
      }

      // Create blob and store in IndexedDB
      const blob = new Blob([allChunks], { type: 'application/octet-stream' });
      const blobUrl = URL.createObjectURL(blob);

      // Store in IndexedDB
      await this.storeFileInDB(url, blob);

      onProgress?.(100);
      return blobUrl;
    } catch (error) {
      throw new Error(`Download failed: ${error.message}`);
    }
  }

  /**
   * Get local path (blob URL) for a URL
   */
  getLocalPath(url: string): string {
    // For web, we'll return the original URL and handle caching in getCachedUrl
    return url;
  }

  /**
   * Check if file exists in IndexedDB
   */
  async fileExists(path: string): Promise<boolean> {
    try {
      const blob = await this.getFileFromDB(path);
      return blob !== null;
    } catch {
      return false;
    }
  }

  /**
   * Delete a file from IndexedDB
   */
  async deleteFile(url: string): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readwrite');
      const store = transaction.objectStore('files');
      const request = store.delete(url);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get file size from IndexedDB
   */
  async getFileSize(url: string): Promise<number> {
    if (!this.db) return 0;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readonly');
      const store = transaction.objectStore('files');
      const request = store.get(url);

      request.onsuccess = () => {
        const { result } = request;
        resolve(result ? result.size : 0);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Ensure directory exists (no-op for web)
   */
  async ensureDirectoryExists(path: string): Promise<void> {
    // No-op for web
  }

  /**
   * Override getCachedUrl to handle blob URLs
   */
  async getCachedUrl(url: string): Promise<string> {
    if (!this.isInitialized) {
      return url;
    }

    try {
      const blob = await this.getFileFromDB(url);
      if (blob) {
        const blobUrl = URL.createObjectURL(blob);

        // Update metadata
        const meta = this.metadata.get(url);
        if (meta) {
          meta.lastAccessed = Date.now();
          meta.localPath = blobUrl;
          this.metadata.set(url, meta);
        }

        return blobUrl;
      }
    } catch (error) {
      console.warn(`Failed to get cached file for ${url}:`, error);
    }

    return url;
  }

  /**
   * Get cache storage info
   */
  async getCacheStorageInfo(): Promise<{ size: number; fileCount: number }> {
    if (!this.db) return { size: 0, fileCount: 0 };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readonly');
      const store = transaction.objectStore('files');
      const request = store.getAll();

      request.onsuccess = () => {
        const files = request.result;
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        resolve({
          size: totalSize,
          fileCount: files.length,
        });
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get storage item from localStorage
   */
  protected async getStorageItem(key: string): Promise<string | null> {
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  }

  /**
   * Set storage item in localStorage
   */
  protected async setStorageItem(key: string, value: string): Promise<void> {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('Failed to set localStorage item:', error);
    }
  }

  /**
   * Initialize IndexedDB
   */
  private async initializeDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object store for cached files
        if (!db.objectStoreNames.contains('files')) {
          const store = db.createObjectStore('files', { keyPath: 'url' });
          store.createIndex('downloadedAt', 'downloadedAt', { unique: false });
        }
      };
    });
  }

  /**
   * Store file in IndexedDB
   */
  private async storeFileInDB(url: string, blob: Blob): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readwrite');
      const store = transaction.objectStore('files');

      const fileData = {
        url,
        blob,
        downloadedAt: Date.now(),
        size: blob.size,
      };

      const request = store.put(fileData);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get file from IndexedDB
   */
  private async getFileFromDB(url: string): Promise<Blob | null> {
    if (!this.db) return null;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readonly');
      const store = transaction.objectStore('files');
      const request = store.get(url);

      request.onsuccess = () => {
        const { result } = request;
        resolve(result ? result.blob : null);
      };
      request.onerror = () => reject(request.error);
    });
  }
}

// Export singleton instance
const riveCacheManager = new WebRiveCacheManager();

export default riveCacheManager;
