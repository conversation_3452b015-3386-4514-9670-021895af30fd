import { Platform } from 'react-native';
import { base64ToFile } from '../base64DataToFile';

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'web',
  },
}));

// Mock fetch for blob URL tests
global.fetch = jest.fn();

describe('base64ToFile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Platform.OS = 'web';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should handle valid base64 data URL', async () => {
    const validBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/AB8A';
    const fileName = 'test.jpg';

    const result = await base64ToFile({ fileUri: validBase64, fileName });

    expect(result).toBeInstanceOf(File);
    expect(result.name).toBe(fileName);
    expect(result.type).toBe('image/jpeg');
  });

  it('should handle blob URLs', async () => {
    const blobUrl = 'blob:http://localhost:3000/12345-6789';
    const fileName = 'test.jpg';
    const mockBlob = new Blob(['test'], { type: 'image/jpeg' });

    fetch.mockResolvedValueOnce({
      blob: () => Promise.resolve(mockBlob),
    });

    const result = await base64ToFile({ fileUri: blobUrl, fileName });

    expect(fetch).toHaveBeenCalledWith(blobUrl);
    expect(result).toBeInstanceOf(File);
    expect(result.name).toBe(fileName);
    expect(result.type).toBe('image/jpeg');
  });

  it('should handle HTTP URLs', async () => {
    const httpUrl = 'https://example.com/image.jpg';
    const fileName = 'test.jpg';
    const mockBlob = new Blob(['test'], { type: 'image/jpeg' });

    fetch.mockResolvedValueOnce({
      blob: () => Promise.resolve(mockBlob),
    });

    const result = await base64ToFile({ fileUri: httpUrl, fileName });

    expect(fetch).toHaveBeenCalledWith(httpUrl);
    expect(result).toBeInstanceOf(File);
    expect(result.name).toBe(fileName);
    expect(result.type).toBe('image/jpeg');
  });

  it('should throw error for invalid base64 data', async () => {
    const invalidBase64 = 'data:image/jpeg;base64,invalid-base64-data!!!';
    const fileName = 'test.jpg';

    await expect(base64ToFile({ fileUri: invalidBase64, fileName })).rejects.toThrow(
      'Failed to convert file URI to File object'
    );
  });

  it('should throw error for empty base64 data', async () => {
    const emptyBase64 = 'data:image/jpeg;base64,';
    const fileName = 'test.jpg';

    await expect(base64ToFile({ fileUri: emptyBase64, fileName })).rejects.toThrow(
      'Failed to convert file URI to File object'
    );
  });

  it('should throw error for unsupported URI format', async () => {
    const unsupportedUri = 'file:///path/to/file.jpg';
    const fileName = 'test.jpg';

    await expect(base64ToFile({ fileUri: unsupportedUri, fileName })).rejects.toThrow(
      'Failed to convert file URI to File object'
    );
  });

  it('should handle native platform correctly', async () => {
    Platform.OS = 'ios';
    const fileUri = 'file:///path/to/image.jpg';
    const fileName = 'test.jpg';

    const result = await base64ToFile({ fileUri, fileName });

    expect(result.uri).toBe(fileUri);
    expect(result.name).toBe(fileName);
    expect(result.type).toBe('image');
  });

  it('should handle fetch errors gracefully', async () => {
    const blobUrl = 'blob:http://localhost:3000/12345-6789';
    const fileName = 'test.jpg';

    fetch.mockRejectedValueOnce(new Error('Network error'));

    await expect(base64ToFile({ fileUri: blobUrl, fileName })).rejects.toThrow(
      'Failed to convert file URI to File object'
    );
  });
});
