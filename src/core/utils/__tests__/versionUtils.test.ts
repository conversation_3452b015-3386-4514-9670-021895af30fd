import { compareVersions, isUpdateAvailable, getUpdateType } from '../versionUtils';

describe('versionUtils', () => {
  describe('compareVersions', () => {
    it('should return 1 when latest version is higher', () => {
      expect(compareVersions('1.8.000', '1.9.000')).toBe(1);
      expect(compareVersions('1.8.000', '2.0.000')).toBe(1);
      expect(compareVersions('1.8.000', '1.8.001')).toBe(1);
    });

    it('should return 0 when versions are equal', () => {
      expect(compareVersions('1.8.000', '1.8.000')).toBe(0);
      expect(compareVersions('2.0.0', '2.0.0')).toBe(0);
    });

    it('should return -1 when current version is higher', () => {
      expect(compareVersions('1.9.000', '1.8.000')).toBe(-1);
      expect(compareVersions('2.0.000', '1.9.999')).toBe(-1);
    });

    it('should handle different version formats', () => {
      expect(compareVersions('1.8', '1.8.0')).toBe(0);
      expect(compareVersions('1.8.0', '1.8')).toBe(0);
      expect(compareVersions('1.8', '1.9')).toBe(1);
    });

    it('should handle invalid versions', () => {
      expect(compareVersions('', '1.8.000')).toBe(0);
      expect(compareVersions('1.8.000', '')).toBe(0);
      expect(compareVersions('', '')).toBe(0);
    });
  });

  describe('isUpdateAvailable', () => {
    it('should return true when update is available', () => {
      expect(isUpdateAvailable('1.8.000', '1.9.000')).toBe(true);
      expect(isUpdateAvailable('1.8.000', '2.0.000')).toBe(true);
    });

    it('should return false when no update is available', () => {
      expect(isUpdateAvailable('1.8.000', '1.8.000')).toBe(false);
      expect(isUpdateAvailable('1.9.000', '1.8.000')).toBe(false);
    });
  });

  describe('getUpdateType', () => {
    it('should return major for major version updates', () => {
      expect(getUpdateType('1.8.000', '2.0.000')).toBe('major');
      expect(getUpdateType('1.8.000', '3.0.000')).toBe('major');
    });

    it('should return minor for minor version updates', () => {
      expect(getUpdateType('1.8.000', '1.9.000')).toBe('minor');
      expect(getUpdateType('1.8.000', '1.10.000')).toBe('minor');
    });

    it('should return patch for patch version updates', () => {
      expect(getUpdateType('1.8.000', '1.8.001')).toBe('patch');
      expect(getUpdateType('1.8.000', '1.8.100')).toBe('patch');
    });

    it('should handle invalid versions', () => {
      expect(getUpdateType('', '1.8.000')).toBe('patch');
      expect(getUpdateType('1.8.000', '')).toBe('patch');
    });
  });
});
