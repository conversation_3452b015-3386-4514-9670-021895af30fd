import * as SplashScreen from 'expo-splash-screen';
import { Platform } from 'react-native';

/**
 * Helper function to ensure the splash screen is properly handled
 * This is especially important for iOS where we need to ensure SplashScreenNative.show() is called
 */
export const ensureSplashScreenShown = async () => {
  try {
    // On iOS, we need to explicitly show the splash screen before hiding it
    if (Platform.OS === 'ios') {
      // This will ensure the splash screen is shown before we try to hide it
      await SplashScreen.preventAutoHideAsync();
    }
  } catch (error) {
    console.warn('Error ensuring splash screen is shown:', error);
  }
};

/**
 * Helper function to safely hide the splash screen
 */
export const hideSplashScreen = async () => {
  try {
    // Add a small delay to ensure the view controller is ready
    await new Promise((resolve) => setTimeout(resolve, 200));
    await SplashScreen.hideAsync();
  } catch (error) {
    console.error('Error hiding splash screen:', error);
  }
};
