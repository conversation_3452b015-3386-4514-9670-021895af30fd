import mime from 'mime';
import { Platform } from 'react-native';
import { ReactNativeFile } from 'apollo-upload-client';

export const base64ToFile = ({ fileUri, fileName }) => {
    if (Platform.OS !== 'web') {
        return new ReactNativeFile({
            uri: fileUri,
            type: mime.getType(fileUri) || 'image',
            name: fileName,
        })
    }

    // For web, handle the base64 string conversion
    const [metadata, base64Data] = fileUri.split(',');
    const binaryData = atob(base64Data);

    const byteArray = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
        byteArray[i] = binaryData.charCodeAt(i);
    }

    const blob = new Blob([byteArray], { type: metadata.split(';')[0].split(':')[1] });

    const file = new File([blob], fileName, { type: blob.type });

    return file;
};
