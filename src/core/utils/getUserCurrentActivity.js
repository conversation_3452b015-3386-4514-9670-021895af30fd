import _includes from 'lodash/includes';
import _split from 'lodash/split';
import _size from 'lodash/size';
import _isNil from 'lodash/isNil';
import USER_ACTIVITY from '../constants/userActivityConstants';

export const getCurrentActiveGameId = (args) => {
  const { currentUrl } = args ?? EMPTY_OBJECT;

  if (_isNil(currentUrl)) {
    return '';
  }

  try {
    const urlsPart = _split(currentUrl, '/');

    if (_includes(urlsPart, 'play-time') && _includes(urlsPart, 'challenge')) {
      if (_size(urlsPart) === 4) {
        return urlsPart[2];
      }
    }

    if (_includes(urlsPart, 'puzzle-game') && _includes(urlsPart, 'game')) {
      if (_size(urlsPart) === 4) {
        return urlsPart[3];
      }
    }

    if (_includes(urlsPart, 'game') && _includes(urlsPart, 'result')) {
      if (_size(urlsPart) === 4) {
        return urlsPart[2];
      }
    }

    if (_size(urlsPart) === 4) {
      if (_includes(urlsPart, 'game')) {
        return urlsPart[2];
      }
    }
    return '';
  } catch (e) {
    return '';
  }
};

export const getUserCurrentActivity = (args) => {
  const { currentUrl } = args ?? EMPTY_OBJECT;

  try {
    if (_isNil(currentUrl)) {
      return USER_ACTIVITY.EXPLORING;
    }
    const urlsPart = _split(currentUrl, '/');

    if (_includes(urlsPart, 'contest') && _includes(urlsPart, 'play')) {
      return USER_ACTIVITY.IN_CONTEST;
    }

    if (_includes(urlsPart, 'search')) {
      return USER_ACTIVITY.SEARCHING_FOR_OPPONENT;
    }
    if (_includes(urlsPart, 'play') && _includes(urlsPart, 'daily-challenge')) {
      return USER_ACTIVITY.PLAYING_DC;
    }
    if (_includes(urlsPart, 'daily-challenge')) {
      return USER_ACTIVITY.IN_DC_WAITING_ROOM;
    }
    if (_includes(urlsPart, 'daily-challenge-leaderboard')) {
      return USER_ACTIVITY.VIEWING_DC_LEADERBOARD;
    }
    if (_includes(urlsPart, 'nets') && _includes(urlsPart, 'play')) {
      return USER_ACTIVITY.PRACTICING_NETS;
    }
    if (_includes(urlsPart, 'result')) {
      return USER_ACTIVITY.EXPLORING;
    }
    if (_includes(urlsPart, 'play') && _includes(urlsPart, 'game')) {
      return USER_ACTIVITY.IN_GAME;
    }

    if (_includes(urlsPart, 'puzzle') && _includes(urlsPart, 'play')) {
      return USER_ACTIVITY.IN_GAME;
    }

    if (_includes(urlsPart, 'puzzle') && _includes(urlsPart, 'result')) {
      return USER_ACTIVITY.VIEWING_RESULT;
    }

    if (_includes(urlsPart, 'result') && _includes(urlsPart, 'game')) {
      return USER_ACTIVITY.VIEWING_RESULT;
    }
    if (_includes(urlsPart, 'chat') && _includes(urlsPart, 'game')) {
      return USER_ACTIVITY.ON_CHAT_PAGE;
    }
    if (_includes(urlsPart, 'play-time') && _includes(urlsPart, 'challenge')) {
      return USER_ACTIVITY.IN_GAME;
    }
    if (_includes(urlsPart, 'fix-my-rating')) {
      return USER_ACTIVITY.FIXING_RATING;
    }
    if (_includes(urlsPart, 'onboarding')) {
      return USER_ACTIVITY.ONBOARDING;
    }
    return USER_ACTIVITY.EXPLORING;
  } catch (error) {
    return USER_ACTIVITY.EXPLORING;
  }
};
