import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    borderRadius: 16,
    height: 'auto',
    maxWidth: 450,
  },
  container: {
    flex: 1,
    borderWidth: 1,
    width: '100%',
    borderColor: dark.colors.primary,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: dark.colors.background,
    zIndex: 5,
  },
  topGradientContainer: {
    position: 'absolute',
    left: -100,
    bottom: 10,
    zIndex: 0,
    overflow: 'hidden',
  },
  bottomGradientContainer: {
    position: 'absolute',
    left: 160,
    top: -40,
    zIndex: -10,
    overflow: 'hidden',
  },
  shadowCard: {
    position: 'absolute',
    borderRadius: 14,
    top: 6,
    left: 6,
    zIndex: 1,
  },
});

export default styles;
