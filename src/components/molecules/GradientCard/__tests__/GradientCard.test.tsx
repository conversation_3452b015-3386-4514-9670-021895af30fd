import React from 'react';
import { render } from '@testing-library/react-native';
import { Text } from 'react-native';
import GradientCard from '../GradientCard';

describe('GradientCard', () => {
  const defaultProps = {
    gradientColor: 'blue',
    borderColor: 'red',
  };

  it('renders without crashing', () => {
    const { getByTestId } = render(
      <GradientCard {...defaultProps}>
        <></>
      </GradientCard>,
    );
    expect(getByTestId('gradientCardContainer')).toBeTruthy();
  });

  it('renders children correctly', () => {
    const childText = 'Test Child';
    const { getByText } = render(
      <GradientCard {...defaultProps}>
        <Text>{childText}</Text>
      </GradientCard>,
    );
    expect(getByText(childText)).toBeTruthy();
  });

  it('does not render shadow when shadowWidth is 0 or not provided', () => {
    const { queryByTestId } = render(
      <GradientCard {...defaultProps}>
        <></>
      </GradientCard>,
    );
    expect(queryByTestId('gradientCardShadow')).toBeNull();

    const { queryByTestId: queryByTestIdZero } = render(
      <GradientCard {...defaultProps} shadowWidth={0}>
        <></>
      </GradientCard>,
    );
    expect(queryByTestIdZero('gradientCardShadow')).toBeNull();
  });

  it('renders shadow when shadowWidth is greater than 0', () => {
    const { getByTestId } = render(
      <GradientCard {...defaultProps} shadowWidth={5}>
        <></>
      </GradientCard>,
    );
    expect(getByTestId('gradientCardShadow')).toBeTruthy();
  });

  it('applies correct styles based on props', () => {
    const { toJSON } = render(
      <GradientCard
        {...defaultProps}
        shadowWidth={10}
        shadowColor="green"
        shadowOffsetX={2}
        shadowOffsetY={3}
      >
        <></>
      </GradientCard>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
