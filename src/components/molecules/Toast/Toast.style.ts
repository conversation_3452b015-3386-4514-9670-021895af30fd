import { Platform, StyleSheet } from 'react-native';

import dark from '../../../core/constants/themes/dark';

export const styleVariables = {
  loaderColor: '#fff',
  iconColor: '#fff',
  loadingToastBackgroundColor: '#312fc7',
  toastBackgroundColor: dark.colors.primary,
};

export default StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    bottom: 80,
    width: '100%',
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    ...Platform.select({
      web: {
        position: 'absolute',
      },
      native: {
        position: 'absolute',
      },
    }),
  },
  loadingContentContainer: {
    borderRadius: 8,
    backgroundColor: '#fff',
    shadowColor: '#000',
    width: '100%',
    shadowOpacity: 0.3,
    maxWidth: 460,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 6 },
    elevation: 8,
  },
  loadingToastContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingHorizontal: 32,
    // paddingVertical: 16,
    borderRadius: 8,
    // gap: 16,
  },
  loadingToastContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    flex: 1,
    gap: 8,
  },
  contentContainer: {
    borderRadius: 8,
    backgroundColor: '#fff',
    shadowColor: '#000',
    maxWidth: 460,
    width: '100%',
    shadowOpacity: 0.3,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 6 },
    elevation: 8,
    // padding: 16,
  },
  toastContainer: {
    flexShrink: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  toastGradientContainer: {
    // padding: 16,
    maxWidth: 460,

    flexDirection: 'row',
    alignItems: 'center',
    // paddingHorizontal: 26,
    // paddingVertical: 24,
    borderRadius: 8,
    // marginHorizontal: 0,
    // width: '100%',
    ...Platform.select({
      web: {
        maxWidth: 460,
      },
    }),
  },
  loaderContainer: {
    flex: 0,
    backgroundColor: 'transparent',
    marginLeft: 8,
  },
  loadingText: {
    color: '#fff',
    fontSize: 14,
    paddingLeft: 8,
    paddingRight: 16,
    paddingVertical: 16,
    marginVertical: 16,
    marginRight: 16,
  },
  messageContainer: {
    flexShrink: 1,
    paddingHorizontal: 16,
    paddingLeft: 8,
    paddingRight: 16,
    // paddingVertical: 16,
    marginVertical: 16,
    marginRight: 16,
  },
  description: {
    fontSize: 13,
    // lineHeight: 13 * 1.5,
    color: '#3e3e42',
    flexShrink: 1,
  },
  title: {
    // fontFamily: modules.nativeUI.fonts.fontNames.bold,
    fontSize: 14,
    color: '#0a0a14',
    flexShrink: 1,
  },
  iconStyle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },

  // cta styles
  ctaViewContainer: {
    marginTop: 7,
    flexShrink: 1,
  },
  ctaButtonContainer: {
    alignSelf: 'flex-end',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.6)',
    flexShrink: 1,
  },
  ctaText: {
    color: '#fff',
    fontSize: 13,
    flexShrink: 1,
  },
});
