import React, { useState } from 'react';
import { Animated, Text, TouchableOpacity, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import styles from './ToggleSwitch.style';

interface ToggleSwitchProps {
  label: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const ToggleSwitch = ({ label, value, onValueChange }: ToggleSwitchProps) => {
  const [animation] = useState(new Animated.Value(value ? 1 : 0));

  const handlePress = () => {
    const newValue = !value;
    onValueChange(newValue);
    Animated.timing(animation, {
      toValue: newValue ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const backgroundColor = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [dark.colors.text, dark.colors.secondary],
  });

  const translateX = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 15],
  });

  return (
    <TouchableOpacity onPress={handlePress} activeOpacity={1}>
      <View style={styles.container}>
        <Text style={styles.label}>{label}</Text>
        <Animated.View style={[styles.switchContainer]}>
          <Animated.View
            style={[
              styles.switchCircle,
              { transform: [{ translateX }] },
              { backgroundColor },
            ]}
          />
        </Animated.View>
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(ToggleSwitch);
