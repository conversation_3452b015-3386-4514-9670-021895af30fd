import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: dark.colors.text,
  },
  switchContainer: {
    justifyContent: 'center',
    width: 36,
    height: 20,
    borderWidth: 0.5,
    backgroundColor: dark.colors.tertiary,
    borderRadius: 10,
    borderColor: withOpacity(dark.colors.text, 0.4),
  },
  switchCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: dark.colors.text,
  },
});

export default styles;
