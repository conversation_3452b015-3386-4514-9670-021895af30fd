import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 1,
    height: 88,
    width: '100%',
    alignSelf: 'center',
    backgroundColor: dark.colors.tertiary,
  },
  valueContainer: {
    backgroundColor: dark.colors.background,
    borderRadius: 11,
    height: 52,
    maxHeight: 52,
    alignItems: 'center',
    justifyContent: 'center',
  },
  metricLabelContainer: {
    backgroundColor: 'transparent',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  metricLabel: {
    fontSize: 8,
    lineHeight: 8,
    letterSpacing: 2,
    fontFamily: 'Montserrat-800',
    color: 'white',
    padding: 4,
    textAlign: 'center',
  },
  metricValue: {
    fontSize: 14,
    lineHeight: 17,
    letterSpacing: 1,
    fontFamily: 'Montserrat-600',
    color: 'white',
    textAlign: 'center',
  },
});

export default styles;
