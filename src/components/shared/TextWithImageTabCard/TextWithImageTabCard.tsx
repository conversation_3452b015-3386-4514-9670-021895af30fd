import React, { useState } from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import styles from './TextWithImageTabCard.style';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import Svg, { Defs, RadialGradient, Rect, Stop } from 'react-native-svg';
import dark from '@/src/core/constants/themes/dark';
import { TextWithImageTabCardProps } from './types';

const TextWithImageTabCard = (props: TextWithImageTabCardProps) => {
  const [cardWidth, setCardWidth] = useState(0);

  const { isMobile } = useMediaQuery();

  const handleLayout = (event: any) => {
    const { width } = event.nativeEvent.layout;
    setCardWidth(width);
  };

  return (
    <Pressable
      onPress={props.onPress}
      style={[
        styles.cardContainer,
        props.isActiveTab && { position: 'relative' },
      ]}
      onLayout={handleLayout}
    >
      {props.isActiveTab && (
        <View
          style={[styles.gradientContainer, { bottom: isMobile ? -30 : -90 }]}
        >
          <Svg
            height="100%"
            width={cardWidth * 2}
            style={[
              styles.gradientBackground,
              { left: -((cardWidth * 2) / 4) },
            ]}
          >
            <Defs>
              <RadialGradient
                id="grad"
                cx="50%"
                cy={isMobile ? '90%' : '150%'}
                rx="40%"
                ry="40%"
                gradientUnits="userSpaceOnUse"
              >
                <Stop
                  offset="0"
                  stopColor={dark.colors.puzzle.primary}
                  stopOpacity="0.6"
                />
                <Stop
                  offset="0.3"
                  stopColor={dark.colors.puzzle.primary}
                  stopOpacity="0.3"
                />
                <Stop
                  offset="0.5"
                  stopColor={dark.colors.puzzle.primary}
                  stopOpacity="0.1"
                />
                <Stop
                  offset="0.7"
                  stopColor={dark.colors.tabButtonGradient}
                  stopOpacity="0"
                />
              </RadialGradient>
            </Defs>
            <Rect x="0" y="0" width="100%" height="100%" fill="url(#grad)" />
          </Svg>
        </View>
      )}
      <View style={styles.contentContainer}>
        {props.renderIcon ? (
          props.renderIcon()
        ) : (
          <Image
            source={props.isActiveTab ? props.activeIcon : props.inActiveIcon}
            style={styles.icon}
          />
        )}
        <Text
          style={[
            styles.text,
            props.isActiveTab && { color: props.activeColor },
            !props.isActiveTab && { color: props.inactiveColor },
          ]}
        >
          {props.label}
        </Text>
      </View>
    </Pressable>
  );
};

export default React.memo(TextWithImageTabCard);
