import React from 'react';
import { Circle, G, Path } from 'react-native-svg';
import { DataSet, ProcessedDataPoint, ProcessedDataSet } from '../types';

const DEFAULT_POINT_RADIUS = 4;

interface LinesAndPointsProps {
  processedDataSets: ProcessedDataSet[];
  handlePointInteraction: (
    dataSet: DataSet,
    point: ProcessedDataPoint,
    seriesIndex: number,
    pointIndex: number,
  ) => void;
}

export const LinesAndPoints: React.FC<LinesAndPointsProps> = ({
  processedDataSets,
  handlePointInteraction,
}) => (
  <>
    {processedDataSets.map((dataSet, seriesIndex) => {
      const pathDefinition = dataSet.data
        .map((p, i) => `${i === 0 ? 'M' : 'L'} ${p.screenX},${p.screenY}`)
        .join(' ');
      return (
        <G key={`dataset-${dataSet.name}-${seriesIndex}`}>
          <Path
            d={pathDefinition}
            fill="none"
            stroke={dataSet.color}
            strokeWidth={dataSet.strokeWidth || 2}
          />
          {dataSet.showPoints !== false &&
            dataSet.data.map((p, pointIndex) => (
              <Circle
                key={`point-${dataSet.name}-${pointIndex}`}
                cx={p.screenX}
                cy={p.screenY}
                r={dataSet.pointRadius || DEFAULT_POINT_RADIUS}
                fill={dataSet.pointColor || dataSet.color}
                onPress={() =>
                  handlePointInteraction(dataSet, p, seriesIndex, pointIndex)
                }
              />
            ))}
        </G>
      );
    })}
  </>
);
