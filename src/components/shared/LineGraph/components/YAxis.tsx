import React from 'react';
import { G, Line, Text as SvgText } from 'react-native-svg';
import dark from 'core/constants/themes/dark';
import _isNaN from 'lodash/isNaN';
import { LineGraphProps } from '../types';

const DEFAULT_AXIS_COLOR = dark.colors.textLight;
const DEFAULT_GRID_COLOR = dark.colors.textLight;
const DEFAULT_TEXT_COLOR = dark.colors.tertiary;

interface YAxisProps {
  yAxisProps?: LineGraphProps['yAxis'];
  padding: { top: number; right: number; bottom: number; left: number };
  actualGraphContentHeight: number;
  yTickValues: number[];
  minY: number;
  maxY: number;
  width: number;
}

export const YAxis: React.FC<YAxisProps> = ({
  yAxisProps,
  padding,
  actualGraphContentHeight,
  yTickValues,
  minY,
  maxY,
  width,
}) => {
  if (!yAxisProps?.show) return null;

  const xPos = padding.left;

  return (
    <G key="y-axis">
      <Line
        x1={xPos}
        y1={padding.top}
        x2={xPos}
        y2={padding.top + actualGraphContentHeight}
        stroke={yAxisProps?.color || DEFAULT_AXIS_COLOR}
        strokeWidth={yAxisProps?.strokeWidth || 1}
      />
      {yTickValues.map((value) => {
        const yPos =
          padding.top +
          actualGraphContentHeight -
          ((value - minY) / (maxY - minY || 1)) * actualGraphContentHeight;
        const label = yAxisProps?.labelFormatter
          ? yAxisProps.labelFormatter(value)
          : typeof value === 'number' && !_isNaN(value)
            ? Number(value.toFixed(1)).toString()
            : '';
        return (
          <G key={`y-tick-${value}`}>
            {yAxisProps?.showGridLines && (
              <Line
                x1={xPos}
                y1={yPos}
                x2={width - padding.right}
                y2={yPos}
                stroke={yAxisProps?.gridLineColor || DEFAULT_GRID_COLOR}
                strokeWidth={yAxisProps?.gridLineStrokeWidth || 0.5}
                strokeDasharray={
                  yAxisProps?.gridLineStrokeDasharray === undefined
                    ? '3,3'
                    : yAxisProps.gridLineStrokeDasharray
                }
              />
            )}
            <SvgText
              x={xPos - 5}
              y={yPos + (yAxisProps?.tickLabelFontSize || 10) / 3}
              fill={yAxisProps?.tickLabelColor || DEFAULT_TEXT_COLOR}
              fontSize={yAxisProps?.tickLabelFontSize || 10}
              textAnchor="end"
              fontFamily="Montserrat-500" // Consider making this configurable
            >
              {label}
            </SvgText>
          </G>
        );
      })}
    </G>
  );
};
