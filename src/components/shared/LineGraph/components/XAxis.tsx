import React from 'react';
import { G, Line, Text as SvgText } from 'react-native-svg';
import dark from 'core/constants/themes/dark';
import { LineGraphProps } from '../types';

const DEFAULT_AXIS_COLOR = dark.colors.textLight;
const DEFAULT_GRID_COLOR = dark.colors.textLight;
const DEFAULT_TEXT_COLOR = dark.colors.tertiary;

interface XAxisProps {
  xAxisProps?: LineGraphProps['xAxis'];
  padding: { top: number; right: number; bottom: number; left: number };
  actualGraphContentHeight: number;
  actualGraphContentWidth: number;
  xTickValues: any[];
  width: number;
}

export const XAxis: React.FC<XAxisProps> = ({
  xAxisProps,
  padding,
  actualGraphContentHeight,
  actualGraphContentWidth,
  xTickValues,
  width,
}) => {
  if (!xAxisProps?.show) return null;

  const yPos = padding.top + actualGraphContentHeight;

  return (
    <G key="x-axis">
      <Line
        x1={padding.left}
        y1={yPos}
        x2={width - padding.right}
        y2={yPos}
        stroke={xAxisProps?.color || DEFAULT_AXIS_COLOR}
        strokeWidth={xAxisProps?.strokeWidth || 1}
      />
      {xTickValues.map((value, index) => {
        const xPos =
          padding.left +
          (index / Math.max(1, xTickValues.length - 1)) *
            actualGraphContentWidth;
        const label = String(value);
        return (
          <G key={`x-tick-${index}`}>
            {xAxisProps?.showGridLines && (
              <Line
                x1={xPos}
                y1={padding.top}
                x2={xPos}
                y2={yPos}
                stroke={xAxisProps?.gridLineColor || DEFAULT_GRID_COLOR}
                strokeWidth={xAxisProps?.gridLineStrokeWidth || 0.5}
                strokeDasharray={
                  xAxisProps?.gridLineStrokeDasharray === undefined
                    ? '3,3'
                    : xAxisProps.gridLineStrokeDasharray
                }
              />
            )}
            <SvgText
              x={xPos}
              y={yPos + (xAxisProps?.tickLabelFontSize || 10) + 7}
              fill={xAxisProps?.tickLabelColor || DEFAULT_TEXT_COLOR}
              fontSize={xAxisProps?.tickLabelFontSize || 10}
              textAnchor="middle"
              alignmentBaseline="hanging"
            >
              {label}
            </SvgText>
          </G>
        );
      })}
    </G>
  );
};
