import { useMemo } from 'react';
import { DataPoint, DataSet, LineGraphProps, ProcessedDataSet } from '../types';

interface UseProcessedGraphDataProps {
  dataSets: DataSet[] | undefined;
  padding: { top: number; right: number; bottom: number; left: number };
  xAxisProps?: LineGraphProps['xAxis'];
  yAxisProps?: LineGraphProps['yAxis'];
  actualGraphContentWidth: number;
  actualGraphContentHeight: number;
}

export const useProcessedGraphData = ({
  dataSets,
  padding,
  xAxisProps,
  yAxisProps,
  actualGraphContentWidth,
  actualGraphContentHeight,
}: UseProcessedGraphDataProps) =>
  useMemo(() => {
    if (!dataSets || dataSets.length === 0) {
      return {
        processedDataSets: [],
        allXValues: [],
        minX: 0,
        maxX: 0,
        minY: 0,
        maxY: 0,
        xTickValues: [],
        yTickValues: [],
      };
    }

    let globalMinY = yAxisProps?.min ?? Infinity;
    let globalMaxY = yAxisProps?.max ?? -Infinity;

    const finalXTickValues: any[] = [];
    const xValuesSeen = new Set<any>();

    dataSets.forEach((dataSet) => {
      dataSet.data.forEach((point) => {
        if (point.y < globalMinY) globalMinY = point.y;
        if (point.y > globalMaxY) globalMaxY = point.y;

        const xVal = xAxisProps?.labelExtractor
          ? xAxisProps.labelExtractor(point)
          : point.x;
        if (!xValuesSeen.has(xVal)) {
          finalXTickValues.push(xVal);
          xValuesSeen.add(xVal);
        }
      });
    });

    if (globalMinY === Infinity) globalMinY = 0;
    if (globalMaxY === -Infinity) globalMaxY = 10;

    if (globalMinY === globalMaxY) {
      globalMinY -= globalMinY === 0 ? 1 : Math.abs(globalMinY * 0.1);
      globalMaxY += globalMaxY === 0 ? 1 : Math.abs(globalMaxY * 0.1);
    }
    if (globalMinY > globalMaxY) {
      [globalMinY, globalMaxY] = [globalMaxY, globalMinY];
    }

    const xMinVal = 0;
    const xMaxVal =
      finalXTickValues.length > 0 ? finalXTickValues.length - 1 : 0;

    const getScreenX = (xValue: any) => {
      const xLabel = xAxisProps?.labelExtractor
        ? xAxisProps.labelExtractor({ x: xValue, y: 0 } as DataPoint)
        : xValue;
      const xIndex = finalXTickValues.indexOf(xLabel);
      if (xIndex === -1) {
        return padding.left;
      }
      return (
        padding.left +
        (xIndex / Math.max(1, finalXTickValues.length - 1)) *
          actualGraphContentWidth
      );
    };

    const getScreenY = (yValue: number) =>
      padding.top +
      actualGraphContentHeight -
      ((yValue - globalMinY) / (globalMaxY - globalMinY || 1)) *
        actualGraphContentHeight;

    const pDataSets: ProcessedDataSet[] = dataSets.map((dataSet) => {
      const sortedData = [...dataSet.data].sort((a, b) => {
        const xValA = xAxisProps?.labelExtractor
          ? xAxisProps.labelExtractor(a)
          : a.x;
        const xValB = xAxisProps?.labelExtractor
          ? xAxisProps.labelExtractor(b)
          : b.x;
        const indexA = finalXTickValues.indexOf(xValA);
        const indexB = finalXTickValues.indexOf(xValB);
        return (
          (indexA === -1 ? Infinity : indexA) -
          (indexB === -1 ? Infinity : indexB)
        );
      });

      return {
        ...dataSet,
        data: sortedData
          .filter((point) => {
            const xVal = xAxisProps?.labelExtractor
              ? xAxisProps.labelExtractor(point)
              : point.x;
            return finalXTickValues.includes(xVal);
          })
          .map((point) => ({
            ...point,
            screenX: getScreenX(point.x),
            screenY: getScreenY(point.y),
          })),
      };
    });

    const yTicks: number[] = [];
    const numYTicksActual = Math.max(1, yAxisProps?.numberOfTicks || 4);

    if (globalMaxY > globalMinY) {
      if (numYTicksActual === 1) {
        yTicks.push((globalMinY + globalMaxY) / 2);
      } else {
        const yTickInterval = (globalMaxY - globalMinY) / (numYTicksActual - 1);
        for (let i = 0; i < numYTicksActual; i++) {
          yTicks.push(globalMinY + i * yTickInterval);
        }
      }
    } else {
      yTicks.push(globalMinY);
    }

    const finalYTicks =
      yAxisProps?.min !== undefined && yAxisProps?.max !== undefined
        ? yTicks.filter(
            (tick) => tick >= yAxisProps!.min! && tick <= yAxisProps!.max!,
          )
        : yTicks;
    if (finalYTicks.length === 0 && yTicks.length > 0) {
      finalYTicks.push(yTicks[Math.floor(yTicks.length / 2)]);
    }

    return {
      processedDataSets: pDataSets,
      allXValues: finalXTickValues,
      minX: xMinVal,
      maxX: xMaxVal,
      minY: globalMinY,
      maxY: globalMaxY,
      xTickValues: finalXTickValues,
      yTickValues:
        finalYTicks.length > 0 ? finalYTicks : [globalMinY, globalMaxY],
    };
  }, [
    dataSets,
    padding,
    xAxisProps,
    yAxisProps,
    actualGraphContentWidth,
    actualGraphContentHeight,
  ]);
