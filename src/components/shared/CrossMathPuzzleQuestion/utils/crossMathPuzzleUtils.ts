import { getDateStringFromTimestamp } from 'core/utils/general';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { format } from 'date-fns';
import _get from 'lodash/get';
import uuid from 'react-native-uuid';
import { cellType, footerType } from '../types/crossMathCellType';

export function evaluateExpression(arr) {
  // First handle multiplication and division (BODMAS Rule)
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '×' || arr[i] === '÷') {
      const num1 = arr[i - 1];
      const operator = arr[i];
      const num2 = arr[i + 1];

      let result;
      if (operator === '×') {
        result = num1 * num2;
      } else if (operator === '÷') {
        result = num1 / num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // Now handle addition and subtraction
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '+' || arr[i] === '-') {
      const num1 = arr[i - 1];
      const operator = arr[i];
      const num2 = arr[i + 1];

      let result;
      if (operator === '+') {
        result = num1 + num2;
      } else if (operator === '-') {
        result = num1 - num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // The final result should be the only value left in the array
  return arr[0];
}

export const getFooterItemFromCell = (cell: cellType): footerType => ({
  id: cell.id,
  value: cell.value,
  type: cell.type,
  isFilled: true,
});

export const checkIsValidDate = (date) => {
  const currentDate = getDateStringFromTimestamp(getCurrentTimeWithOffset());
  const FirststJan2000Date = '2000-01-01';

  const isFutureDate = date > currentDate;
  const isBefore1Jan2000Date = date < FirststJan2000Date;

  return !isFutureDate && !isBefore1Jan2000Date;
};

export const getDefaultPuzzleDate = () => {
  const today = new Date(getCurrentTimeWithOffset());
  return format(today, 'yyyy-MM-dd');
};

export const getPuzzleAnswerArrayFromGrid = (cells: any): cellType[] => {
  const result = [] as cellType[];
  let position = 0;

  for (let row = 0; row < cells.length; row++) {
    for (let col = 0; col < cells[row].length; col++) {
      const cell = cells[row][col];
      const isVisible = _get(cell, 'isVisible', true);
      result.push({
        footerId: null,
        isVisible,
        type: _get(cell, 'type'),
        id: uuid.v4().toString(),
        pos: position,
        value: cell.value,
        editable: !isVisible,
      });
      position++;
    }
  }

  return result;
};

const getPuzzleArrayFromGrid = (cells: any): cellType[] => {
  const result = [] as cellType[];
  let position = 0;

  for (let row = 0; row < cells.length; row++) {
    for (let col = 0; col < cells[row].length; col++) {
      const cell = cells[row][col];
      const isVisible = _get(cell, 'isVisible', true);
      result.push({
        footerId: null,
        isVisible,
        type: _get(cell, 'type'),
        id: uuid.v4().toString(),
        pos: position,
        value: isVisible ? cell.value : '',
        editable: !isVisible,
      });
      position++;
    }
  }

  return result;
};

export default getPuzzleArrayFromGrid;
