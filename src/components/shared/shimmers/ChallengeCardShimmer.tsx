import React from 'react';
import { View } from 'react-native';
import ShimmerView from '../../molecules/ShimmerView';
import Dark from 'core/constants/themes/dark';
import styles from './ChallengeCardShimmer.style';

const ChallengeCardShimmer = () => {
  return (
    <View style={styles.container}>
      <ShimmerView
        style={styles.imageContainer}
        shimmerColors={Dark.colors.placeholderShimmerColors}
      />

      <View style={styles.textContainer}>
        <ShimmerView
          style={styles.titleLine}
          shimmerColors={Dark.colors.placeholderShimmerColors}
        />
        <ShimmerView
          style={styles.subtitleLine}
          shimmerColors={Dark.colors.placeholderShimmerColors}
        />
      </View>
    </View>
  );
};

export default React.memo(ChallengeCardShimmer);
