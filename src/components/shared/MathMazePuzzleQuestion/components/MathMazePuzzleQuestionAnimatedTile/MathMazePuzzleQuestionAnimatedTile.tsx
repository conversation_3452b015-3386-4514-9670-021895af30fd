import React, { useEffect } from 'react';
import { Pressable, LayoutChangeEvent } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  interpolateColor,
} from 'react-native-reanimated';
import { styles } from './MathMazePuzzleQuestionAnimatedTile.style';
import { TILE_CONFIG } from '../../constants/puzzleConstants';
const COLORS = {
  normalText: '#000000',
  selectedText: '#000000',
};

interface AnimatedTileProps {
  row: number;
  col: number;
  cell: string;
  tileSize: number;
  borderColor: string;
  isSelected: boolean;
  borderRadius: number;
  backgroundColor: string;
  onPress: () => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  tileProps?: React.ComponentProps<typeof Pressable>;
}

const MathMazePuzzleQuestionAnimatedTile: React.FC<AnimatedTileProps> = ({
  cell,
  tileSize,
  borderColor,
  isSelected,
  borderRadius,
  backgroundColor,
  onPress,
  onLayout,
  tileProps = {},
}) => {
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const textColorProgress = useSharedValue(0);
  const backgroundColorProgress = useSharedValue(0);

  useEffect(() => {
    if (isSelected) {
      scale.value = withSequence(
        withTiming(1.1, { duration: 200 }),
        withSpring(1.05, { damping: 8, stiffness: 100 }),
      );
      rotation.value = withSequence(
        withTiming(5, { duration: 100 }),
        withTiming(-5, { duration: 100 }),
        withTiming(0, { duration: 100 }),
      );
      textColorProgress.value = withTiming(1, { duration: 300 });
    } else {
      scale.value = withSpring(1, { damping: 8, stiffness: 100 });
      rotation.value = withTiming(0, { duration: 200 });
      textColorProgress.value = withTiming(0, { duration: 300 });
    }
  }, [isSelected, scale, rotation, textColorProgress]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { rotate: `${rotation.value}deg` }],
    backgroundColor: interpolateColor(
      backgroundColorProgress.value,
      [0, 1],
      [backgroundColor, '#FFFFFF'],
    ),
    borderColor: borderColor,
  }));

  const textAnimatedStyle = useAnimatedStyle(() => ({
    color: interpolateColor(
      textColorProgress.value,
      [0, 1],
      [COLORS.normalText, COLORS.selectedText],
    ),
  }));

  return (
    <Pressable 
      onPress={onPress} 
      onLayout={onLayout} 
      style={{zIndex: 1}} 
      hitSlop={{top: 5, bottom: 5, left: 5, right: 5}}
      {...tileProps}
    >
      <Animated.View
        style={[
          styles.tile,
          {
            width: tileSize,
            height: tileSize,
            margin: TILE_CONFIG.MARGIN,
            borderRadius: borderRadius,
          },
          animatedStyle,
        ]}
      >
        <Animated.Text
          style={[styles.text, textAnimatedStyle, { userSelect: 'none' }]}
        >
          {cell}
        </Animated.Text>
      </Animated.View>
    </Pressable>
  );
};

export default MathMazePuzzleQuestionAnimatedTile;
