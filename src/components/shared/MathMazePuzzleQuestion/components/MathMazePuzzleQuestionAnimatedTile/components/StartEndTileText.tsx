import { View, Text } from 'react-native';
import getStyles from './StartEndTileText.style';

interface StartEndTileTextProps {
  isStart?: boolean;
  isEnd?: boolean;
  tileSize: number;
}

const StartEndTileText: React.FC<StartEndTileTextProps> = ({
  isStart,
  isEnd,
  tileSize,
}) => {
  if (!isStart && !isEnd) {
    return null;
  }

  const styles = getStyles(tileSize, isStart ?? false);
  const text = isStart ? 'START' : 'END';

  return (
    <View style={styles.container}>
      <View style={[styles.buttonBorderBackground]} />
      <View style={[styles.button]}>
        <View style={[styles.buttonContent]}>
          <Text style={[styles.text]}>{text}</Text>
        </View>
      </View>
      <View style={styles.triangleBorderBackground} />
      <View style={styles.triangle} />
    </View>
  );
};

export default StartEndTileText;
