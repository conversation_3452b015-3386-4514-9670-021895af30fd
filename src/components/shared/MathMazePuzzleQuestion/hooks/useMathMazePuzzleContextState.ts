import { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions } from 'react-native';
import _toNumber from 'lodash/toNumber';
import _isNaN from 'lodash/isNaN';
import { MathMazePuzzleContextValue, MathMazePuzzleState } from '../context';
import {
  HistoryEntry,
  MathMazePuzzleQuestionProps,
  PathConnection,
  PuzzleGrid,
} from '../types';
import {
  DEFAULT_GRID_SIZE,
  GRID_SIZES,
  isAdjacent,
  isNumber,
  OPERATORS,
  TILE_CONFIG,
} from '../constants/puzzleConstants';

function generatePuzzle(size: number): PuzzleGrid {
  const grid: string[][] = Array.from({ length: size }, () =>
    Array(size).fill(''),
  );
  const solutionPath: [number, number][] = [];

  let r = 0;
  let c = 0;
  const maxIterations = size * size * 4;
  let iterations = 0;

  while (r < size - 1 || c < size - 1) {
    if (iterations >= maxIterations) {
      console.error('Puzzle generation exceeded max iterations, regenerating.');
      return generatePuzzle(size);
    }
    solutionPath.push([r, c]);
    if (r === size - 1) c++;
    else if (c === size - 1) r++;
    else Math.random() < 0.5 ? r++ : c++;
    iterations++;
  }
  solutionPath.push([r, c]);

  let expr = '';
  let current = Math.floor(Math.random() * 9) + 1;
  grid[solutionPath[0][0]][solutionPath[0][1]] = String(current);
  expr += current;

  for (let i = 1; i < solutionPath.length; i++) {
    const [row, col] = solutionPath[i];
    let value = '';
    if (i % 2 === 1) {
      value = OPERATORS[Math.floor(Math.random() * OPERATORS.length)];
    } else {
      let num = Math.floor(Math.random() * 9) + 1;
      const op = grid[solutionPath[i - 1][0]][solutionPath[i - 1][1]];
      switch (op) {
        case '+':
          if (current + num > 100) num = Math.max(1, 100 - current);
          current += num;
          break;
        case '-':
          if (current - num < 0) num = Math.min(current, 9);
          current -= num;
          break;
        case '*':
          while (current * num > 100) num = Math.floor(Math.random() * 9) + 1;
          current *= num;
          break;
        case '/':
          const divisors = Array.from({ length: 9 }, (_, k) => k + 1).filter(
            (n) => current % n === 0,
          );
          num =
            divisors.length > 0
              ? divisors[Math.floor(Math.random() * divisors.length)]
              : 1;
          current = Math.floor(current / num);
          break;
      }
      value = String(num);
    }
    grid[row][col] = value;
    expr += value;
  }

  const result = current;

  for (let row = 0; row < size; row++) {
    for (let col = 0; col < size; col++) {
      if (!grid[row][col]) {
        const isEvenFill = (row + col) % 2 === 0;
        grid[row][col] = isEvenFill
          ? String(Math.floor(Math.random() * 9) + 1)
          : OPERATORS[Math.floor(Math.random() * OPERATORS.length)];
      }
    }
  }
  return { grid, result, solutionPath };
}

function parsePuzzleData(
  data: string | PuzzleGrid | undefined,
  defaultSize: number,
): PuzzleGrid {
  if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data);
      if (
        parsed &&
        parsed.grid &&
        typeof parsed.result === 'number' &&
        Array.isArray(parsed.solutionPath)
      ) {
        return parsed as PuzzleGrid;
      }
    } catch (e) {
      console.error('Failed to parse puzzleData string:', e);
    }
    return generatePuzzle(defaultSize);
  }
  return data || generatePuzzle(defaultSize);
}

function evaluateLeftToRight(expr: string): number {
  const tokens = expr.split(/([+\-*/])/).filter((token) => token !== '');
  if (tokens.length === 0) return 0;

  let result = parseFloat(tokens[0]);
  if (_isNaN(result)) return 0;

  for (let i = 1; i < tokens.length; i += 2) {
    if (i + 1 >= tokens.length) break;
    const operator = tokens[i];
    const nextNumStr = tokens[i + 1];
    const nextNum = parseFloat(nextNumStr);

    if (_isNaN(nextNum)) break;

    switch (operator) {
      case '+':
        result += nextNum;
        break;
      case '-':
        result -= nextNum;
        break;
      case '*':
        result *= nextNum;
        break;
      case '/':
        if (nextNum === 0) {
          console.error('Division by zero attempted');
          return 'INVALID';
        }
        result = Math.floor(result / nextNum);
        break;
    }
  }
  return result;
}

interface UseMathMazePuzzleContextStateProps
  extends Pick<
    MathMazePuzzleQuestionProps,
    'initialGridSize' | 'puzzleData' | 'onSubmit' | 'onWin' | 'onLose'
  > {}

const useMathMazePuzzleContextState = (
  props: UseMathMazePuzzleContextStateProps,
): MathMazePuzzleContextValue => {
  const {
    initialGridSize = DEFAULT_GRID_SIZE,
    puzzleData,
    onSubmit,
    onWin,
    onLose,
  } = props;

  const [gridSize, setGridSize] = useState<number>(initialGridSize);
  const [puzzle, setPuzzle] = useState<PuzzleGrid>(() =>
    parsePuzzleData(puzzleData, gridSize),
  );
  const [result, setResult] = useState<number | null>(null);
  const [reachedEnd, setReachedEnd] = useState<boolean>(false);
  const [path, setPath] = useState<string[]>([]);
  const [expression, setExpression] = useState<string>('');
  const [tileSize, setTileSize] = useState<number>(() =>
    Math.min(
      Math.floor(
        Dimensions.get('window').width /
          (gridSize + TILE_CONFIG.SPACING_DIVISOR),
      ),
      TILE_CONFIG.MAX_SIZE,
    ),
  );
  const startTime = useRef(getCurrentTime());
  const [target, setTarget] = useState<number>(puzzle.result);
  const [seconds, setSeconds] = useState<number>(0);

  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [pathConnections, setPathConnections] = useState<PathConnection[]>([]);

  const history = useRef<HistoryEntry[]>([{ path: [], expression: '' }]);
  const [historyIndex, setHistoryIndex] = useState<number>(0);
  const hasSubmitted = useRef(false);

  useEffect(() => {
    const { width } = Dimensions.get('window');
    setTileSize(
      Math.min(
        Math.floor(width / (gridSize + TILE_CONFIG.SPACING_DIVISOR)),
        TILE_CONFIG.MAX_SIZE,
      ),
    );
  }, [gridSize]);

  useEffect(() => {
    const interval = setInterval(() => setSeconds((s) => s + 1), 1000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = useCallback((totalSeconds: number): string => {
    const minutes = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const internalResetPath = useCallback(() => {
    setPath([]);
    setExpression('');
    setPathConnections([]);
    setReachedEnd(false);
    history.current = [{ path: [], expression: '' }];
    setHistoryIndex(0);
    hasSubmitted.current = false;
  }, []);

  const internalGenerateNewPuzzle = useCallback(
    (newSize: number = gridSize) => {
      const newPuzzle = generatePuzzle(newSize);
      setPuzzle(newPuzzle);
      setTarget(newPuzzle.result);
      setGridSize(newSize);
      internalResetPath();
      setSeconds(0);
      hasSubmitted.current = false;
    },
    [gridSize, internalResetPath],
  );

  const internalHandleSizeChange = useCallback(
    (newSize: number) => {
      if (GRID_SIZES.includes(newSize)) {
        internalGenerateNewPuzzle(newSize);
      }
    },
    [internalGenerateNewPuzzle],
  );

  const internalAddToPath = useCallback(
    (row: number, col: number): boolean => {
      const key = `${row}-${col}`;
      if (
        !puzzle ||
        !puzzle.grid ||
        !puzzle.grid[row] ||
        !puzzle.grid[row][col]
      )
        return false;
      const val = puzzle.grid[row][col];

      if (path.length === 0) {
        if (row !== 0 || col !== 0) return false;
        if (!isNumber(val)) return false;

        const newPath = [key];
        const newExpr = val;
        setPath(newPath);
        setExpression(newExpr);
        setPathConnections([]);

        const newHistoryEntry = { path: newPath, expression: newExpr };
        const baseHistory = history.current.slice(0, historyIndex + 1);
        history.current = [...baseHistory, newHistoryEntry];
        setHistoryIndex(baseHistory.length);
        return true;
      }
      const [prevRow, prevCol] = path[path.length - 1].split('-').map(Number);
      if (prevRow === gridSize - 1 && prevCol === gridSize - 1) return false;
      if (path.includes(key)) return false;

      const prevVal = puzzle.grid[prevRow][prevCol];
      if (!isAdjacent(prevRow, prevCol, row, col)) return false;
      if (isNumber(prevVal) === isNumber(val)) return false;

      const newPath = [...path, key];
      const newExpr = expression + val;
      const newConnections = [
        ...pathConnections,
        {
          from: [prevRow, prevCol] as [number, number],
          to: [row, col] as [number, number],
        },
      ];

      setPath(newPath);
      setExpression(newExpr);
      setPathConnections(newConnections);

      const newHistoryEntry = { path: newPath, expression: newExpr };
      const updatedHistory = history.current.slice(0, historyIndex + 1);
      updatedHistory.push(newHistoryEntry);
      history.current = updatedHistory;
      setHistoryIndex(updatedHistory.length - 1);

      if (row === gridSize - 1 && col === gridSize - 1) {
        const exprToEval = newExpr.replace(/−/g, '-');
        try {
          const result = evaluateLeftToRight(exprToEval);
          setResult(result);
          setReachedEnd(true);
          const outcome = {
            timeSpent: _toNumber(getCurrentTime() - startTime.current),
            expression: newExpr,
            path: newPath,
          };
          if (!hasSubmitted.current) {
            hasSubmitted.current = true;
            if (result === target) {
              setTimeout(() => {
                if (onWin) onWin(outcome);
                if (onSubmit) onSubmit(outcome);
              }, 100);
            } else {
              setTimeout(() => {
                if (onLose) onLose(outcome);
              }, 100);
            }
          }
        } catch (e) {
          console.error('Error evaluating expression:', e);
        }
      }
      return true;
    },
    [
      path,
      expression,
      puzzle,
      gridSize,
      target,
      seconds,
      historyIndex,
      pathConnections,
      onWin,
      onLose,
      onSubmit,
      formatTime,
    ],
  );

  const internalUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      const stateToRestore = history.current[newIndex];
      setPath(stateToRestore.path);
      setExpression(stateToRestore.expression);
      setHistoryIndex(newIndex);

      const newConnections: PathConnection[] = [];
      for (let i = 1; i < stateToRestore.path.length; i++) {
        const [prevR, prevC] = stateToRestore.path[i - 1]
          .split('-')
          .map(Number);
        const [currR, currC] = stateToRestore.path[i].split('-').map(Number);
        newConnections.push({ from: [prevR, prevC], to: [currR, currC] });
      }
      setPathConnections(newConnections);
      setReachedEnd(false);
    }
  }, [historyIndex]);

  const internalRedo = useCallback(() => {
    if (historyIndex < history.current.length - 1) {
      const newIndex = historyIndex + 1;
      const stateToRestore = history.current[newIndex];
      setPath(stateToRestore.path);
      setExpression(stateToRestore.expression);
      setHistoryIndex(newIndex);

      const newConnections: PathConnection[] = [];
      for (let i = 1; i < stateToRestore.path.length; i++) {
        const [prevR, prevC] = stateToRestore.path[i - 1]
          .split('-')
          .map(Number);
        const [currR, currC] = stateToRestore.path[i].split('-').map(Number);
        newConnections.push({ from: [prevR, prevC], to: [currR, currC] });
      }
      setPathConnections(newConnections);
    }
  }, [historyIndex]);

  const internalAutoSolve = useCallback(() => {
    if (!puzzle || !puzzle.solutionPath || !puzzle.grid) return;
    const solutionPathKeys = puzzle.solutionPath.map(([r, c]) => `${r}-${c}`);
    let solutionExpr = '';
    puzzle.solutionPath.forEach(([r, c]) => {
      if (puzzle.grid[r] && puzzle.grid[r][c]) {
        solutionExpr += puzzle.grid[r][c];
      }
    });

    const solutionConnections: PathConnection[] = [];
    for (let i = 1; i < puzzle.solutionPath.length; i++) {
      solutionConnections.push({
        from: puzzle.solutionPath[i - 1],
        to: puzzle.solutionPath[i],
      });
    }

    setPath(solutionPathKeys);
    setExpression(solutionExpr);
    setPathConnections(solutionConnections);

    const newHistoryEntry = {
      path: solutionPathKeys,
      expression: solutionExpr,
    };
    const baseHistory = history.current.slice(0, historyIndex + 1);
    history.current = [...baseHistory, newHistoryEntry];
    setHistoryIndex(baseHistory.length);
  }, [puzzle, target, historyIndex]);

  const onAction = useCallback(
    (action: { type: string; payload?: any }) => {
      switch (action.type) {
        case 'ADD_TO_PATH':
          if (
            action.payload &&
            typeof action.payload.row === 'number' &&
            typeof action.payload.col === 'number'
          ) {
            internalAddToPath(action.payload.row, action.payload.col);
          }
          break;
        case 'RESET_PATH':
          internalResetPath();
          break;
        case 'GENERATE_NEW_PUZZLE':
          internalGenerateNewPuzzle(action.payload?.newSize);
          break;
        case 'CHANGE_GRID_SIZE':
          if (action.payload && typeof action.payload.newSize === 'number') {
            internalHandleSizeChange(action.payload.newSize);
          }
          break;
        case 'UNDO':
          internalUndo();
          break;
        case 'REDO':
          internalRedo();
          break;
        case 'AUTO_SOLVE':
          internalAutoSolve();
          break;
        case 'SET_IS_DRAGGING':
          if (typeof action.payload?.isDragging === 'boolean') {
            setIsDragging(action.payload.isDragging);
          }
          break;
        case 'SET_RESULT':
          if (typeof action.payload?.result === 'number') {
            setResult(action.payload.result);
          }
          break;
        default:
          console.warn('Unhandled action type:', action.type);
      }
    },
    [
      internalAddToPath,
      internalResetPath,
      internalGenerateNewPuzzle,
      internalHandleSizeChange,
      internalUndo,
      internalRedo,
      internalAutoSolve,
    ],
  );

  const state: MathMazePuzzleState = {
    gridSize,
    puzzle,
    path,
    expression,
    tileSize,
    target,
    seconds,
    result,
    reachedEnd,
    startTime: startTime.current,
    isDragging,
    pathConnections,
    history: history.current,
    historyIndex,
  };

  return { state, onAction };
};

export default useMathMazePuzzleContextState;
