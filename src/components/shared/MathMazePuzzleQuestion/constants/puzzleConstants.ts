export const TILE_CONFIG = {
  SPACING_DIVISOR: 6,
  MAX_SIZE: 40,
  LINE_WIDTH_MULTIPLIER: 0.6,
  CIRCLE_RADIUS_MULTIPLIER: 0.1,
  MARGIN: 7,
};

export const LINE_CONFIG = {
  STROKE_WIDTH_MULTIPLIER: 1,
  OPACITY: 1.0,
  CIRCLE_RADIUS_MULTIPLIER: 0.08,
};

export const OPERATORS = ['+', '-', '*', '/'];
export const GRID_SIZES = [4, 5, 6, 7];

export const DEFAULT_GRID_SIZE = 7;

export const isNumber = (val: string): boolean => /^[0-9]$/.test(val);

export const isAdjacent = (
  r1: number,
  c1: number,
  r2: number,
  c2: number,
): boolean =>
  (Math.abs(r1 - r2) === 1 && c1 === c2) ||
  (Math.abs(c1 - c2) === 1 && r1 === r2);
