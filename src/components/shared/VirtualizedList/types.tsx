import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';

export interface VirtualizedListProps<T = any> {
  data: T[];
  renderItem: (item: T) => React.ReactElement;
  loadMore?: () => void;
  hasMore?: boolean;
  keyExtractor?: (item: T, index: number) => string;
  ListFooterComponent?: React.ReactElement;
  ListHeaderComponent?: React.ReactElement;
  inverted?: boolean;
  contentContainerStyle?: StyleProp<ViewStyle>;
  estimatedItemSize?: number;
  onVisibilityChanged?: (isVisible: boolean) => void;
  initialScrollIndex?: number;
}