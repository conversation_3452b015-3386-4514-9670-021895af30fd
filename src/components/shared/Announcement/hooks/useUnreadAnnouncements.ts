import { useQuery } from '@apollo/client';
import { GET_UNREAD_ANNOUNCEMENTS_QUERY } from 'core/graphql/fragments/announcement';
import { Announcement } from 'core/types/announcement';

interface UseUnreadAnnouncementsProps {
  limit?: number;
  offset?: number;
}

interface UnreadAnnouncementsResponse {
  getUnreadAnnouncements: Announcement[];
}

const useUnreadAnnouncements = ({
  limit = 1,
  offset = 0,
}: UseUnreadAnnouncementsProps = {}) => {
  const { data, loading, error, refetch, fetchMore } =
    useQuery<UnreadAnnouncementsResponse>(GET_UNREAD_ANNOUNCEMENTS_QUERY, {
      variables: { limit, offset },
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    });

  const loadMore = async (newOffset: number) =>
    fetchMore({
      variables: {
        offset: newOffset,
        limit,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          getUnreadAnnouncements: [
            ...prev.getUnreadAnnouncements,
            ...fetchMoreResult.getUnreadAnnouncements,
          ],
        };
      },
    });

  return {
    announcements: data?.getUnreadAnnouncements || EMPTY_ARRAY,
    loading,
    error,
    refetch,
    loadMore,
  };
};

export default useUnreadAnnouncements;
