import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    // backgroundColor: dark.colors.background,
    padding: 16,
    margin: 0,
    shadowColor: '#000',
    backgroundColor: dark.colors.background,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxWidth: 500,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  date: {
    fontSize: 12,
    color: '#6B7280',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
    color: 'white',
  },
  description: {
    fontSize: 14,
    color: dark.colors.textDark,
    marginBottom: 12,
    lineHeight: 20,
  },
  image: {
    width: '100%',
    height: 180,
    borderRadius: 8,
    marginBottom: 12,
  },
  ctaContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 16,
    height: 48,
    gap: 8,
  },
  ctaButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ctaButtonPrimary: {
    backgroundColor: '#3B82F6',
  },
  ctaButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  ctaButtonDefault: {
    backgroundColor: '#E5E7EB',
  },
  ctaText: {
    fontWeight: '600',
    fontSize: 14,
  },
  ctaTextPrimary: {
    color: '#fff',
  },
  ctaTextSecondary: {
    color: '#3B82F6',
  },
  ctaTextDefault: {
    color: '#374151',
  },
  expiry: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 12,
    fontStyle: 'italic',
  },
});

export default styles;
