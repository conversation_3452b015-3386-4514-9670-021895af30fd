import React from 'react'
import { View, TouchableOpacity } from 'react-native'
import { Text, Overlay } from '@rneui/themed'
import { AlertBoxProps } from './types'
import styles from './AlertBox.style'

const AlertBox = ({
    isVisible,
    toggleOverlay,
    title,
    cancelText,
    confirmText,
    onCancel,
    onConfirm,
    cancelButtonColor,
    confirmButtonColor,
    children,
}: AlertBoxProps) => {
    return (
        <Overlay
            isVisible={isVisible}
            onBackdropPress={toggleOverlay}
            overlayStyle={styles.overlay}
        >
            <View style={styles.overlayContent}>
                <Text style={styles.overlayText}>{title}</Text>
                {children}
                <View style={styles.overlayButtons}>
                    <TouchableOpacity
                        style={[
                            styles.buttonCancel,
                            { backgroundColor: cancelButtonColor },
                        ]}
                        onPress={onCancel}
                        testID="alertbox-cancel-button"
                    >
                        <Text style={styles.textStyle}>{cancelText}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[
                            styles.buttonConfirm,
                            { backgroundColor: confirmButtonColor },
                        ]}
                        onPress={onConfirm}
                        testID="alertbox-confirm-button"
                    >
                        <Text style={styles.textStyle}>{confirmText}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Overlay>
    )
}

export default AlertBox
