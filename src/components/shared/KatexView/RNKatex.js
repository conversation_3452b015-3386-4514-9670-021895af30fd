import React from 'react';
import { StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';

import katexStyle from './katex-style';
import katexScript from './katex-script';

function getContent({ inlineStyle, expressionStyle, expression, ...options }) {
  return `<!DOCTYPE html>
            <html>
                <head>
                    <style>
                    ${katexStyle}
                    ${inlineStyle}
                    body {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    #katex {
                        font-size: 24px;
                        padding: 10px;
                        color: white;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        ${expressionStyle}
                    }
                    </style>
                    <script>
                        window.onerror = e => document.write(e);
                        ${katexScript}
                    </script>
                </head>
                <body>
                <div id="katex" class="katex">
                </div>
                  <script>
                    document.getElementById('katex').innerHTML = katex.renderToString(${JSON.stringify(expression)}, ${JSON.stringify(options)});
                  </script>
                </body>
            </html>
            `;
}

const defaultStyle = StyleSheet.create({
  root: {
    height: 40,
  },
});

const defaultInlineStyle = `
html, body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: 0;
  padding: 0;
}
.katex {
  margin: 0;
  display: flex;
}
`;

export default function Katex({
  expression,
  displayMode,
  output,
  leqno,
  fleqn,
  throwOnError,
  errorColor,
  macros,
  minRuleThickness,
  colorIsTextColor,
  maxSize,
  maxExpand,
  strict,
  trust,
  expressionStyle,
  globalGroup,
  ...webViewProps
}) {
  return (
    <WebView
      {...webViewProps}
      source={{
        html: getContent({
          expression,
          displayMode,
          output,
          leqno,
          fleqn,
          throwOnError,
          errorColor,
          macros,
          minRuleThickness,
          colorIsTextColor,
          maxSize,
          maxExpand,
          strict,
          trust,
          globalGroup,
          expressionStyle,
        }),
      }}
    />
  );
}

Katex.defaultProps = {
  expression: '',
  displayMode: false,
  throwOnError: false,
  errorColor: '#f00',
  inlineStyle: defaultInlineStyle,
  style: defaultStyle,
  macros: {},
  colorIsTextColor: false,
};
