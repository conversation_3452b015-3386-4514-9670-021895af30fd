import { StyleSheet } from "react-native";
import dark from "../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        maxHeight: 450,
        flex: 1,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        gap: 44,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal:16
    },
    image: {
        height: 140,
        width: 140,
    },
    infoIconBox: {
        height: 36,
        width: 36,
        borderRadius: 18,
        backgroundColor:dark.colors.cardBackground,
        justifyContent: "center",
        alignItems: 'center'
    },
    infoText: {
        fontFamily: "Montserrat-400",
        lineHeight: 16,
        fontSize: 11,
        color: 'white'
    },
    leadingText: {
        fontFamily: "Montserrat-700",
        lineHeight: 16,
        fontSize: 15,
        color: 'white',
        textAlign:"center"
    },
    infoTextBold: {
        fontFamily: "Montserrat-700",
    },
    infoDetailRow:{
        flexDirection: 'row', 
        gap: 12,
        alignItems:"center"
    }
})

export default styles