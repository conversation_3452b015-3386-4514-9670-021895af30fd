import React from 'react';
import {View} from 'react-native';
import {Text} from '@rneui/themed';
import Dark from '@/src/core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGoBack from 'navigator/hooks/useGoBack';
import _size from 'lodash/size';
import BackButton from 'molecules/BackButton';
import styles from './Header.style';
import {Props} from './types';

const Header: React.FC<Props> = ({
                                     title,
                                     renderTitle: renderTitleFromProps,
                                     isTransparentBg = false,
                                     goBack: goBackFromProps,
                                     renderTrailingComponent,
                                     renderCenterComponent,
                                     showBackInWeb = false,
                                 }) => {
    const {isMobile} = useMediaQuery();

    const {goBack} = useGoBack();

    const renderTitle = () => {
        if (renderTitleFromProps) {
            return renderTitleFromProps();
        }
        if (_size(title) <= 0) return null;
        return <Text style={styles.headerTitle} numberOfLines={1}>{title}</Text>;
    };

    if (!isMobile) {
        if (showBackInWeb) {
            return (
                <View
                    style={{
                        ...styles.header,
                        backgroundColor: isTransparentBg
                            ? 'transparent'
                            : Dark.colors.background,
                    }}
                >
                    <View style={styles.headerLeft}>
                        <BackButton onBackPress={goBackFromProps ?? goBack}/>
                    </View>
                </View>
            );
        }
        return null;
    }

    return (
        <View
            style={{
                ...styles.header,
                backgroundColor: isTransparentBg
                    ? 'transparent'
                    : Dark.colors.background,
            }}
        >
            <View style={styles.headerLeft}>
                <BackButton onBackPress={goBackFromProps ?? goBack}/>
                {renderTitle()}
            </View>
            <View>{renderCenterComponent?.()}</View>
            <View>{renderTrailingComponent?.()}</View>
        </View>
    );
};

export default React.memo(Header);
