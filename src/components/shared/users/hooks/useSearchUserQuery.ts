import { gql, useQuery } from '@apollo/client';

import { USER_PUBLIC_DETAIL_FRAGMENT } from 'core/graphql/fragments/userPublicDetail';

const LEADERBOARD_QUERY = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query Leaderboard(
    $first: Int
    $countryCode: String
    $searchKey: String
    $after: String
  ) {
    leaderboard(
      first: $first
      countryCode: $countryCode
      searchKey: $searchKey
      after: $after
    ) {
      edges {
        cursor
        node {
          ...UserPublicDetailFields
        }
      }
    }
  }
`;

const DEFAULT_PAGE_SIZE = 50;

const useSearchUserQuery = ({
  searchKey,
  countryCode,
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  searchKey: string;
  countryCode: string;
  pageSize?: number;
}) => {
  const { loading, error, data } = useQuery(LEADERBOARD_QUERY, {
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'cache-first',
    variables: {
      searchKey,
      first: pageSize,
    },
  });

  return {
    ...data?.leaderboard,
    loading,
    error,
  };
};

export default useSearchUserQuery;
