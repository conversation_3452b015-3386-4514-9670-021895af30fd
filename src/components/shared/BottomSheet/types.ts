import { StyleProp, ViewStyle } from "react-native";
import { YStackProps } from "tamagui";

export interface BottomSheetProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  snapPoints?: number[];
  styles?: {
    closeButton?: StyleProp<ViewStyle>;
    frame?: YStackProps;
    contentContainer?: YStackProps;
  };
  position?: number;
  onPositionChange?: (position: number) => void;
}