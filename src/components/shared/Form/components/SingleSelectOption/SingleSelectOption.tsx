import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import FormFieldLabel from '@/src/components/shared/Form/components/FormFieldLabel';
import {
  SelectOption,
  SingleSelectOptionProps,
} from '@/src/components/shared/Form/types/formTypes';
import styles from './SingleSelectOption.style';

const SingleSelectOption = (props: SingleSelectOptionProps) => {
  const { field, onSelect, selectedOption } = props ?? EMPTY_OBJECT;
  const { options } = field ?? EMPTY_OBJECT;

  return (
    <View style={styles.mainContainerStyle}>
      <FormFieldLabel field={field} />
      {_map(options, (option: SelectOption) => (
        <TouchableOpacity
          key={option.value}
          style={styles.innerContainerStyle}
          onPress={() => onSelect?.(option.value)}
        >
          <View style={styles.unfilledOptionStyle}>
            {_isEqual(selectedOption, option.value) && (
              <View style={styles.filledCircleStyle} />
            )}
          </View>
          <View>
            <Text style={styles.labelStyle}>{option.label}</Text>
            <Text style={styles.descStyle}>{option.description}</Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default React.memo(SingleSelectOption);
