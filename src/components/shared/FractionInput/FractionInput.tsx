import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  NativeSyntheticEvent,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from 'react-native';
import _toString from 'lodash/toString';
import CustomKeyboard, { KEYBOARD_TYPES } from 'shared/CustomKeyboard';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _split from 'lodash/split';
import styles from './FractionInput.style';
import { FractionInputProps } from './types';

const INPUTS = {
  NUMERATOR: 'NUMERATOR',
  DENOMINATOR: 'DENOMINATOR',
};

const getDestructuredValue = (value: string) => {
  const [numerator = '', denominator = ''] = _split(value, '/');
  return { numerator, denominator };
};

const FractionInput: React.FC<FractionInputProps> = ({
  onChangeText,
  value,
  answer,
  customKeyboardType = KEYBOARD_TYPES.NUMBERS,
}) => {
  const valueRef = useRef(getDestructuredValue(value));

  const numeratorRef = useRef<TextInput>(null);
  const denominatorRef = useRef<TextInput>(null);

  useEffect(() => {
    valueRef.current = getDestructuredValue(value);
    numeratorRef.current?.focus();
  }, [value]);

  const focusedInput = useRef(INPUTS.NUMERATOR);

  const { isMobile } = useMediaQuery();

  // Parse the correct answer
  const correctAnswer = useMemo(() => {
    const [numerator, denominator] = _split(answer, '/');
    return { numerator, denominator };
  }, [answer]);

  const handleValueChange = useCallback(() => {
    onChangeText(
      `${valueRef.current.numerator}/${valueRef.current.denominator}`,
    );
  }, [onChangeText]);

  const handleNumeratorChange = useCallback(
    (numerator: string) => {
      valueRef.current.numerator = numerator;
      handleValueChange();
      // Auto focus to denominator when numerator matches
      if (_toString(numerator) === correctAnswer.numerator) {
        denominatorRef.current?.focus();
        focusedInput.current = INPUTS.DENOMINATOR;
      }
    },
    [correctAnswer, handleValueChange],
  );

  const handleDenominatorChange = useCallback(
    (denominator: string) => {
      valueRef.current.denominator = denominator;

      handleValueChange();
    },
    [handleValueChange],
  );

  const handleKeyPress = useCallback(
    (
      event: NativeSyntheticEvent<TextInputKeyPressEventData>,
      field: 'numerator' | 'denominator',
    ) => {
      const { key } = event.nativeEvent;

      if (key === 'ArrowUp' && field === 'denominator') {
        numeratorRef.current?.focus();
      } else if (key === 'ArrowDown' && field === 'numerator') {
        denominatorRef.current?.focus();
      }
    },
    [],
  );

  const handleCustomKeyPress = useCallback(
    (key: string) => {
      if (key === 'clr') {
        if (focusedInput.current === INPUTS.NUMERATOR) {
          valueRef.current.numerator = '';
        } else if (focusedInput.current === INPUTS.DENOMINATOR) {
          valueRef.current.denominator = '';
        }
        handleValueChange();
      } else if (key === 'space') {
        if (focusedInput.current === INPUTS.NUMERATOR) {
          valueRef.current.numerator += ' ';
        } else if (focusedInput.current === INPUTS.DENOMINATOR) {
          valueRef.current.denominator += ' ';
        }
        handleValueChange();
      } else if (focusedInput.current === INPUTS.NUMERATOR) {
        valueRef.current.numerator += key;
        handleNumeratorChange(valueRef.current.numerator);
      } else if (focusedInput.current === INPUTS.DENOMINATOR) {
        valueRef.current.denominator += key;
        handleDenominatorChange(valueRef.current.denominator);
      }
    },
    [handleValueChange],
  );

  // Initial focus on numerator
  useEffect(() => {
    numeratorRef.current?.focus();
    const timeoutId = setTimeout(() => {
      numeratorRef.current?.focus();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  const handleDelete = useCallback(() => {
    // vibrateFeedback()
    if (focusedInput.current === INPUTS.NUMERATOR) {
      valueRef.current.numerator = valueRef.current.numerator.slice(0, -1);
    } else {
      valueRef.current.denominator = valueRef.current.denominator.slice(0, -1);
    }
    handleValueChange();
  }, [handleValueChange]);

  const customKeyboard = isMobile;

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.fractionContainer}>
          <TextInput
            ref={numeratorRef}
            style={[styles.input]}
            value={valueRef.current.numerator}
            onFocus={() => {
              focusedInput.current = INPUTS.NUMERATOR;
            }}
            onChangeText={handleNumeratorChange}
            keyboardType="numeric"
            maxLength={3}
            textAlign="center"
            editable={!customKeyboard}
            onKeyPress={(e) => handleKeyPress(e, 'numerator')}
          />
          <View style={styles.fractionLine} />
          <TextInput
            ref={denominatorRef}
            style={[styles.input]}
            value={valueRef.current.denominator}
            onFocus={() => {
              focusedInput.current = INPUTS.DENOMINATOR;
            }}
            onChangeText={handleDenominatorChange}
            keyboardType="numeric"
            maxLength={3}
            editable={!customKeyboard}
            textAlign="center"
            onKeyPress={(e) => handleKeyPress(e, 'denominator')}
          />
        </View>
      </View>
      {customKeyboard && (
        <CustomKeyboard
          onKeyPress={handleCustomKeyPress}
          onDelete={handleDelete}
          customKeyboardType={customKeyboardType}
        />
      )}
    </View>
  );
};

export default React.memo(FractionInput);
