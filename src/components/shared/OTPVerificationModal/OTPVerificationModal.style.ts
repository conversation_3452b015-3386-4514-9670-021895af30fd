import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)', 
  },
  modalContent: {
    backgroundColor: dark.colors.card,
    borderRadius: 20, 
    width: '30%', 
    padding: 20,
    gap:20
  },
  disabledButton: {
    opacity: 0.5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 35,
  },
  headerText: {
    fontSize: 16,
    color: 'white',
    fontFamily: 'Montserrat-600',
  },
  closeIcon: {
    color: '#ccc',
    fontSize: 18,
  },
  inputLabelBox: {
    flex: 1,
    display:'flex',
    flexDirection:'row',
    gap:10,
    marginRight: 10,
  },
  inputContainer: {
    marginBottom: 25,
  },
  inputLabel: {
    fontSize: 14,
    color: '#fff', 
    marginBottom: 5,
    fontFamily: 'Montserrat-500',
  },
  inputLabelMandatory: {
    fontSize: 16,
    color: dark.colors.error, 
    marginBottom: 5,
    fontWeight:'700'
  },
  inputField: {
    borderWidth:2,
    borderColor: dark.colors.tertiary, 
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: '#fff', 
    backgroundColor: '#333', 
    outlineStyle: 'none',
  },
  focusedInputField:{
    outlineColor:dark.colors.secondary,
    borderWidth: 1,
    outlineWidth:1,
    borderColor: 'white', 
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: '#fff', 
    backgroundColor: 'transparent', 
  },
  buttonContainer: {
    flexDirection: 'row',
    gap:10,
    marginTop: 20,
    marginLeft:50
  },
  cancelButton: {
    backgroundColor: 'transparent', 
    padding: 12,
    borderRadius: 10,
    flex: 1,
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: dark.colors.secondary, 
    padding: 12,
    borderRadius: 20,
    flex: 1,
    height:40,
    alignItems: 'center',
    justifyContent:"center"
  },
  cancelButtonText: {
    color: dark.colors.secondary,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  submitButtonText: {
    color:dark.colors.card, 
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  errorText:{
    color:dark.colors.errorDark,
    fontFamily:"Montserrat-500",
    fontSize:14
  }
});

export default styles
