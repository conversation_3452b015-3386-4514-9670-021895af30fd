import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  otpContainer: {
    marginBottom: 16,
  },
  actionContainer: {
    alignItems: 'flex-end',
  },
  timerText: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-400',
    fontSize: 12,
  },
  resendButton: {
    padding: 8,
  },
  resendText: {
    color: dark.colors.secondary,
    fontSize: 14,
    fontFamily: 'Montserrat-400',
    textDecorationLine: 'underline',
  },
  verifiedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  verifiedText: {
    color: dark.colors.secondary,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  changeEmailText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    marginTop: 5,
    color: dark.colors.secondary,
  },
  actionsContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
})

export default styles
