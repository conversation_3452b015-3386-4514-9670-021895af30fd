import { StyleSheet } from 'react-native';
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 10,
  },
  statsContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 40,
    borderRadius: 20,
    paddingLeft: 12,
    paddingRight: 18,
    backgroundColor: dark.colors.primary,
  },
  icon: {
    width: 25,
    height: 25,
  },
  statsTextContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginLeft: 10,
    justifyContent: 'center',
  },
  statsTitle: {
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  statsValue: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
});

export default styles;
