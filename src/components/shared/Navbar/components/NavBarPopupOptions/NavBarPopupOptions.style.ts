import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'
import { withOpacity } from '../../../../../core/utils/colorUtils'

const styles = StyleSheet.create({
  modalContent: {
    borderRadius: 10,
    minWidth: 200,
    // alignItems: 'flex-start',
    backgroundColor: dark.colors.tertiary,
    flex: 1,
    width: '100%',
  },
  optionRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    width: '100%',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  profileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    width: '100%',
  },
  optionLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-500',
  },
  hoveredOptionRowContainer: {
    backgroundColor: withOpacity(dark.colors.secondary, 0.05),
    borderRadius: 4,
  },
  optionRowContent: {
    flexDirection: 'row',
    gap: 12,
  },
})

export default styles
