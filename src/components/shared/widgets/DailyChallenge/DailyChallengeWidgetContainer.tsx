import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import DailyChallenge from './DailyChallenge';
import DailyChallengeWithoutGradient from './DailyChallengeWithoutGradient';

const DailyChallengeWidgetContainer = (props) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const DailyChallengeWidgetComponent = isCompactMode
    ? DailyChallengeWithoutGradient
    : DailyChallenge;

  return <DailyChallengeWidgetComponent {...props} />;
};

export default React.memo(DailyChallengeWidgetContainer);
