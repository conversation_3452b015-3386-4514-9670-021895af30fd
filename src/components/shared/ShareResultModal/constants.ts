import { Platform } from 'react-native';
import Share from 'react-native-share';
import dark from 'core/constants/themes/dark';
import { SocialConfig, SocialPlatform } from 'shared/ShareResultModal/types';
import { ICON_TYPES } from 'atoms/Icon';

const FACEBOOK_APP_ID = '1049325993701153';

export const SOCIAL_CONFIGS: Record<SocialPlatform, SocialConfig> = {
  instagram: {
    icon: 'instagram',
    color: dark.colors.instagramGradientColor,
    label: 'Instagram',
    shareOptions: {
      social: Share.Social.INSTAGRAM,
      type: 'image/*',
      appId: FACEBOOK_APP_ID, // Required for iOS
    },
  },
  instagramStory: {
    icon: 'instagram',
    color: dark.colors.instagramGradientColor,
    label: 'Instagram Story',
    iconImage: require('assets/images/instagram-stories.png'),
    shareOptions: {
      social: Share.Social.INSTAGRAM_STORIES,
      appId: FACEBOOK_APP_ID, // Required for iOS
      backgroundBottomColor: dark.colors.earnStreakText[0],
      backgroundTopColor: dark.colors.earnStreakText[1],
      attributionURL: 'https://www.matiks.com/apps', // in beta
    },
  },
  twitter: {
    icon: 'x-twitter',
    color: 'black',
    iconType: ICON_TYPES.FONT_AWESOME_6,
    label: 'X',
    shareOptions: {
      social: Share.Social.TWITTER,
    },
  },
  linkedin: {
    icon: 'linkedin',
    color: '#0077B5',
    label: 'LinkedIn',
    isAllowed: Platform.OS === 'android',
    shareOptions: {
      social: Share.Social.LINKEDIN,
    },
  },
  whatsapp: {
    icon: 'whatsapp',
    color: '#25D366',
    label: 'WhatsApp',
    iconSize: 26,
    shareOptions: {
      social: Share.Social.WHATSAPP,
    },
  },
  facebook: {
    icon: 'facebook',
    color: '#1877F2',
    label: 'Facebook',
    iconSize: 28,
    shareOptions: {
      social: Share.Social.FACEBOOK,
      appId: FACEBOOK_APP_ID, // Required for iOS
    },
  },
};

export const OTHER_PLATFORMS: Record<SocialPlatform, SocialConfig> = {
  download: {
    icon: 'download',
    color: dark.colors.cardBackground,
    iconColor: dark.colors.textDark,
    label: 'Save Result',
    // Special case for download, handled separately
    shareOptions: {},
  },
  more: {
    icon: 'more-horiz',
    iconType: ICON_TYPES.MATERIAL_ICONS,
    iconColor: dark.colors.textDark,
    color: dark.colors.cardBackground,
    label: 'More',
    shareOptions: {},
  },
};
