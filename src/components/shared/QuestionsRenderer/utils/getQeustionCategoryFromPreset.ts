import _isEmpty from 'lodash/isEmpty';
import _split from 'lodash/split';
import _get from 'lodash/get';
import { PRESEST_VS_QUESTION_CATEGORY } from 'core/constants/questionCategories';

const getQuestionCategoryFromPreset = ({
  presetIdentifier,
}: {
  presetIdentifier: string;
}) => {
  if (_isEmpty(presetIdentifier)) return null;

  const preset = _get(_split(presetIdentifier, '_'), [0]);
  return PRESEST_VS_QUESTION_CATEGORY[preset];
};

export default getQuestionCategoryFromPreset;
