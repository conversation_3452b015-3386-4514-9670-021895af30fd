import React from 'react';
import { Platform, StyleSheet, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _get from 'lodash/get';
import KatexView from '../../../KatexView';
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  tagContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    backgroundColor: dark.colors.gradientBackground,
    flexShrink: 1,
  },
  tagText: {
    color: dark.colors.textDark,
  },
});

const NthRootQuestion = (props) => {
  const { question } = props;

  const { isMobile: isCompactMode } = useMediaQuery();

  const { isPerfectPower, nthRoot, roundOffDecimals, expression } = question;
  const base = _get(expression, [0]);

  const tag = `${roundOffDecimals} Decimal places`;

  const fontSize = Platform.OS !== 'web' ? 64 : isCompactMode ? 24 : 36;

  return (
    <View style={{ width: '100%' }}>
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          justifyContent: 'center',
        }}
      >
        <KatexView
          expression={`\\sqrt[${nthRoot}]{${base}}`}
          fontSize={fontSize}
        />
      </View>
      {!isPerfectPower && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            marginTop: 16,
          }}
        >
          <View style={styles.tagContainer}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default React.memo(NthRootQuestion);
