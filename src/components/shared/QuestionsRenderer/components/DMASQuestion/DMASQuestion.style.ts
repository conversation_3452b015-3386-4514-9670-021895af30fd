import { Platform, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isNativeDevice, isCompactMode) =>
  StyleSheet.create({
    questionExpression: {
      fontSize: isCompactMode ? 16 : 24,
      fontFamily: 'Montserrat-500',
      color: 'white',
      textAlign: 'center',
      letterSpacing: 0,
    },
    expressionContainer: {
      gap: 4,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
      maxWidth: 400,
      maxHeight: 400,
      height: '100%',
    },
    expressionRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    operatorContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
    },
    operator: {
      width: 16,
      // maxWidth: 24,
      fontSize: 14,
      color: 'white',
    },
    numberContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
    },
    digitBox: {
      width: 18,
      height: isCompactMode ? 16 : 20,
    },
    digitContainer: {
      flexDirection: 'row',
      gap: 0,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    scrollContainer:{
      flex: 1,
      justifyContent: 'center',
    }
  });

const usePracticeQuestionStyles = () => {
  const { isMobile } = useMediaQuery();
  const isNativeDevice = Platform.OS !== 'web';
  const styles = useMemo(
    () => createStyles(isNativeDevice, isMobile),
    [isNativeDevice, isMobile],
  );

  return styles;
};

export default usePracticeQuestionStyles;
