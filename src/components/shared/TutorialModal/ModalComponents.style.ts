import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 7,
  },
  number: {
    fontSize: 18,
    color: Dark.colors.offWhite,
  },
  inputStyle: {
    flex: 1,
    width: 60,
    fontSize: 18,
    color: Dark.colors.offWhite,
    fontFamily: 'Montserrat-400',
    outlineStyle: 'none',
    borderWidth: 0,
    borderBottomWidth: 2,
    borderBottomColor: Dark.colors.stroke,
    paddingLeft: 10,
  },
  iconStyle: {
    position: 'absolute',
    right: 12,
    top: 12,
  },
  errorContainer: {
    position: 'absolute',
    right: 16,
    top: 8,
  },
  errorText: {
    color: Dark.colors.errorDark,
    fontSize: 20,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
