import React from 'react';
import { View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import DCByCategory from 'modules/dailyChallenge/components/DCByCategory/DCByCategory';
import UserRatingsInfoCard from 'shared/UserRatingsInfoCard';
import DownloadMobileAppSection from 'shared/DownloadMobileAppSection';
import styles from './RightPaneWidget.style';

const RightPaneWidgets = () => {
  const { isMobile } = useMediaQuery();

  if (isMobile) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* <View style={{ flexDirection: 'row', gap: 12 }}> */}
      {/*  <UserStatikCoinsCard /> */}
      {/*  <StreakCard /> */}
      {/* </View> */}
      <UserRatingsInfoCard />
      {/* <ExpandedStreakCard /> */}
      {/* <UserCommitmentTracker /> */}
      <DCByCategory />
      {/* <OnlineUsers /> */}
      <DownloadMobileAppSection />
    </View>
  );
};

export default React.memo(RightPaneWidgets);
