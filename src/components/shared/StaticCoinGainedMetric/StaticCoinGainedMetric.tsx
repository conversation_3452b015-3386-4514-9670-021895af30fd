import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import MetricStatCard from 'shared/MetricStatCard/MetricStatCard';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import useIncreaseNumber from 'core/hooks/useIncreaseNumber';

const StaticCoinGainedMetric = ({ coinsGained }: { coinsGained: number }) => {
  const { user } = useSession();
  const totalPoints = userReader.statikCoins(user);

  const { value, isAnimating } = useIncreaseNumber({
    initialValue: totalPoints,
    finalValue: totalPoints + coinsGained,
    duration: 1000,
  });

  const renderStaticCoinsGained = useCallback(
    () => (
      <View style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
        <Text
          style={{
            color: 'white',
            fontSize: isAnimating ? 16 : 14,
            fontFamily: 'Montserrat-800',
          }}
        >
          {value}
        </Text>
        <Text
          style={{
            color: dark.colors.streakOrangeColor,
            fontSize: 14,
            fontFamily: 'Montserrat-800',
          }}
        >
          +{coinsGained}
        </Text>
      </View>
    ),
    [value, isAnimating, coinsGained],
  );

  return (
    <View style={{ flexDirection: 'row', gap: 8 }}>
      <MetricStatCard
        label="COINS GAINED"
        renderValue={renderStaticCoinsGained}
      />
    </View>
  );
};

export default React.memo(StaticCoinGainedMetric);
