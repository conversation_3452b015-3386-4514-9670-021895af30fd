import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
    height: 100
  },
  contentContainer: {
    backgroundColor: dark.colors.tertiary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 19,
    alignItems: 'center',
    maxWidth: 330,
    width: 330,
    flexDirection: 'row',
  },
  textInfos: {
    flex: 1,
    justifyContent: 'center',
    gap: 9,
  },
  title: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
    letterSpacing: 2,
  },
  message: {
    fontSize: 12,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 81,
    height: 72,
  },
  timer: {
    fontFamily: 'BebasNeue-500',
    fontSize: 24,
    letterSpacing: 1,
    color: dark.colors.textLight,
  },
});

export default styles;
