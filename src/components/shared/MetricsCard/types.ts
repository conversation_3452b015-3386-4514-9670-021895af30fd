import { ImageSourcePropType, ImageStyle, StyleProp, ViewStyle } from "react-native";

export interface MetricsCardProps {
  title?: string;
  value: string | number;
  changeValue?: number;
  imageSource?: ImageSourcePropType;
  containerStyle?: StyleProp<ViewStyle>;
  valueStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<ViewStyle>;
  changeValueStyle?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
}
