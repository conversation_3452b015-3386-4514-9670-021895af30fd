import React, { useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
} from 'react-native';
import styles from './OTPInput.style';
import _isNull from 'lodash/isNull';

interface OTPInputProps {
  length?: number;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  isError?: boolean;
}

const OTPInput: React.FC<OTPInputProps> = ({
  length = 4,
  value = '',
  onChange,
  disabled = false,
  isError = false,
}) => {
  const inputRefs = useRef<TextInput[]>([]);
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  const handleChange = (text: string, index: number) => {
    const newValue = value.split('');
    newValue[index] = text;
    const finalValue = newValue.join('');
    onChange?.(finalValue);
    if (text && index < length - 1) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handleKeyPress = (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
    index: number,
  ) => {
    if (e.nativeEvent.key === 'Backspace' && !value[index] && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  return (
    <View style={{ gap: 6 }}>
      <Text style={styles.label}>OTP</Text>
      <View style={styles.container}>
        {[...Array(length)].map((_, index) => (
          <TextInput
            key={index}
            ref={(ref) => {
              if (!ref) return;
              inputRefs.current[index] = ref;
            }}
            style={[
              styles.input,
              disabled && styles.disabledInput,
              isError && styles.errorInput,
              !_isNull(value[index]) && styles.filledInput,
            ]}
            maxLength={1}
            keyboardType="numeric"
            value={value[index] || ''}
            onChangeText={(text) => handleChange(text, index)}
            onKeyPress={(e) => handleKeyPress(e, index)}
            editable={!disabled}
          />
        ))}
      </View>
    </View>
  );
};

export default React.memo(OTPInput);
