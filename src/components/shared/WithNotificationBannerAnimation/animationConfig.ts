import { Dimensions } from "react-native";

const { width, height } = Dimensions.get('window');

export const ANIMATION_CONFIGS = {
  RTL: {
    initial: width,
    final: 0,
    transform: (value: Number) => ({ translateX: value }),
  },
  LTR: {
    initial: -width,
    final: 0,
    transform: (value: Number) => ({ translateX: value }),
  },
  TTB: {
    initial: -height,
    final: 0,
    transform: (value: Number) => ({ translateY: value }),
  },
  BTT: {
    initial: height,
    final: 0,
    transform: (value: Number) => ({ translateY: value }),
  },
};

export const ANIMATION_DIRECTION = {
    RTL: "RTL",
    LTR: "LTR",
    TTB: "TTB",
    BTT: "BTT"
} as const;