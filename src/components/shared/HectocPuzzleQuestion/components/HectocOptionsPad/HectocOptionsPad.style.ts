import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  padContainer: {
    marginBottom: 12,
    gap: 16,
    alignItems: 'center',
  },
  rowContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    width: '100%',
  },
  bottomRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '90%',
    gap: 20,
  },
  utilityButtonsGroup: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 16,
    flex: 1,
  },
  submitButtonGroup: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flex: 1,
  },
});

export default styles;
