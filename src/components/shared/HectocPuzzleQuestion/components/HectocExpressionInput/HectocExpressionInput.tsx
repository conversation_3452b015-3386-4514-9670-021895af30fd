import React, { useCallback } from 'react';
import { StyleProp, TextInput, TextInputProps, ViewStyle } from 'react-native';
import dark from 'core/constants/themes/dark';
import _isNil from 'lodash/isNil';
import _isNaN from 'lodash/isNaN';
import { useHectocPuzzleQuestion } from '../../context';
import styles from './HectocExpressionInput.style';

type HectocExpressionInputProps = Omit<
  TextInputProps,
  | 'style'
  | 'value'
  | 'onChangeText'
  | 'placeholderTextColor'
  | 'editable'
  | 'selection'
  | 'onSelectionChange'
  | 'keyboardType'
  | 'autoCapitalize'
  | 'autoCorrect'
> & {
  placeholder?: string;
  style?: StyleProp<ViewStyle>;
};

const HectocExpressionInput: React.FC<HectocExpressionInputProps> = ({
  placeholder = 'Enter expression',
  style,
  ...restProps
}) => {
  const { state, setCursorPosition } = useHectocPuzzleQuestion();
  const { expression, cursorPosition, isInputInvalid } = state;

  const borderColor = isInputInvalid
    ? dark.colors.defeatColor
    : dark.colors.border;

  const onSelectionChange = useCallback(
    (e: any) => {
      const newPosition = e?.nativeEvent?.selection?.start;
      if (!_isNil(newPosition) && !_isNaN(newPosition)) {
        setCursorPosition(newPosition);
      }
    },
    [setCursorPosition],
  );

  return (
    <TextInput
      style={[styles.input, { borderColor }, style]}
      placeholder={placeholder}
      placeholderTextColor={styles.placeholderText.color}
      value={expression}
      selection={{ start: cursorPosition, end: cursorPosition }}
      onSelectionChange={onSelectionChange}
      editable
      keyboardType="default"
      autoCapitalize="none"
      autoCorrect={false}
      {...restProps}
    />
  );
};

export default React.memo(HectocExpressionInput);
