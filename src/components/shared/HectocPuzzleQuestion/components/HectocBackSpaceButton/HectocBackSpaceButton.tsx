import React from 'react';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import { useHectocPuzzleQuestion } from '../../context';
import styles from './HectocBackSpaceButton.style';

const HectocBackSpaceButton = () => {
  const { handleBackspace } = useHectocPuzzleQuestion();

  return (
    <InteractiveSecondaryButton
      label="⌫"
      onPress={handleBackspace}
      buttonStyle={{ height: 48, width: 52 }}
      labelStyle={styles.text}
      borderColor={dark.colors.warning}
    />
  );
};

export default React.memo(HectocBackSpaceButton);
