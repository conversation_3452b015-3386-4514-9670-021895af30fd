import React from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import styles from './ConfirmationPopover.style';

interface ConfirmationPopoverProps {
  isVisible: boolean;
  title: string;
  message: string;
  confirmText: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  isConfirmDisabled?: boolean;
  confirmButtonType?: 'primary' | 'danger';
}

const ConfirmationPopover: React.FC<ConfirmationPopoverProps> = ({
  isVisible,
  title,
  message,
  confirmText = 'CONFIRM',
  cancelText = 'CANCEL',
  onConfirm,
  onCancel,
  isConfirmDisabled = false,
  confirmButtonType = 'primary',
}) => (
  <Modal
    transparent
    animationType="fade"
    visible={isVisible}
    onRequestClose={onCancel}
  >
    <View style={styles.overlay}>
      <View style={styles.container}>
        <Text style={styles.titleText}>{title}</Text>
        <Text style={styles.messageText}>{message}</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity onPress={onCancel} style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>{cancelText}</Text>
          </TouchableOpacity>
          <InteractiveSecondaryButton
            label={confirmText}
            onPress={onConfirm}
            disabled={isConfirmDisabled}
            buttonContentStyle={{
              paddingHorizontal: 12,
            }}
            borderColor={dark.colors.secondary}
            buttonBackgroundStyle={{
              backgroundColor: dark.colors.secondary,
              borderColor: dark.colors.secondary,
            }}
            buttonContainerStyle={{
              height: 40,
            }}
            labelStyle={styles.confirmButtonLabel}
          />
        </View>
      </View>
    </View>
  </Modal>
);

export default ConfirmationPopover;
