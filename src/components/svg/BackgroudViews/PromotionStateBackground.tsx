import React from 'react';
import Svg, {
  Defs,
  Line,
  LinearGradient,
  Path,
  RadialGradient,
  Rect,
  Stop,
} from 'react-native-svg';

const PromotionStateBackground = ({ color = '#F44927' }: { color: string }) => (
  <Svg
    width="259"
    height="215"
    viewBox="0 0 259 215"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Defs>
      <RadialGradient
        id="paint0_radial"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(134 112.5) rotate(92.8768) scale(99.6255 97.9598)"
      >
        <Stop stopColor="#F5C944" stopOpacity="0.4" />
        <Stop offset="0.92" stopColor="#F5C944" stopOpacity="0" />
      </RadialGradient>
      <LinearGradient
        id="paint1_linear"
        x1="27.3128"
        y1="201.39"
        x2="133.313"
        y2="116.39"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint2_linear"
        x1="26.6964"
        y1="35.3973"
        x2="132.696"
        y2="116.397"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint3_linear"
        x1="242.704"
        y1="34.5974"
        x2="132.704"
        y2="115.597"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint4_linear"
        x1="257.988"
        y1="112.5"
        x2="132.988"
        y2="115.5"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint5_linear"
        x1="4.7779e-08"
        y1="116.5"
        x2="133"
        y2="116.5"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint6_linear"
        x1="243.306"
        y1="200.604"
        x2="133.306"
        y2="115.604"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint7_linear"
        x1="132.5"
        y1="-3.15687e-08"
        x2="132.5"
        y2="116"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
      <LinearGradient
        id="paint8_linear"
        x1="134.5"
        y1="213.995"
        x2="133.5"
        y2="115.995"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="white" />
        <Stop offset="0.53" stopColor="white" stopOpacity="0" />
      </LinearGradient>
    </Defs>
    <Rect width="258" height="212" fill="url(#paint0_radial)" />
    <Line
      x1="26.8436"
      y1="200.805"
      x2="132.844"
      y2="115.805"
      stroke="url(#paint1_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="27.1518"
      y1="34.8014"
      x2="133.152"
      y2="115.801"
      stroke="url(#paint2_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="243.148"
      y1="35.2013"
      x2="133.148"
      y2="116.201"
      stroke="url(#paint3_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="258.006"
      y1="113.25"
      x2="133.006"
      y2="116.25"
      stroke="url(#paint4_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="0"
      y1="115.75"
      x2="133"
      y2="115.75"
      stroke="url(#paint5_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="242.847"
      y1="201.198"
      x2="132.847"
      y2="116.198"
      stroke="url(#paint6_linear)"
      strokeWidth="0.5"
    />
    <Line
      x1="133.25"
      y1="0"
      x2="133.25"
      y2="116"
      stroke="url(#paint7_linear)"
      strokeWidth="0.5"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M48 25C48 27.7614 45.7614 30 43 30C45.7614 30 48 32.2386 48 35C48 32.2386 50.2386 30 53 30C50.2386 30 48 27.7614 48 25Z"
      fill={color}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M238 65C238 67.7614 235.761 70 233 70C235.761 70 238 72.2386 238 75C238 72.2386 240.239 70 243 70C240.239 70 238 67.7614 238 65Z"
      fill={color}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M208 175C208 177.761 205.761 180 203 180C205.761 180 208 182.239 208 185C208 182.239 210.239 180 213 180C210.239 180 208 177.761 208 175Z"
      fill={color}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M38 145C38 147.761 35.7614 150 33 150C35.7614 150 38 152.239 38 155C38 152.239 40.2386 150 43 150C40.2386 150 38 147.761 38 145Z"
      fill={color}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M188 25C188 27.7614 185.761 30 183 30C185.761 30 188 32.2386 188 35C188 32.2386 190.239 30 193 30C190.239 30 188 27.7614 188 25Z"
      fill={color}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M108 205C108 207.761 105.761 210 103 210C105.761 210 108 212.239 108 215C108 212.239 110.239 210 113 210C110.239 210 108 207.761 108 205Z"
      fill={color}
    />
    <Line
      x1="133.5"
      y1="214.005"
      x2="132.5"
      y2="116.005"
      stroke="url(#paint8_linear)"
    />
  </Svg>
);

export default React.memo(PromotionStateBackground);
