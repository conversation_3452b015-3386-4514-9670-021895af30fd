import React from 'react';
import Svg, {
  <PERSON><PERSON><PERSON>ath,
  Defs,
  FeBlend,
  FeColorMatrix,
  FeFlood,
  FeOffset,
  Filter,
  G,
  Mask,
  Path,
  Rect,
} from 'react-native-svg';

const ThirdRankSvg = () => (
  <Svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Rect y="3.05176e-05" width="28" height="28" rx="4" fill="#C79E73" />
    <Rect
      x="0.5"
      y="0.500031"
      width="27"
      height="27"
      rx="3.5"
      stroke="white"
      stroke-opacity="0.5"
    />
    <G clip-path="url(#clip0_356_356)">
      <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="#CDA880" />
      <G filter="url(#filter0_d_356_356)">
        <Mask
          id="path-4-outside-1_356_356"
          maskUnits="userSpaceOnUse"
          x="8"
          y="8.00003"
          width="11"
          height="13"
          fill="black"
        >
          <Rect fill="white" x="8" y="8.00003" width="11" height="13" />
          <Path d="M13.4677 20.224C12.689 20.224 11.9104 20.128 11.1317 19.936C10.3637 19.744 9.69169 19.472 9.11569 19.12L10.2677 16.736C10.7157 17.0347 11.2117 17.264 11.7557 17.424C12.3104 17.584 12.849 17.664 13.3717 17.664C13.9157 17.664 14.3477 17.5627 14.6677 17.36C14.9877 17.1574 15.1477 16.8694 15.1477 16.496C15.1477 16.1654 15.0144 15.904 14.7477 15.712C14.481 15.5094 14.0437 15.408 13.4357 15.408H12.1077V13.408L15.1157 10.16L15.4197 11.248H9.69169V8.80003H17.7877V10.784L14.7797 14.032L13.2277 13.152H14.0917C15.489 13.152 16.545 13.4667 17.2597 14.096C17.985 14.7147 18.3477 15.5147 18.3477 16.496C18.3477 17.136 18.1717 17.7387 17.8197 18.304C17.4784 18.8694 16.9504 19.3334 16.2357 19.696C15.521 20.048 14.5984 20.224 13.4677 20.224Z" />
        </Mask>
        <Path
          d="M13.4677 20.224C12.689 20.224 11.9104 20.128 11.1317 19.936C10.3637 19.744 9.69169 19.472 9.11569 19.12L10.2677 16.736C10.7157 17.0347 11.2117 17.264 11.7557 17.424C12.3104 17.584 12.849 17.664 13.3717 17.664C13.9157 17.664 14.3477 17.5627 14.6677 17.36C14.9877 17.1574 15.1477 16.8694 15.1477 16.496C15.1477 16.1654 15.0144 15.904 14.7477 15.712C14.481 15.5094 14.0437 15.408 13.4357 15.408H12.1077V13.408L15.1157 10.16L15.4197 11.248H9.69169V8.80003H17.7877V10.784L14.7797 14.032L13.2277 13.152H14.0917C15.489 13.152 16.545 13.4667 17.2597 14.096C17.985 14.7147 18.3477 15.5147 18.3477 16.496C18.3477 17.136 18.1717 17.7387 17.8197 18.304C17.4784 18.8694 16.9504 19.3334 16.2357 19.696C15.521 20.048 14.5984 20.224 13.4677 20.224Z"
          fill="white"
        />
        <Path
          d="M11.1317 19.936L11.0104 20.4211L11.012 20.4215L11.1317 19.936ZM9.11569 19.12L8.66549 18.9025L8.46837 19.3104L8.85496 19.5467L9.11569 19.12ZM10.2677 16.736L10.545 16.32L10.0673 16.0015L9.81749 16.5185L10.2677 16.736ZM11.7557 17.424L11.6146 17.9037L11.6171 17.9044L11.7557 17.424ZM14.6677 17.36L14.9352 17.7824L14.9352 17.7824L14.6677 17.36ZM14.7477 15.712L14.445 16.1102L14.4555 16.1178L14.7477 15.712ZM12.1077 15.408H11.6077V15.908H12.1077V15.408ZM12.1077 13.408L11.7408 13.0683L11.6077 13.2121V13.408H12.1077ZM15.1157 10.16L15.5972 10.0255L15.3566 9.16408L14.7488 9.82029L15.1157 10.16ZM15.4197 11.248V11.748H16.0785L15.9012 11.1135L15.4197 11.248ZM9.69169 11.248H9.19169V11.748H9.69169V11.248ZM9.69169 8.80003V8.30003H9.19169V8.80003H9.69169ZM17.7877 8.80003H18.2877V8.30003H17.7877V8.80003ZM17.7877 10.784L18.1545 11.1238L18.2877 10.98V10.784H17.7877ZM14.7797 14.032L14.5331 14.467L14.8775 14.6623L15.1465 14.3718L14.7797 14.032ZM13.2277 13.152V12.652H11.3322L12.9811 13.587L13.2277 13.152ZM17.2597 14.096L16.9292 14.4713L16.9352 14.4764L17.2597 14.096ZM17.8197 18.304L17.3952 18.0397L17.3917 18.0456L17.8197 18.304ZM16.2357 19.696L16.4566 20.1446L16.462 20.1419L16.2357 19.696ZM13.4677 19.724C12.7306 19.724 11.9921 19.6332 11.2514 19.4506L11.012 20.4215C11.8286 20.6229 12.6475 20.724 13.4677 20.724V19.724ZM11.253 19.451C10.5259 19.2692 9.90244 19.0148 9.37641 18.6934L8.85496 19.5467C9.48094 19.9292 10.2015 20.2189 11.0104 20.4211L11.253 19.451ZM9.56588 19.3376L10.7179 16.9536L9.81749 16.5185L8.66549 18.9025L9.56588 19.3376ZM9.99034 17.1521C10.4825 17.4801 11.0248 17.7302 11.6146 17.9037L11.8968 16.9443C11.3986 16.7978 10.9489 16.5892 10.545 16.32L9.99034 17.1521ZM11.6171 17.9044C12.2122 18.0761 12.7975 18.164 13.3717 18.164V17.164C12.9005 17.164 12.4085 17.092 11.8943 16.9436L11.6171 17.9044ZM13.3717 18.164C13.9712 18.164 14.5075 18.0533 14.9352 17.7824L14.4002 16.9376C14.1879 17.0721 13.8602 17.164 13.3717 17.164V18.164ZM14.9352 17.7824C15.3974 17.4897 15.6477 17.0437 15.6477 16.496H14.6477C14.6477 16.695 14.578 16.825 14.4002 16.9376L14.9352 17.7824ZM15.6477 16.496C15.6477 16.0058 15.438 15.5929 15.0398 15.3063L14.4555 16.1178C14.5907 16.2151 14.6477 16.325 14.6477 16.496H15.6477ZM15.0502 15.3139C14.65 15.0098 14.0748 14.908 13.4357 14.908V15.908C14.0126 15.908 14.312 16.0089 14.4451 16.1101L15.0502 15.3139ZM13.4357 14.908H12.1077V15.908H13.4357V14.908ZM12.6077 15.408V13.408H11.6077V15.408H12.6077ZM12.4745 13.7478L15.4825 10.4998L14.7488 9.82029L11.7408 13.0683L12.4745 13.7478ZM14.6341 10.2946L14.9381 11.3826L15.9012 11.1135L15.5972 10.0255L14.6341 10.2946ZM15.4197 10.748H9.69169V11.748H15.4197V10.748ZM10.1917 11.248V8.80003H9.19169V11.248H10.1917ZM9.69169 9.30003H17.7877V8.30003H9.69169V9.30003ZM17.2877 8.80003V10.784H18.2877V8.80003H17.2877ZM17.4208 10.4443L14.4128 13.6923L15.1465 14.3718L18.1545 11.1238L17.4208 10.4443ZM15.0263 13.5971L13.4743 12.7171L12.9811 13.587L14.5331 14.467L15.0263 13.5971ZM13.2277 13.652H14.0917V12.652H13.2277V13.652ZM14.0917 13.652C15.423 13.652 16.3404 13.9528 16.9292 14.4713L17.5901 13.7208C16.7496 12.9806 15.555 12.652 14.0917 12.652V13.652ZM16.9352 14.4764C17.5455 14.997 17.8477 15.6561 17.8477 16.496H18.8477C18.8477 15.3732 18.4246 14.4324 17.5842 13.7156L16.9352 14.4764ZM17.8477 16.496C17.8477 17.0387 17.7 17.5503 17.3952 18.0398L18.2441 18.5683C18.6434 17.927 18.8477 17.2334 18.8477 16.496H17.8477ZM17.3917 18.0456C17.1081 18.5152 16.6585 18.9208 16.0094 19.2502L16.462 20.1419C17.2422 19.746 17.8486 19.2235 18.2477 18.5625L17.3917 18.0456ZM16.0148 19.2475C15.3914 19.5545 14.5517 19.724 13.4677 19.724V20.724C14.645 20.724 15.6506 20.5416 16.4566 20.1446L16.0148 19.2475Z"
          fill="black"
          mask="url(#path-4-outside-1_356_356)"
        />
      </G>
    </G>
    <Defs>
      <Filter
        id="filter0_d_356_356"
        x="8.46838"
        y="8.30002"
        width="10.3793"
        height="13.224"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <FeFlood flood-opacity="0" result="BackgroundImageFix" />
        <FeColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <FeOffset dy="0.8" />
        <FeColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"
        />
        <FeBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_356_356"
        />
        <FeBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_356_356"
          result="shape"
        />
      </Filter>
      <ClipPath id="clip0_356_356">
        <Rect x="4" y="4.00003" width="20" height="20" rx="2" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default React.memo(ThirdRankSvg);
