import React from 'react';
import Svg, { Path } from 'react-native-svg';
import dark from 'core/constants/themes/dark';

const DemotionIcon = ({
  width = 17,
  height = 16,
  color = dark.colors.demotionText,
}: {
  width?: number;
  height?: number;
  color?: string;
}) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M2.5967 6.80958L8.0967 14.3091C8.14531 14.3692 8.20676 14.4177 8.27654 14.4511C8.34633 14.4844 8.42269 14.5017 8.50002 14.5017C8.57736 14.5017 8.65372 14.4844 8.72351 14.4511C8.79329 14.4177 8.85474 14.3692 8.90335 14.3091L14.4033 6.80958C14.4647 6.71933 14.4953 6.61161 14.4903 6.50257C14.4853 6.39353 14.4452 6.28903 14.3758 6.20473C14.3065 6.12042 14.2117 6.06086 14.1057 6.03495C13.9996 6.00905 13.8881 6.0182 13.7877 6.06103L8.5 8.54543L3.2124 6.06108C3.11195 6.01734 2.99999 6.00753 2.89347 6.03313C2.78694 6.05874 2.69167 6.11836 2.62207 6.20297C2.55247 6.28758 2.51235 6.39256 2.50777 6.50202C2.50319 6.61148 2.53441 6.71945 2.5967 6.80958ZM14.2017 5.03808L8.7495 7.44388C8.68651 7.47192 8.61836 7.4865 8.54941 7.48667C8.48046 7.48684 8.41223 7.47261 8.3491 7.44488L2.80125 5.03908C2.71175 5.00042 2.63553 4.9364 2.582 4.85492C2.52846 4.77344 2.49996 4.67807 2.5 4.58058V1.98633C2.50182 1.90424 2.52353 1.82381 2.56327 1.75196C2.60301 1.6801 2.65959 1.61896 2.72816 1.57378C2.79672 1.5286 2.87522 1.50073 2.95693 1.49256C3.03863 1.48439 3.1211 1.49617 3.19725 1.52688L8.5918 3.84573L13.7968 1.52933C13.873 1.49679 13.9561 1.48351 14.0387 1.49067C14.1212 1.49783 14.2008 1.5252 14.2703 1.57036C14.3398 1.61553 14.3971 1.6771 14.4371 1.74966C14.4772 1.82221 14.4988 1.90351 14.5 1.98638V4.58058C14.5001 4.67758 14.4719 4.77249 14.4189 4.85374C14.3659 4.93499 14.2905 4.99901 14.2017 5.03808Z"
      fill={color}
    />
  </Svg>
);

export default React.memo(DemotionIcon);
