import React from 'react';
import Svg, { Path } from 'react-native-svg';
import dark from 'core/constants/themes/dark';

const PromotionIcon = ({
  width = 16,
  height = 16,
  color = dark.colors.secondary,
}: {
  width?: number;
  height?: number;
  color?: string;
}) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M2.0967 9.19042L7.5967 1.69092C7.64531 1.63077 7.70676 1.58225 7.77654 1.54892C7.84633 1.51559 7.92269 1.49829 8.00002 1.49829C8.07736 1.49829 8.15372 1.51559 8.22351 1.54892C8.29329 1.58225 8.35474 1.63077 8.40335 1.69092L13.9033 9.19042C13.9647 9.28067 13.9953 9.38839 13.9903 9.49743C13.9853 9.60647 13.9452 9.71097 13.8758 9.79527C13.8065 9.87958 13.7117 9.93914 13.6057 9.96505C13.4996 9.99095 13.3881 9.9818 13.2877 9.93897L8 7.45457L2.7124 9.93892C2.61195 9.98266 2.49999 9.99247 2.39347 9.96687C2.28694 9.94126 2.19167 9.88164 2.12207 9.79703C2.05247 9.71242 2.01235 9.60744 2.00777 9.49798C2.00319 9.38852 2.03441 9.28055 2.0967 9.19042ZM13.7017 10.9619L8.2495 8.55612C8.18651 8.52808 8.11836 8.5135 8.04941 8.51333C7.98046 8.51316 7.91223 8.52739 7.8491 8.55512L2.30125 10.9609C2.21175 10.9996 2.13553 11.0636 2.082 11.1451C2.02846 11.2266 1.99996 11.3219 2 11.4194V14.0137C2.00182 14.0958 2.02353 14.1762 2.06327 14.248C2.10301 14.3199 2.15959 14.381 2.22816 14.4262C2.29672 14.4714 2.37522 14.4993 2.45693 14.5074C2.53863 14.5156 2.6211 14.5038 2.69725 14.4731L8.0918 12.1543L13.2968 14.4707C13.373 14.5032 13.4561 14.5165 13.5387 14.5093C13.6212 14.5022 13.7008 14.4748 13.7703 14.4296C13.8398 14.3845 13.8971 14.3229 13.9371 14.2503C13.9772 14.1778 13.9988 14.0965 14 14.0136V11.4194C14.0001 11.3224 13.9719 11.2275 13.9189 11.1463C13.8659 11.065 13.7905 11.001 13.7017 10.9619Z"
      fill={color}
    />
  </Svg>
);

export default React.memo(PromotionIcon);
