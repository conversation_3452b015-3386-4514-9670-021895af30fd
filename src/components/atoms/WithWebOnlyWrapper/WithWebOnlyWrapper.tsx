import { StyleSheet, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useRouter } from 'expo-router';
import React from 'react';
import dark from '../../../core/constants/themes/dark';
import SecondaryButton from '../SecondaryButton';

interface WithWebOnlyWrapperProps {
  children?: React.ReactNode;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    padding: 16,
  },
  errorText: {
    color: dark.colors.text,
    fontSize: 16,
  },
});

const WithWebOnlyWrapper = (Components: React.ComponentType<any>) => {
  const WrappedComponent: React.FC = (props) => {
    const { isMobile } = useMediaQuery();
    const router = useRouter();

    if (isMobile) {
      return (
        <View style={styles.container}>
          <Text style={styles.errorText}>
            This page is not compatible on Mobile.
          </Text>
          <SecondaryButton
            label="Go Home"
            radius="sm"
            onPress={() => router.replace('/home')}
          />
        </View>
      );
    }

    return <Components {...props} />;
  };

  return WrappedComponent;
};

export default WithWebOnlyWrapper;
