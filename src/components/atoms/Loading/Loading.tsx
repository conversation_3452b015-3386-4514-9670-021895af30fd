import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';

import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    backgroundColor: dark.colors.background,
  },
  label: {
    color: dark.colors.secondary,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Montserrat-500'
  },
});

const Loading = ({ label }: { label?: string }) => (
  <View style={styles.container}>
    <ActivityIndicator color={dark.colors.secondary} size="large" />
    {label && <Text style={styles.label}>{label}</Text>}
  </View>
);

export default Loading;
