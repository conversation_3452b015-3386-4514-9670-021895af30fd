import React from 'react';

import Dark from 'core/constants/themes/dark';

interface LinearGradientTextWebProps {
  colors?: string[];
  textStyle?: React.CSSProperties;
  children: React.ReactNode;
}

const LinearGradientTextWeb: React.FC<LinearGradientTextWebProps> = (props) => {
  const {
    colors = [Dark.colors.gradientLeft, Dark.colors.gradientRight],
    textStyle,
    children,
  } = props;

  const gradientStyle = {
    background: `linear-gradient(to right, ${colors.join(',')})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    fontFamily: 'Montserrat-500',
    ...textStyle,
  };

  return (
    <span style={gradientStyle} {...props}>
      {children}
    </span>
  );
};

export default React.memo(LinearGradientTextWeb);
