import React, { useCallback, useEffect, useRef, useState } from 'react';
import Rive, { Fit, RiveRef } from 'rive-react-native';
import {
  AppState,
  AppStateStatus,
  NativeSyntheticEvent,
  Platform,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useRiveCachedUrl } from 'core/hooks/useRiveCache';
import { isValidRiveUrl } from 'atoms/Rive/utils/riveUrlValidation';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  fit: Fit;
  onLoopEnd?: () => void;
  fallbackText?: string;
  maxRetries?: number;
  retryDelay?: number;
  enableCrashPrevention?: boolean;
  riveRef?: React.Ref<RiveRef>;
  onStateChanged?: (
    event: NativeSyntheticEvent<{
      stateMachineName: string;
      stateName: string;
    }>,
  ) => void;
}

interface RiveState {
  hasError: boolean;
  isLoading: boolean;
  retryCount: number;
  errorType: 'network' | 'file' | 'runtime' | 'unknown';
  lastError?: Error;
}

const DEFAULT_FALLBACK_TEXT = 'Animation unavailable';
const DEFAULT_MAX_RETRIES = 2;
const DEFAULT_RETRY_DELAY = 1000;

const isNetworkError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('network') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('404') ||
    errorMessage.includes('fetch') ||
    errorMessage.includes('connection')
  );
};

const isFileCorruptionError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('corrupt') ||
    errorMessage.includes('invalid') ||
    errorMessage.includes('malformed') ||
    errorMessage.includes('parse')
  );
};

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay = true,
    fit = Fit.Contain,
    loop = true,
    onLoopEnd,
    fallbackText = DEFAULT_FALLBACK_TEXT,
    maxRetries = DEFAULT_MAX_RETRIES,
    retryDelay = DEFAULT_RETRY_DELAY,
    enableCrashPrevention = true,
    riveRef,
    onStateChanged,
  } = props;

  // Add global error handler for unhandled Rive errors
  useEffect(() => {
    const originalConsoleError = console.error;

    const handleGlobalError = (...args: any[]) => {
      const errorMessage = args.join(' ').toLowerCase();

      // Check if this is a Rive-related error
      if (errorMessage.includes('rive') || errorMessage.includes('malformed') || errorMessage.includes('librive')) {
        console.warn('Global Rive error intercepted:', ...args);

        // Track the error but don't let it crash the app
        try {
          Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
            url,
            errorType: 'global',
            errorMessage: errorMessage,
            source: 'globalErrorHandler',
            timestamp: new Date().toISOString(),
          });
        } catch (analyticsError) {
          console.warn('Failed to track global Rive error:', analyticsError);
        }

        // Don't propagate Rive errors to crash the app
        return;
      }

      // For non-Rive errors, use original console.error
      originalConsoleError(...args);
    };

    // Override console.error temporarily
    console.error = handleGlobalError;

    return () => {
      // Restore original console.error
      console.error = originalConsoleError;
    };
  }, [url]);

  // Use cached URL
  const {
    url: cachedUrl,
    isLoading: isCacheLoading,
    isCached,
  } = useRiveCachedUrl(url);

  const [state, setState] = useState<RiveState>({
    hasError: false,
    isLoading: true,
    retryCount: 0,
    errorType: 'unknown',
  });
  const shouldUseCachedUrl = Platform.OS !== 'android';
  const urlToUse = shouldUseCachedUrl ? cachedUrl : url;

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);
  const isComponentMountedRef = useRef(true);

  const memoizedStyle = React.useMemo(() => style, [style]);

  useEffect(() => {
    setState({
      hasError: false,
      isLoading: true,
      retryCount: 0,
      errorType: 'unknown',
    });
  }, [url]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (state.hasError && state.errorType === 'runtime') {
          setState((prev) => ({
            ...prev,
            hasError: false,
            isLoading: true,
            retryCount: 0,
            errorType: 'unknown',
          }));
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.hasError, state.errorType, url]);

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    },
    [],
  );

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      // Clean up Rive ref to prevent memory leaks
      try {
        if (riveRef && typeof riveRef === 'object' && 'current' in riveRef) {
          const ref = riveRef.current;
          if (ref) {
            // Try to stop the animation before cleanup
            if (typeof ref.stop === 'function') {
              ref.stop();
            }
            // Clear the ref
            riveRef.current = null;
          }
        }
      } catch (error) {
        console.warn('Error cleaning up Rive ref:', error);
      }
    },
    [riveRef],
  );

  const retryLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    if (state.retryCount < maxRetries) {
      setState((prev) => ({
        ...prev,
        hasError: false,
        isLoading: true,
        retryCount: prev.retryCount + 1,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_RETRY, {
        url,
        retryCount: state.retryCount + 1,
        errorType: state.errorType,
        maxRetries,
      });
    }
  }, [state.retryCount, state.errorType, maxRetries, url]);

  const handleError = useCallback(
    (error: any) => {
      try {
        if (!isComponentMountedRef.current) return;

        let errorType: RiveState['errorType'] = 'unknown';

        if (isNetworkError(error)) {
          errorType = 'network';
        } else if (isFileCorruptionError(error)) {
          errorType = 'file';
        } else {
          errorType = 'runtime';
        }

        // Prevent crash by safely updating state
        setState((prev) => ({
          ...prev,
          hasError: true,
          isLoading: false,
          errorType,
          lastError: error,
        }));

        // Safely track analytics
        try {
          Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
            url,
            errorType,
            errorMessage: error?.message || 'Unknown error',
            retryCount: state.retryCount,
            artboardName,
            stateMachineName,
            crashPrevention: true,
          });
        } catch (analyticsError) {
          console.warn('Failed to track Rive error analytics:', analyticsError);
        }

        // Only retry for network errors, not for malformed files
        if (errorType === 'network' && state.retryCount < maxRetries) {
          retryTimeoutRef.current = setTimeout(
            () => {
              try {
                retryLoad();
              } catch (retryError) {
                console.warn('Failed to retry Rive load:', retryError);
              }
            },
            retryDelay * (state.retryCount + 1),
          );
        }

        // Log error for debugging but don't let it crash the app
        if (__DEV__) {
          console.warn('Rive animation error handled:', {
            url,
            errorType,
            errorMessage: error?.message,
            retryCount: state.retryCount,
          });
        }
      } catch (handlerError) {
        // Last resort: prevent the error handler itself from crashing
        console.error('Critical error in Rive error handler:', handlerError);

        // Force safe state
        try {
          setState({
            hasError: true,
            isLoading: false,
            retryCount: 0,
            errorType: 'unknown',
            lastError: error,
          });
        } catch (stateError) {
          console.error('Failed to set safe state in Rive component:', stateError);
        }
      }
    },
    [
      state.retryCount,
      maxRetries,
      retryDelay,
      url,
      artboardName,
      stateMachineName,
      retryLoad,
    ],
  );

  const handleLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    setState((prev) => ({
      ...prev,
      hasError: false,
      isLoading: false,
    }));

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED, {
      url,
      cachedUrl,
      isCached,
      retryCount: state.retryCount,
      artboardName,
      stateMachineName,
    });
  }, [
    url,
    cachedUrl,
    isCached,
    state.retryCount,
    artboardName,
    stateMachineName,
  ]);

  const handleLoopEnd = useCallback(() => {
    try {
      onLoopEnd?.();
    } catch (error) {
      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_CALLBACK_ERROR, {
        url,
        errorMessage: error?.message || 'Loop end callback error',
      });
    }
  }, [onLoopEnd, url]);

  // Show loading state while cache is loading or validating URL

  const onRiveEventReceived = (event) => {
    // These are properties added to the event at Design Time in the
    const eventProperties = event.properties;
    console.info(eventProperties, event, 'infooooooo');
  };

  const riveProps = {
    stateMachineName,
    artboardName,
    style,
    onLoopEnd: handleLoopEnd,
    onError: handleError,
    onPlay: handleLoad,
    onStateChanged,
    ...(urlToUse ? { url: urlToUse } : {}),
    ...(resourceName ? { resourceName } : {}),
  };

  // Check if we have either a valid URL or a resourceName
  const hasValidSource = (url && isValidRiveUrl(url)) || resourceName;

  if ((isCacheLoading && shouldUseCachedUrl) || !hasValidSource) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {isCacheLoading ? 'Loading...' : fallbackText}
        </Text>
      </View>
    );
  }

  if (state.hasError) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (enableCrashPrevention) {
    try {
      return (
        <Rive
          fit={fit}
          url={urlToUse}
          stateMachineName={stateMachineName}
          artboardName={artboardName}
          style={memoizedStyle}
          autoplay={autoPlay}
          // loop={loop}
          onLoopEnd={handleLoopEnd}
          onError={handleError}
          onPlay={handleLoad}
          onStateChanged={onStateChanged}
          ref={riveRef}
          {...(riveProps as any)}
        />
      );
    } catch (error) {
      // Handle synchronous render errors
      console.warn('Rive component render error caught:', error);

      // Safely handle the error without causing additional crashes
      try {
        handleError(error);
      } catch (handlerError) {
        console.error('Error in handleError during render catch:', handlerError);
      }

      return (
        <View style={memoizedStyle}>
          <Text style={{ color: dark.colors.textDark, textAlign: 'center', fontSize: 12, opacity: 0.7 }}>
            {fallbackText}
          </Text>
        </View>
      );
    }
  }

  return (
    <Rive
      fit={fit}
      url={urlToUse}
      // resourceName={resourceName}
      stateMachineName={stateMachineName}
      artboardName={artboardName}
      style={memoizedStyle}
      autoplay={autoPlay}
      // loop={loop}
      onLoopEnd={handleLoopEnd}
      onError={handleError}
      onPlay={handleLoad}
      onStateChanged={onStateChanged}
      ref={riveRef}
      {...(riveProps as any)}
    />
  );
};

export default React.memo(RiveComponent);
