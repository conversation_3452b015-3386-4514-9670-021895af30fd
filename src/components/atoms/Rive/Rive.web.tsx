import Rive, { Alignment, Fit, Layout } from '@rive-app/react-canvas';
import PropTypes from 'prop-types';
import React from 'react';
import { useRiveCachedUrl } from 'core/hooks/useRiveCache';
import { isValidRiveUrl } from './utils/riveUrlValidation';

const RiveComponent = (props) => {
  const {
    url,
    artboardName,
    stateMachineName,
    style,
    autoPlay,
    fit = Fit.Contain,
    ...restProps
  } = props;

  // Use cached URL
  const { url: cachedUrl, isLoading: isCacheLoading } = useRiveCachedUrl(url);

  // Show loading state while cache is loading or validating URL
  if (isCacheLoading || !isValidRiveUrl(cachedUrl)) {
    return (
      <div
        style={{
          ...style,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <span>{isCacheLoading ? 'Loading...' : 'Invalid animation'}</span>
      </div>
    );
  }

  // console.info("MOHAN: restProps", restProps);

  return (
    <Rive
      src={cachedUrl}
      stateMachines={stateMachineName}
      style={style}
      layout={new Layout({ fit: fit, alignment: Alignment.Center })}
    />
  );
};

RiveComponent.propTypes = {
  url: PropTypes.string.isRequired,
  loop: PropTypes.bool,
  artboardName: PropTypes.string,
  stateMachineName: PropTypes.string,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

export default React.memo(RiveComponent);
