import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { isValidRiveUrl } from './riveUrlValidation';

export interface RiveValidationResult {
  isValid: boolean;
  error?: string;
  fileSize?: number;
}

export interface RivePreloadOptions {
  timeout?: number;
  maxFileSize?: number; // in bytes
  validateContent?: boolean;
}

const DEFAULT_TIMEOUT = 10000; // 10 seconds
const DEFAULT_MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const validateRiveUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;

  try {
    const urlObj = new URL(url);
    return (
      (urlObj.protocol === 'https:' || urlObj.protocol === 'http:') &&
      url.toLowerCase().endsWith('.riv')
    );
  } catch {
    return false;
  }
};

/**
 * Checks if a Rive file exists and is accessible
 */
export const validateRiveFile = async (
  url: string,
  options: RivePreloadOptions = {},
): Promise<RiveValidationResult> => {
  const {
    timeout = DEFAULT_TIMEOUT,
    maxFileSize = DEFAULT_MAX_FILE_SIZE,
    validateContent = true,
  } = options;

  if (!isValidRiveUrl(url)) {
    return {
      isValid: false,
      error: 'Invalid URL format',
    };
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return {
        isValid: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const contentLength = response.headers.get('content-length');
    const fileSize = contentLength ? parseInt(contentLength, 10) : 0;

    if (fileSize > maxFileSize) {
      return {
        isValid: false,
        error: `File too large: ${fileSize} bytes (max: ${maxFileSize})`,
        fileSize,
      };
    }

    if (validateContent) {
      const contentType = response.headers.get('content-type');
      if (
        contentType &&
        !contentType.includes('application/octet-stream') &&
        !contentType.includes('binary')
      ) {
        // Note: Rive files might not have a specific content-type, so this is a soft check
        console.warn(
          'Rive file might have unexpected content-type:',
          contentType,
        );
      }
    }

    return {
      isValid: true,
      fileSize,
    };
  } catch (error: any) {
    let errorMessage = 'Unknown error';

    if (error.name === 'AbortError') {
      errorMessage = 'Request timeout';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      isValid: false,
      error: errorMessage,
    };
  }
};

/**
 * Preloads a Rive file to check if it's accessible
 */
export const preloadRiveFile = async (
  url: string,
  options: RivePreloadOptions = {},
): Promise<RiveValidationResult> => {
  const startTime = Date.now();

  try {
    const result = await validateRiveFile(url, options);

    const loadTime = Date.now() - startTime;

    Analytics.track(
      result.isValid
        ? ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED
        : ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR,
      {
        url,
        loadTime,
        fileSize: result.fileSize,
        error: result.error,
        preload: true,
      },
    );

    return result;
  } catch (error: any) {
    const loadTime = Date.now() - startTime;

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
      url,
      loadTime,
      error: error.message || 'Preload failed',
      preload: true,
    });

    return {
      isValid: false,
      error: error.message || 'Preload failed',
    };
  }
};

/**
 * Batch preload multiple Rive files
 */
export const preloadRiveFiles = async (
  urls: string[],
  options: RivePreloadOptions = {},
): Promise<Record<string, RiveValidationResult>> => {
  const results: Record<string, RiveValidationResult> = {};

  const promises = urls.map(async (url) => {
    const result = await preloadRiveFile(url, options);
    results[url] = result;
    return result;
  });

  await Promise.allSettled(promises);

  return results;
};

/**
 * Creates a cache key for Rive validation results
 */
export const createRiveCacheKey = (url: string): string =>
  `rive_validation_${btoa(url).replace(/[^a-zA-Z0-9]/g, '')}`;

/**
 * Cached validation with localStorage
 */
export const getCachedValidation = (
  url: string,
): RiveValidationResult | null => {
  try {
    const cacheKey = createRiveCacheKey(url);
    const cached = localStorage.getItem(cacheKey);

    if (cached) {
      const parsed = JSON.parse(cached);
      const now = Date.now();

      // Cache for 1 hour
      if (now - parsed.timestamp < 60 * 60 * 1000) {
        return parsed.result;
      }
      localStorage.removeItem(cacheKey);
    }
  } catch (error) {
    // Ignore cache errors
  }

  return null;
};

/**
 * Cache validation result
 */
export const setCachedValidation = (
  url: string,
  result: RiveValidationResult,
): void => {
  try {
    const cacheKey = createRiveCacheKey(url);
    const cacheData = {
      result,
      timestamp: Date.now(),
    };

    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
  } catch (error) {
    // Ignore cache errors
  }
};

/**
 * Validate with caching
 */
export const validateRiveFileWithCache = async (
  url: string,
  options: RivePreloadOptions = {},
): Promise<RiveValidationResult> => {
  // Check cache first
  const cached = getCachedValidation(url);
  if (cached) {
    return cached;
  }

  // Validate and cache result
  const result = await validateRiveFile(url, options);
  setCachedValidation(url, result);

  return result;
};
