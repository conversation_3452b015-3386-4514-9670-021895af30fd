import React from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

interface RiveErrorHandler {
  setup: () => void;
  cleanup: () => void;
}

let isSetup = false;
let originalConsoleError: typeof console.error;
let originalConsoleWarn: typeof console.warn;

/**
 * Global error handler for Rive-related crashes
 * This helps prevent app crashes from malformed Rive files or native library errors
 */
export const riveGlobalErrorHandler: RiveErrorHandler = {
  setup: () => {
    if (isSetup) {
      return; // Already setup
    }

    // Store original console methods
    originalConsoleError = console.error;
    originalConsoleWarn = console.warn;

    // Override console.error to catch Rive errors
    console.error = (...args: any[]) => {
      const errorMessage = args.join(' ').toLowerCase();
      
      // Check if this is a Rive-related error
      if (
        errorMessage.includes('rive') ||
        errorMessage.includes('malformed') ||
        errorMessage.includes('librive') ||
        errorMessage.includes('riv file') ||
        errorMessage.includes('rive-react-native') ||
        errorMessage.includes('rive animation')
      ) {
        console.warn('🛡️ Rive error intercepted by global handler:', ...args);
        
        // Track the error for monitoring
        try {
          Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
            errorType: 'global_intercepted',
            errorMessage: errorMessage,
            source: 'globalErrorHandler',
            timestamp: new Date().toISOString(),
            args: args.slice(0, 3), // Limit args to prevent large payloads
          });
        } catch (analyticsError) {
          console.warn('Failed to track global Rive error:', analyticsError);
        }
        
        // Don't propagate Rive errors to prevent crashes
        return;
      }
      
      // For non-Rive errors, use original console.error
      originalConsoleError(...args);
    };

    // Also override console.warn for Rive warnings that might escalate
    console.warn = (...args: any[]) => {
      const warningMessage = args.join(' ').toLowerCase();
      
      if (
        warningMessage.includes('rive') &&
        (warningMessage.includes('corrupt') || warningMessage.includes('invalid'))
      ) {
        console.info('🛡️ Rive warning intercepted:', ...args);
        
        try {
          Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
            errorType: 'global_warning',
            errorMessage: warningMessage,
            source: 'globalWarningHandler',
            timestamp: new Date().toISOString(),
          });
        } catch (analyticsError) {
          console.warn('Failed to track global Rive warning:', analyticsError);
        }
        
        return;
      }
      
      // For non-Rive warnings, use original console.warn
      originalConsoleWarn(...args);
    };

    // Set up global error event listener for unhandled errors
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        const errorMessage = event.message?.toLowerCase() || '';
        
        if (
          errorMessage.includes('rive') ||
          errorMessage.includes('malformed') ||
          event.filename?.includes('rive')
        ) {
          console.warn('🛡️ Global window error intercepted (Rive):', event);
          
          try {
            Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
              errorType: 'global_window_error',
              errorMessage: event.message,
              filename: event.filename,
              lineno: event.lineno,
              colno: event.colno,
              source: 'windowErrorHandler',
              timestamp: new Date().toISOString(),
            });
          } catch (analyticsError) {
            console.warn('Failed to track window Rive error:', analyticsError);
          }
          
          // Prevent the error from crashing the app
          event.preventDefault();
          return false;
        }
      });

      // Set up unhandled promise rejection handler
      window.addEventListener('unhandledrejection', (event) => {
        const reason = event.reason?.toString()?.toLowerCase() || '';
        
        if (
          reason.includes('rive') ||
          reason.includes('malformed') ||
          reason.includes('librive')
        ) {
          console.warn('🛡️ Unhandled promise rejection intercepted (Rive):', event);
          
          try {
            Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
              errorType: 'global_promise_rejection',
              errorMessage: event.reason?.toString(),
              source: 'promiseRejectionHandler',
              timestamp: new Date().toISOString(),
            });
          } catch (analyticsError) {
            console.warn('Failed to track promise rejection Rive error:', analyticsError);
          }
          
          // Prevent the error from crashing the app
          event.preventDefault();
        }
      });
    }

    isSetup = true;
    console.info('🛡️ Rive global error handler setup complete');
  },

  cleanup: () => {
    if (!isSetup) {
      return;
    }

    // Restore original console methods
    if (originalConsoleError) {
      console.error = originalConsoleError;
    }
    if (originalConsoleWarn) {
      console.warn = originalConsoleWarn;
    }

    isSetup = false;
    console.info('🛡️ Rive global error handler cleaned up');
  },
};

/**
 * Hook to automatically setup and cleanup global Rive error handling
 */
export const useRiveGlobalErrorHandler = () => {
  React.useEffect(() => {
    riveGlobalErrorHandler.setup();
    
    return () => {
      riveGlobalErrorHandler.cleanup();
    };
  }, []);
};

export default riveGlobalErrorHandler;
