import React, { useRef, useCallback } from 'react';
import RiveErrorBoundary from './RiveErrorBoundary';

interface WithRiveErrorBoundaryProps {
  fallbackText?: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onAsyncError?: (error: Error) => void;
}

const withRiveErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = React.forwardRef<
    any,
    P & WithRiveErrorBoundaryProps
  >((props, ref) => {
    const { fallbackText, onError, onAsyncError, ...componentProps } = props;
    const errorBoundaryRef = useRef<RiveErrorBoundary>(null);

    // Enhanced error handler that can handle both sync and async errors
    const handleAsyncError = useCallback((error: Error) => {
      try {
        // Try to trigger the error boundary for async errors
        if (errorBoundaryRef.current) {
          errorBoundaryRef.current.handleAsyncError(error);
        }

        // Call the provided async error handler
        onAsyncError?.(error);
      } catch (handlerError) {
        console.error('Error in async error handler:', handlerError);
      }
    }, [onAsyncError]);

    // Wrap the component's onError prop to also handle async errors
    const enhancedComponentProps = {
      ...componentProps,
      onError: useCallback((error: any) => {
        // Handle the error in the component's own error handler
        if ((componentProps as any).onError) {
          try {
            (componentProps as any).onError(error);
          } catch (handlerError) {
            console.error('Error in component error handler:', handlerError);
          }
        }

        // Also trigger the async error handler
        handleAsyncError(error);
      }, [(componentProps as any).onError, handleAsyncError]),
    };

    return (
      <RiveErrorBoundary
        ref={errorBoundaryRef}
        fallbackText={fallbackText}
        onError={onError}
        onAsyncError={onAsyncError}
        url={(componentProps as any).url}
        style={(componentProps as any).style}
      >
        <Component {...(enhancedComponentProps as P)} ref={ref} />
      </RiveErrorBoundary>
    );
  });

  WrappedComponent.displayName = `withRiveErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default withRiveErrorBoundary;
