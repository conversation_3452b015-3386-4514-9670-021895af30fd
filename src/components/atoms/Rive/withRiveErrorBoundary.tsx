import React from 'react';
import RiveErrorBoundary from './RiveErrorBoundary';

interface WithRiveErrorBoundaryProps {
  fallbackText?: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

const withRiveErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = React.forwardRef<
    any,
    P & WithRiveErrorBoundaryProps
  >((props, ref) => {
    const { fallbackText, onError, ...componentProps } = props;
    
    return (
      <RiveErrorBoundary
        fallbackText={fallbackText}
        onError={onError}
        url={(componentProps as any).url}
        style={(componentProps as any).style}
      >
        <Component {...(componentProps as P)} ref={ref} />
      </RiveErrorBoundary>
    );
  });

  WrappedComponent.displayName = `withRiveErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default withRiveErrorBoundary;
