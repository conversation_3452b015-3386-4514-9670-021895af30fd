# Rive Error Handling Guide

This guide explains how to properly handle Rive animation errors to prevent app crashes, especially from malformed Rive files.

## Why Error Boundaries Don't Catch Rive Errors

React Error Boundaries only catch errors that occur during React's render cycle. However, Rive animation errors (like malformed file errors) occur **asynchronously** in the native Rive library, outside of React's render cycle. These errors are handled by the `onError` callback, not by Error Boundaries.

## Enhanced Error Handling Solutions

### 1. Global Error Handler (Recommended)

Set up global error handling at the app level to catch all Rive-related errors:

```tsx
// In your App.tsx or _layout.tsx
import { useRiveGlobalErrorHandler } from '@/src/components/atoms/Rive/utils/riveGlobalErrorHandler';

export default function App() {
  // This will automatically setup and cleanup global Rive error handling
  useRiveGlobalErrorHandler();
  
  return (
    // Your app content
  );
}
```

### 2. Enhanced Rive Component

The `Rive.native.tsx` component now includes:

- **Robust error handling** in the `onError` callback
- **Try-catch blocks** around rendering
- **Safe state updates** to prevent crashes
- **Automatic retry logic** for network errors (but not malformed files)
- **Global error interception** for unhandled Rive errors

### 3. Enhanced Error Boundary Wrapper

Use the enhanced `withRiveErrorBoundary` HOC:

```tsx
import Rive from '@/src/components/atoms/Rive/Rive.native';
import withRiveErrorBoundary from '@/src/components/atoms/Rive/withRiveErrorBoundary';

const SafeRive = withRiveErrorBoundary(Rive);

// Usage
<SafeRive
  url="https://example.com/animation.riv"
  fallbackText="Animation unavailable"
  onError={(error, errorInfo) => {
    console.log('Error Boundary caught:', error);
  }}
  onAsyncError={(error) => {
    console.log('Async error handled:', error);
  }}
/>
```

## Error Types Handled

### 1. Malformed Rive Files
- **Symptoms**: "Malformed Rive File" errors, parsing errors
- **Handling**: Shows fallback UI, tracks error, no retry
- **Prevention**: File validation before loading

### 2. Network Errors
- **Symptoms**: 404, timeout, connection errors
- **Handling**: Automatic retry with exponential backoff
- **Prevention**: URL validation, cache management

### 3. Runtime Errors
- **Symptoms**: Crashes during animation playback
- **Handling**: Safe error recovery, fallback UI
- **Prevention**: App state monitoring, cleanup on unmount

## Configuration Options

### Rive Component Props

```tsx
interface RiveComponentProps {
  // ... other props
  enableCrashPrevention?: boolean; // Default: true
  maxRetries?: number; // Default: 2
  retryDelay?: number; // Default: 1000ms
  fallbackText?: string; // Default: "Animation unavailable"
}
```

### Error Boundary Props

```tsx
interface WithRiveErrorBoundaryProps {
  fallbackText?: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onAsyncError?: (error: Error) => void;
}
```

## Best Practices

### 1. Always Use Error Handling
```tsx
// ✅ Good
<SafeRive
  url={animationUrl}
  fallbackText="Loading animation..."
  enableCrashPrevention={true}
/>

// ❌ Bad
<Rive url={animationUrl} />
```

### 2. Validate URLs Before Loading
```tsx
import { isValidRiveUrl } from '@/src/components/atoms/Rive/utils/riveUrlValidation';

const url = "https://example.com/animation.riv";
if (isValidRiveUrl(url)) {
  // Safe to load
} else {
  // Show fallback
}
```

### 3. Handle Different Error Types
```tsx
<SafeRive
  url={animationUrl}
  onAsyncError={(error) => {
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('malformed')) {
      // Handle malformed file - don't retry
      showFallbackAnimation();
    } else if (errorMessage.includes('network')) {
      // Handle network error - component will auto-retry
      showNetworkErrorMessage();
    } else {
      // Handle other errors
      trackUnknownError(error);
    }
  }}
/>
```

### 4. Monitor and Track Errors
All errors are automatically tracked with analytics:

```tsx
// Errors are tracked with these events:
ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR
ANALYTICS_EVENTS.RIVE_ANIMATION_RETRY
ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED
```

## Debugging

### Enable Debug Logging
```tsx
// In development, errors are logged to console
if (__DEV__) {
  console.log('Rive error details:', error);
}
```

### Check Error Types
```tsx
onAsyncError={(error) => {
  console.log('Error type:', error.message);
  console.log('Error stack:', error.stack);
}}
```

## Migration Guide

If you're currently using basic Rive components:

1. **Add global error handler** to your app root
2. **Wrap Rive components** with `withRiveErrorBoundary`
3. **Add error callbacks** to handle specific error types
4. **Test with malformed files** to ensure fallbacks work

## Testing Error Handling

Create test cases with:
- Invalid/malformed Rive files
- Network timeout scenarios
- Large file loading
- Rapid component mounting/unmounting

This comprehensive error handling approach ensures your app remains stable even when encountering problematic Rive files.
