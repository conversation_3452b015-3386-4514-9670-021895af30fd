import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import IconButton from '../index';

describe('IconButton', () => {
  it('renders without crashing', () => {
    render(
      <IconButton
        name="home"
        size={30}
        color="white"
        onPress={() => {}}
        buttonStyle={{}}
      />,
    );
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByRole } = render(
      <IconButton
        name="home"
        size={30}
        color="white"
        onPress={onPress}
        buttonStyle={{}}
        role="button"
      />,
    );
    const button = getByRole('button');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });
});
