#define API_VERSION_NUMBER @API_VERSION_MAJOR@,@API_VERSION_MINOR@,@API_VERSION_REVISION@,0
#define API_VERSION_NUMBER_STR "@API_VERSION_MAJOR@.@API_VERSION_MINOR@.@API_VERSION_REVISION@"
#define PROJECT_VERSION_NUMBER @PROJECT_VERSION_MAJOR@,@PROJECT_VERSION_MINOR@,@PROJECT_VERSION_REVISION@,0
#define PROJECT_VERSION_NUMBER_STR "@PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_REVISION@"

#include <windows.h>

1 VERSIONINFO
FILETYPE VFT_DLL
FILEOS VOS_NT_WINDOWS32
PRODUCTVERSION PROJECT_VERSION_NUMBER
FILEVERSION API_VERSION_NUMBER
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904E4"
    BEGIN
      VALUE "CompanyName", "VideoLAN"
      VALUE "ProductName", "dav1d"
      VALUE "ProductVersion", PROJECT_VERSION_NUMBER_STR
      VALUE "FileVersion", API_VERSION_NUMBER_STR
      VALUE "FileDescription", "dav1d " PROJECT_VERSION_NUMBER_STR " - AV1 decoder"
      VALUE "InternalName", "dav1d"
      VALUE "OriginalFilename", "libdav1d.dll"
      VALUE "LegalCopyright", "Copyright \251 @COPYRIGHT_YEARS@ VideoLAN and dav1d Authors"
    END
  END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x409, 1252
  END
END
