/* eslint-disable no-undef */
import useWebsocketStore from 'store/useWebSocketStore';
import { useCallback, useEffect, useRef } from 'react';
import useRefetchOnAppFocus from 'core/hooks/useRefetchOnAppFocus';

const useWebsocketMonitoring = () => {
  const { ws, reconnect } = useWebsocketStore((state) => ({
    ws: state.ws,
    reconnect: state.reconnect,
  }));

  const wsRef = useRef(ws);
  wsRef.current = ws;

  const _reconnect = useCallback(() => {
    if (
      wsRef.current &&
      (wsRef.current?.readyState === WebSocket.OPEN ||
        wsRef.current?.readyState === WebSocket.CONNECTING)
    ) {
      return;
    }
    reconnect();
  }, [reconnect]);

  const reconnectRef = useRef(_reconnect);
  reconnectRef.current = _reconnect;

  const intervalRef = useRef<any>(null);

  const checkAndReconnect = () => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    reconnectRef.current();
    intervalRef.current = setInterval(() => {
      reconnectRef.current();
    }, 1500);
  };

  const checkAndReconnectRef = useRef(checkAndReconnect);
  checkAndReconnectRef.current = checkAndReconnect;

  useRefetchOnAppFocus(checkAndReconnectRef.current);

  useEffect(() => {
    checkAndReconnectRef.current();
    return () => clearInterval(intervalRef.current);
  }, []);
};

export default useWebsocketMonitoring;
