import _get from 'lodash/get';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import useGetWeeklyCoinsEarned from 'modules/profile/hooks/query/useGetWeeklyCoinsEarned';

const StatikCoinsThreshold = 25;

const useUserStatikCoinsEarnedEvent = () => {
  const { updateCurrentUser, userId: currentUserId, user } = useSession();

  const { updateWeeklyCoinsEarnedCache } = useGetWeeklyCoinsEarned();

  const handleUserStatikCoinsEarnedEvent = useCallback(
    ({ payload }) => {
      if (_isNil(user) || _isEmpty(user)) {
        return;
      }

      const statikCoins = userReader.statikCoins(user);
      const statikCoinsEarned = _get(payload, 'statikCoinsEarned', 0);

      let updatedUser = {
        ...user,
        statikCoins: statikCoins + statikCoinsEarned,
      };

      updateWeeklyCoinsEarnedCache?.({ increaseInCoin: statikCoinsEarned });

      const userCurrLeagueInfo = userReader.userCurrLeagueInfo(user);

      if (!_isNil(userCurrLeagueInfo)) {
        const hasParticipatedInLeague =
          userReader.userHasParticipatedInLeague(user);

        if (!hasParticipatedInLeague) {
          const coinsTillLastWeek = userReader.userCoinsTillLastWeek(user);
          const coinsNow = statikCoins + statikCoinsEarned;

          if (coinsNow - coinsTillLastWeek >= StatikCoinsThreshold) {
            updatedUser = {
              ...updatedUser,
              league: {
                ...userCurrLeagueInfo,
                hasParticipated: true,
              },
            };
          }
        }
      }

      updateCurrentUser(updatedUser);
    },
    [updateCurrentUser, user, updateWeeklyCoinsEarnedCache],
  );

  return {
    handleUserStatikCoinsEarnedEvent,
  };
};

export default useUserStatikCoinsEarnedEvent;
