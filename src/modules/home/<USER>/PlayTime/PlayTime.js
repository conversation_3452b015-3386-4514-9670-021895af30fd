import React, { useCallback, useEffect, useState } from 'react';
import { Platform, View } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import Header from 'shared/Header';
import dark from '@/src/core/constants/themes/dark';
import { PAGE_NAME_KEY } from '@/src/core/constants/pageNames';
import useGoBack from '@/src/navigator/hooks/useGoBack';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Analytics from '@/src/core/analytics';
import _join from 'lodash/join';
import _split from 'lodash/split';
import _toLower from 'lodash/toLower';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import HowToPlay from 'modules/home/<USER>/PlayTime/HowToPlay';
import InnerGameType from '../GameConfigs/GameType/InnerGameType';
import GameTimeConfigs from '../GameConfigs/GameTimeConfigs';
import useGameConfig from '../../hooks/useGameConfig';
import { GAME_TYPE_DETAILS } from '../../constants/gameTypes';
import useCreateGameAction from '../../hooks/useCreateGameAction';
import styles from './PlayTime.style';

const PlayTime = () => {
  const router = useRouter();
  const { isMobile } = useMediaQuery();
  const { gameType } = useLocalSearchParams();
  const { gameConfig, updateGameConfig } = useGameConfig(gameType);
  const { goBack } = useGoBack();
  const [showHowToPlay, setShowHowToPlay] = useState(false);
  const [position, setPosition] = useState(0);
  const isNativeDevice = Platform.OS === 'android' || Platform.OS === 'ios';
  const gameTypeDetail = GAME_TYPE_DETAILS[gameType];

  const { onPressStartPlayingButton, game, creatingGame, error } =
    useCreateGameAction({ gameConfig, selectedGameType: gameType });

  useEffect(() => {
    if (showHowToPlay) {
      setTimeout(() => {
        setPosition(1);
      }, 50);
    } else {
      setPosition(0);
    }
  }, [showHowToPlay]);

  useEffect(() => {
    if (position === 0 && showHowToPlay) {
      const timer = setTimeout(() => {
        setShowHowToPlay(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [position, showHowToPlay]);

  useEffect(() => {
    if (!isMobile) {
      router.replace('/home');
    }
  }, [isMobile, router]);

  const renderGameConfig = useCallback(
    () => (
      <GameTimeConfigs
        gameConfig={gameConfig}
        updateGameConfig={updateGameConfig}
        gameType={gameType}
      />
    ),
    [updateGameConfig, gameConfig, gameType],
  );

  const goBackFromGameConfigPage = useCallback(() => {
    const goBackEvent = ANALYTICS_EVENTS[gameType]?.CLICKED_ON_BACK;
    if (goBackEvent) {
      Analytics.track(goBackEvent, {
        [PAGE_NAME_KEY]: `${_join(_split(_toLower(gameType), '_'), '-')} config page`,
      });
    }
    goBack();
  }, [goBack, gameType]);

  const renderHowToPlayModal = () => {
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <HowToPlay closeBottomSheet={closeBottomSheet} />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.secondary,
        },
      },
    });
  };

  const renderHowToPlayCard = () => (
    <InteractivePrimaryButton
      label="How To Play?"
      labelStyle={styles.howtoplaylabel}
      buttonStyle={styles.howtoplaybutton}
      buttonBorderBackgroundStyle={styles.howtoplaybackground}
      onPress={renderHowToPlayModal}
    />
  );

  return (
    <View style={styles.superContainer}>
      <Header
        goBack={goBackFromGameConfigPage}
        renderTrailingComponent={renderHowToPlayCard}
      />
      <View
        style={[styles.container, { marginTop: isNativeDevice ? '70%' : 80 }]}
      >
        <View style={[styles.gameTypeContainer]}>
          <InnerGameType gameType={gameType} />
        </View>
      </View>
      <View style={styles.bottomContainer}>
        <View
          style={[
            styles.gameConfigContainer,
            !isNativeDevice && { marginBottom: 60 },
          ]}
        >
          {renderGameConfig()}
        </View>
        <View style={styles.playButton}>
          <InteractivePrimaryButton
            onPress={onPressStartPlayingButton}
            label={gameTypeDetail?.ctaLabel}
            buttonStyle={styles.inviteFriendButton}
            labelStyle={styles.buttonText}
            radius={2}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(PlayTime);
