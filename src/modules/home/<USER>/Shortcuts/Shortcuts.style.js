import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    width: '100%',
    paddingHorizontal: 16,
  },
  headerContainer: {
    width: '100%',
    height: 20,
    paddingLeft: 0,
  },
  container: {
    width: '100%',
    justifyContent: 'space-around',
    flexDirection: 'row',
    marginTop: 8,
    gap: 8,
  },
  quickLinksText: {
    letterSpacing: 1,
    fontSize: 10, 
    lineHeight: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
    opacity: 0.5,
  },
});

export default styles;
