import React, { useCallback } from 'react';
import { Image, TouchableOpacity } from 'react-native';

import ResolutionBannerImage from 'assets/images/banners/resolution_banner.png';
import ResolutionBannerDesktopImage from 'assets/images/banners/Desktop/resolution_banner.png';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useRouter } from 'expo-router';
import dark from '../../../../../core/constants/themes/dark';

const BANNER_HEIGHT = {
  get DESKTOP() {
    return 125;
  },
  get MOBILE() {
    return 105;
  },
};

const ResolutionBanner = () => {
  const router = useRouter();
  const { isMobile: isCompactMode } = useMediaQuery();

  const navigateToResolutionPage = useCallback(() => {
    router.push('/resolutions');
  }, [router]);

  return (
    <TouchableOpacity
      onPress={navigateToResolutionPage}
      style={{
        borderRadius: 12,
        overflow: 'hidden',
        width: '100%',
        height: isCompactMode ? BANNER_HEIGHT.MOBILE : BANNER_HEIGHT.DESKTOP,
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
      }}
    >
      <Image
        resizeMode="stretch"
        style={{
          width: '100%',
          height: isCompactMode ? BANNER_HEIGHT.MOBILE : BANNER_HEIGHT.DESKTOP,
        }}
        source={
          isCompactMode ? ResolutionBannerImage : ResolutionBannerDesktopImage
        }
      />
    </TouchableOpacity>
  );
};

export default React.memo(ResolutionBanner);
