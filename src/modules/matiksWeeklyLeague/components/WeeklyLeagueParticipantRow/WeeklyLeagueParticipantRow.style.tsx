import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    // height: 50,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  rankContainer: {
    flex: 0.6,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  userInfoContainer: {
    flex: 3,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  userXPContainer: {
    flex: 1,
  },
  userImage: {
    height: 40,
    width: 40,
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'white',
  },
  rankText: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: 'white',
    width: 28,
    height: 28,
    textAlign: 'center',
  },
  nameText: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    color: 'white',
    textAlign: 'center',
  },
  xPText: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: 'white',
    textAlign: 'center',
  },
});

export default styles;
