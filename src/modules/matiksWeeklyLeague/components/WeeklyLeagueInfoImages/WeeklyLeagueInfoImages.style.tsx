import { Platform, StyleSheet } from 'react-native';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';

const createStyles = (isWeb) =>
  StyleSheet.create({
    headerRow: {
      width: '94%',
      marginTop: 16,
      alignItems: 'flex-end',
    },
    imageContainer: {
      flex: 1,
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    image: {
      width: 80,
      height: 80,
      opacity: 0.4,
      resizeMode: 'center',
    },
    titleText: {
      fontSize: 13,
      color: '#FBDD55',
      fontFamily: 'Montserrat-900',
      lineHeight: 16,
      letterSpacing: 1,
      textAlign: 'center',
    },
    textContainer: {
      marginTop: 10,
      width: '100%',
      alignItems: 'center',
    },
    descriptionText: {
      fontSize: 10,
      lineHeight: 14,
      textAlign: 'center',
      width: '100%',
      fontFamily: 'Montserrat-600',
      color: dark.colors.textDark,
      marginTop: 10,
    },
    leagueNameText: {
      textAlign: 'center',
      fontFamily: 'Montserrat-400',
      fontSize: 14,
      letterSpacing: 4,
      color: dark.colors.leagueText,
    },
  });

const useWeeklyLeagueInfoImagesStyles = () => {
  const isWeb = Platform.OS === 'web';
  const styles = useMemo(() => createStyles(isWeb), [isWeb]);

  return styles;
};

export default useWeeklyLeagueInfoImagesStyles;
