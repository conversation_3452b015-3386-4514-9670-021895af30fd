import { StyleSheet } from "react-native";
import dark from "../../../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    popoverImagePicker: {
        backgroundColor: dark.colors.primary,
        borderRadius: 8,
        width: '50%',
        maxWidth:200,
        position: 'absolute',
        top: 170,
    },
    popoverText: {
        color: 'white',
        fontSize: 12,
        fontFamily:"Montserrat-500",
        marginVertical: 5,
        textAlign: 'center',
    },
    popoverTitle: {
        color: 'white',
        fontSize: 18,
        marginBottom: 10,
        textAlign: 'center',
        fontFamily: 'Montserrat-500',
    },
    choose: {
        alignItems: 'flex-start',
        borderBottomColor: dark.colors.tertiary,
        borderBottomWidth: 1,
        marginTop: -4,
        paddingLeft: 8,
        paddingBottom: 6,
    },
    remove: { 
        alignItems: 'flex-start', 
        paddingLeft: 8 
    },
})

export default styles