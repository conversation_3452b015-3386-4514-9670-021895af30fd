import React, { useEffect, useMemo, useState } from 'react'
import { ScrollView, Text, TouchableOpacity, View } from 'react-native'
import { Dropdown } from 'react-native-element-dropdown'
import useMediaQuery from 'core/hooks/useMediaQuery'
import Header from 'shared/Header'
import useInsightPageStyles from './Insights.style'
import useGetUsersAllPlayedPresets from '../../hooks/query/useGetUserAllPlayedPresets'
import INSIGHTS_TAB_CATEGORIES from './constants/categories'
import StatsGraph from './components/StatsGraph/StatsGraph'
import { getDescriptiveNameFromIdentifier } from '../../../practice/utils/getIdentifierStringFromConfig'
import dark from '../../../../core/constants/themes/dark'
import { withOpacity } from 'core/utils/colorUtils'
import ShimmerView from 'molecules/ShimmerView'
import Loading from 'atoms/Loading'
import ErrorView from 'atoms/ErrorView'
import PropTypes from 'prop-types'
import useUserPresetStatsByDate from '../../../practice/hooks/queries/useUserPresetStatsByDate'
import useGlobalPresetStats from '../../../practice/hooks/queries/useGlobalPresetStats'

const InsightsPage = (props) => {
    const { username } = props
    const styles = useInsightPageStyles()
    const { isMobile: isCompactMode } = useMediaQuery()

    const [selectedCategory, setSelectedCategory] = useState(
        INSIGHTS_TAB_CATEGORIES[0].key
    )
    const [selectedIdentifier, setSelectedIdentifier] = useState(null)
    const [timeRange, setTimeRange] = useState('0')

    const {
        playedPresets,
        loading: presetsLoading,
        error: presetsError,
    } = useGetUsersAllPlayedPresets({
        username: username,
    })

    const {
        presetStats: userStatsData,
        loading: userStatsLoading,
        error: userStatsError,
    } = useUserPresetStatsByDate({
        identifier: selectedIdentifier,
        durationFilter: parseInt(timeRange),
        username: username,
    })

    const {
        presetsStats: globalStatsData,
        loading: globalStatsLoading,
        error: globalStatsError,
    } = useGlobalPresetStats({
        identifier: selectedIdentifier,
    })

    const availableIdentifiers = useMemo(() => {
        if (!playedPresets || !playedPresets[selectedCategory]) return []
        return playedPresets[selectedCategory].map((preset) => ({
            label: getDescriptiveNameFromIdentifier({
                identifier: preset.identifier,
            }),
            value: preset.identifier,
            avgTime: preset.avgTime,
        }))
    }, [playedPresets, selectedCategory])

    const dropdownData = {
        timeRanges: [
            { label: 'All time', value: '0' },
            { label: 'Last 7 days', value: '7' },
            { label: 'Last 30 days', value: '30' },
            { label: 'Last 6 months', value: '180' },
        ],
    }

    useEffect(() => {
        setSelectedIdentifier(null)
        if (availableIdentifiers.length > 0) {
            setSelectedIdentifier(availableIdentifiers[0].value)
        }
    }, [selectedCategory, availableIdentifiers])

    const renderCategorySelection = () => {
        if (!isCompactMode) {
            return null
        }

        return (
            <ScrollView
                contentContainerStyle={styles.tabContainer}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
            >
                {INSIGHTS_TAB_CATEGORIES.map((category) => (
                    <TouchableOpacity
                        key={category.key}
                        style={[
                            styles.tabItem,
                            selectedCategory === category.key &&
                                styles.activeTab,
                        ]}
                        onPress={() => setSelectedCategory(category.key)}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                selectedCategory === category.key &&
                                    styles.activeTabText,
                            ]}
                        >
                            {category.title}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        )
    }

    if (presetsLoading) {
        return <Loading label={'Loading Stats'} />
    }

    if (userStatsError || globalStatsError || presetsError) {
        return <ErrorView errorMessage={'Failed to fetch Data '} />
    }

    return (
        <View style={{ flex: 1, alignItems: 'center', overflow: 'hidden' }}>
            <Header title={'Insights'} />
            <ScrollView
                style={[
                    styles.container,
                    !isCompactMode && { maxWidth: '70%', paddingTop: 20 },
                ]}
                showsVerticalScrollIndicator={false}
            >
                {renderCategorySelection()}

                <View style={styles.dropdownContainer}>
                    {!isCompactMode && (
                        <Dropdown
                            data={INSIGHTS_TAB_CATEGORIES.map((cat) => ({
                                label: cat.title,
                                value: cat.key,
                            }))}
                            labelField="label"
                            valueField="value"
                            value={selectedCategory}
                            onChange={(item) => setSelectedCategory(item.value)}
                            style={styles.dropdown}
                            selectedTextStyle={styles.selectedTextStyle}
                            containerStyle={styles.dropdownContainerItem}
                            placeholderStyle={styles.placeholderStyle}
                            itemTextStyle={styles.label}
                            activeColor={withOpacity(
                                dark.colors.background,
                                0.7
                            )}
                        />
                    )}
                    <Dropdown
                        data={availableIdentifiers}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Preset"
                        value={selectedIdentifier}
                        selectedTextStyle={styles.selectedTextStyle}
                        onChange={(item) => setSelectedIdentifier(item.value)}
                        style={styles.dropdown}
                        containerStyle={styles.dropdownContainerItem}
                        placeholderStyle={styles.placeholderStyle}
                        itemTextStyle={styles.label}
                        activeColor={withOpacity(dark.colors.background, 0.7)}
                    />

                    <Dropdown
                        data={dropdownData.timeRanges}
                        labelField="label"
                        valueField="value"
                        placeholder="Time Range"
                        value={timeRange}
                        onChange={(item) => setTimeRange(item.value)}
                        style={styles.dropdown}
                        selectedTextStyle={styles.selectedTextStyle}
                        containerStyle={styles.dropdownContainerItem}
                        placeholderStyle={styles.placeholderStyle}
                        itemTextStyle={styles.label}
                        activeColor={withOpacity(dark.colors.background, 0.7)}
                    />
                </View>

                {selectedIdentifier &&
                (globalStatsLoading === true || userStatsLoading === true) ? (
                    <View>
                        <ShimmerView
                            shimmerColors={dark.colors.placeholderShimmerColors}
                            style={{
                                height: 150,
                                borderColor: dark.colors.tertiary,
                                borderWidth: 1,
                                borderRadius: 12,
                                width: '90%',
                                marginTop: 15,
                                marginHorizontal: 16,
                            }}
                        />
                        <ShimmerView
                            shimmerColors={dark.colors.placeholderShimmerColors}
                            style={{
                                height: 150,
                                borderColor: dark.colors.tertiary,
                                borderWidth: 1,
                                borderRadius: 12,
                                width: '90%',
                                marginTop: 15,
                                marginHorizontal: 16,
                            }}
                        />
                    </View>
                ) : (
                    <View style={styles.graphsSection}>
                        <StatsGraph
                            userStatsData={userStatsData}
                            globalStatsData={globalStatsData}
                        />
                    </View>
                )}
            </ScrollView>
        </View>
    )
}

InsightsPage.propTypes = {
    username: PropTypes.string,
}

export default InsightsPage
