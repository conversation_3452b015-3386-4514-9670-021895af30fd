import _isNil from 'lodash/isNil';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';

const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

const useStreakAnalytics = () => {
  const { user } = useSession();
  const personName = user?.name;
  const userStreaks = userReader.userStreaks(user) ?? EMPTY_OBJECT;

  const streakFreezers = userReader.streakFreezers(user);

  const { currentStreak = 0, longestStreak = 0 } = userStreaks ?? {};

  let { lastSevenDays = [false, false, false, false, false, false, false] } =
    userStreaks;

  if (_isNil(lastSevenDays)) {
    lastSevenDays = [false, false, false, false, false, false, false];
  }
  const currentStreakCount = currentStreak;

  const maxStreakCount = longestStreak;

  const today = new Date(getCurrentTime());
  const currentDayIndex = today.getDay();

  const streakStatus = weekDays.map((_, index) => {
    if (index > currentDayIndex) {
      return false;
    }
    const daysAgo = currentDayIndex - index;
    const lastSevenDaysIndex = 6 - daysAgo;
    const defaultStreakStatus = daysAgo + 1 <= currentStreakCount;
    return (
      (lastSevenDaysIndex >= 0 ? lastSevenDays?.[lastSevenDaysIndex] : false) ??
      defaultStreakStatus
    );
  });

  const lastFiveWeekDays = [];
  const lastFiveDaysStreakStatus = [];

  for (let i = 4; i >= 0; i--) {
    const dayIndex = (currentDayIndex - i + 7) % 7;
    lastFiveWeekDays.push(weekDays[dayIndex]);
    lastFiveDaysStreakStatus.push(lastSevenDays[6 - i]);
  }

  return {
    currentStreakCount,
    weekDays,
    maxStreakCount,
    streakStatus,
    personName,
    lastFiveDaysStreakStatus,
    lastFiveWeekDays,
    currentDayIndex,
    hasStreak: lastSevenDays[6], // Assumes that the last day is the current day
    streakFreezers,
  };
};

export default useStreakAnalytics;
