import { gql, useLazyQuery } from '@apollo/client';
import { useCallback, useEffect, useState } from 'react';
import _debounce from 'lodash/debounce';
import _size from 'lodash/size';
import _trim from 'lodash/trim';

export const SEARCH_INSTITUTIONS_QUERY = gql`
  query SearchInstitutions($query: String!, $limit: Int = 10) {
    searchInstitutions(query: $query, limit: $limit) {
      id
      name
      domains
      country
      state
      city
      slug
    }
  }
`;

const DEBOUNCE_DELAY = 500;

const useSearchInstitutions = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInstitutions, { loading, error, data }] = useLazyQuery(
    SEARCH_INSTITUTIONS_QUERY,
    {
      fetchPolicy: 'cache-and-network',
    },
  );

  const debouncedSearch = useCallback(
    _debounce((query) => {
      if (query && _size(_trim(query)) > 2) {
        searchInstitutions({ variables: { query } });
      }
    }, DEBOUNCE_DELAY),
    [searchInstitutions],
  );

  useEffect(() => {
    debouncedSearch(searchTerm);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, debouncedSearch]);

  const institutions = data?.searchInstitutions || [];

  return {
    searchTerm,
    setSearchTerm,
    institutions,
    loading,
    error,
  };
};

export default useSearchInstitutions;
