import { gql, useQuery } from '@apollo/client';
import _get from 'lodash/get';

const EMPTY_ARRAY = [];

const GET_USER_STREAK_HISTORY_BY_MONTH = gql`
  query GetUserStreakHistoryByMonth($yearMonths: [String!]!) {
    getUserStreakHistoryByMonth(yearMonths: $yearMonths) {
      date
      isShieldUsed
    }
  }
`;

const useGetUserStreakHistory = (yearMonths) => {
  const { data, loading, error } = useQuery(GET_USER_STREAK_HISTORY_BY_MONTH, {
    variables: { yearMonths },
    fetchPolicy: 'cache-and-network',
  });

  return {
    streakHistory: _get(data, 'getUserStreakHistoryByMonth', EMPTY_ARRAY),
    loading,
    error,
  };
};

export default useGetUserStreakHistory;
