import { gql, useMutation } from "@apollo/client"
import { useCallback } from "react"
import { base64ToFile } from "core/utils/base64DataToFile"

const EMPTY_OBJECT = {};

const UPLOAD_PROFILE_PICTURE = gql`
  mutation UploadProfilePicture($file: Upload!) {
    uploadProfilePicture(file: $file) {
      url
    }
  }
`

const useUploadProfilePicture = () => {

  const [uploadProfilePicture, {loading, error }] = useMutation(
    UPLOAD_PROFILE_PICTURE
  )

  const uploadImage = useCallback(async ({ fileUri }) => {
    try {
      const imageFile = await base64ToFile({ fileUri: fileUri, fileName: "profile_image.jpeg" })
      const response = await uploadProfilePicture({
        variables: {
          file: imageFile
        }
      })

      const { uploadProfilePicture:uploadProfilePictureRes } = response?.data ?? EMPTY_OBJECT

      const { url } = uploadProfilePictureRes ?? EMPTY_OBJECT

      return url

    } catch (err) {
      console.error("Upload error:", err)
      throw err
    }

  }, [uploadProfilePicture])

  return {
    uploadImage,
    loading,
    error,
  }
}

export default useUploadProfilePicture