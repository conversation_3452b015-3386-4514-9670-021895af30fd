import { StyleSheet } from 'react-native';
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  background: {
    width: '100%',
    borderBottomEndRadius: 12,
    borderBottomLeftRadius: 12,
    backgroundColor: dark.colors.gradientBackground,
    justifyContent: 'center',
    alignItems: 'flex-start',

  },
  innerCard: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'column',
    alignItems: 'flex-start',

  },
  card: {
    flex: 1,
    paddingHorizontal: 2,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
    borderRadius: 12,
  },
  imageContainer: {
    position: 'absolute',
    left: 24,
    top: -45,
    marginHorizontal: 1,
    zIndex: 20,
    borderRadius: 50,
    borderWidth: 6,
    borderColor: dark.colors.background,
  },
  userImage: {
    height: 72,
    width: 72,
    borderRadius: 50,
    overflow: 'hidden',
  },
  bio: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: 12,
  },
  badge: {
    width: 36,
    height: 36,
  },
  badgeContainer: {
    position: 'absolute',
    bottom: -10,
    right: -10,
  },
  badgeTitle: {
    fontFamily: 'Montserrat-700',
    fontSize: 10,
  },
  userInfo: {
    alignItems: 'flex-start',
    paddingLeft: 16,
    marginTop: 60,
  },
  userName: {
    fontFamily: 'Montserrat-600',
    fontSize: 20,
    color: 'white',
    lineHeight: 24,
  },
  username: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: dark.colors.textDark,
    lineHeight: 16,
    marginBottom: 3,
  },
  userRating: {
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
  editConatiner: {
    flexDirection: 'row',
    gap: 5,
    alignItems: 'center',
  },
  editText: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    lineHeight: 20,
    color: dark.colors.secondary,
  },
  followAndChallengeButtonRows: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    marginTop: 34,
    gap: 8,
  },
  followAndFriendsButtonRow: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    gap: 8,

    marginHorizontal: 14,
    marginBottom: 20,
    marginTop: 14
  },
  friendsAndFollowersText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.secondary,
    marginBottom: 3,
  },
  friendsSection: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  friendsAndFollowersText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.secondary,
    marginBottom: 3,
  },
  friendsStatusText: {
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-700',
    fontSize: 12,
  },
});

export default styles;
