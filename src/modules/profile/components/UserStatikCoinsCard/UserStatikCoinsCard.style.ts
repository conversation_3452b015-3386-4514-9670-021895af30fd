import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  streakCardStyle: {
    minWidth: 41,
    height: 38,
    justifyContent: 'center',
    backgroundColor: dark.colors.primary,
    paddingLeft: 10,
    paddingRight: 12,
    borderRadius: 20,
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
  },
  streakCardTextStyle: {
    color: 'white',
    textAlign: 'center',
    fontFamily: 'Montserrat-800',
    fontSize: 12,
  },
  innerStreakContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
});

export default styles;
