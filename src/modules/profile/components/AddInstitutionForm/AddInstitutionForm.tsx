import React, { useCallback, useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import _trim from 'lodash/trim';
import _isEmpty from 'lodash/isEmpty';
import usePickAndCompressImage from 'core/hooks/usePickAndCompressImage';
import DefaultCollegeIcon from '@/src/components/atoms/DefaultCollegeIcon';
import { base64ToFile } from 'core/utils/base64DataToFile';
import styles from './AddInstitutionForm.style';
import useCreateInstitution from '../../hooks/query/useCreateInstitution';

const INSTITUTION_TYPES = {
  COLLEGE: 'COLLEGE',
  SCHOOL: 'SCHOOL',
};

const AddInstitutionForm = ({
  onInstitutionCreated,
  instituteName,
}: {
  onInstitutionCreated: Function;
  instituteName: any;
}) => {
  const [name, setName] = useState(instituteName ?? '');
  const [institutionType, setInstitutionType] = useState(
    INSTITUTION_TYPES.COLLEGE,
  );
  const [logoUri, setLogoUri] = useState<string | null>(null);
  const [formError, setFormError] = useState('');

  const { isLoading: isLogoLoading, pickAndCompressImage } =
    usePickAndCompressImage();

  const {
    createInstitution,
    loading,
    error: mutationError,
  } = useCreateInstitution();

  const handleSubmitPress = useCallback(async () => {
    setFormError('');
    if (_isEmpty(_trim(name))) {
      setFormError('Institution name is required.');
      return;
    }
    let logoUriFile = null;
    if (logoUri) {
      logoUriFile = await base64ToFile({
        fileUri: logoUri,
        fileName: 'logo_image.jpeg',
      });
    }

    const input = {
      name: _trim(name),
      type: institutionType,
      logo: logoUriFile,
    };

    const newInstitution = await createInstitution(input);

    if (newInstitution) {
      setName('');
      setLogoUri(null);
      setInstitutionType(INSTITUTION_TYPES.COLLEGE);

      if (onInstitutionCreated) {
        onInstitutionCreated(newInstitution);
      }
    }
  }, [name, institutionType, logoUri, createInstitution, onInstitutionCreated]);

  const handleSelectLogo = useCallback(async () => {
    try {
      const uri = await pickAndCompressImage();
      if (uri) {
        setLogoUri(uri);
      }
    } catch (e) {
      setLogoUri(null);
      // console.info('Image picking cancelled or failed:', e);
    }
  }, [pickAndCompressImage]);

  const renderError = () => {
    const error = formError || mutationError?.message;
    if (!error) return null;
    return <Text style={styles.errorText}>{error}</Text>;
  };

  return (
    <View style={styles.fullContainer}>
      <Text style={styles.titleText}>Enter Details</Text>
      {renderError()}

      <View style={styles.fieldContainer}>
        <Text style={styles.label}>Select Type</Text>
        <View style={styles.typeSelectionContainer}>
          <TouchableOpacity
            style={[
              styles.typeButton,
              institutionType === INSTITUTION_TYPES.COLLEGE &&
                styles.typeButtonSelected,
            ]}
            onPress={() => setInstitutionType(INSTITUTION_TYPES.COLLEGE)}
            disabled={loading}
          >
            <Text
              style={[
                styles.typeButtonText,
                institutionType === INSTITUTION_TYPES.COLLEGE &&
                  styles.typeButtonTextSelected,
              ]}
            >
              College/Institute
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.typeButton,
              institutionType === INSTITUTION_TYPES.SCHOOL &&
                styles.typeButtonSelected,
            ]}
            onPress={() => setInstitutionType(INSTITUTION_TYPES.SCHOOL)}
            disabled={loading}
          >
            <Text
              style={[
                styles.typeButtonText,
                institutionType === INSTITUTION_TYPES.SCHOOL &&
                  styles.typeButtonTextSelected,
              ]}
            >
              School
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.logoSectionContainer}>
        <TouchableOpacity
          onPress={handleSelectLogo}
          disabled={isLogoLoading || loading}
        >
          {logoUri ? (
            <Image source={{ uri: logoUri }} style={styles.logoPlaceholder} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <DefaultCollegeIcon size={40} />
            </View>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleSelectLogo}
          disabled={isLogoLoading || loading}
        >
          <Text style={styles.uploadLogoText}>
            {isLogoLoading
              ? 'Loading...'
              : logoUri
                ? 'Change Logo'
                : 'Upload Logo'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.fieldContainer}>
        <Text style={styles.label}>
          {institutionType === INSTITUTION_TYPES.COLLEGE
            ? 'College/Institute'
            : 'School'}{' '}
          Name <Text style={{ color: dark.colors.defeatColor }}>*</Text>
        </Text>
        <TextInput
          style={styles.input}
          placeholder={`Enter ${
            institutionType === INSTITUTION_TYPES.COLLEGE
              ? 'college/institute'
              : 'school'
          } name`}
          placeholderTextColor={dark.colors.placeholder}
          value={name}
          onChangeText={setName}
          editable={!loading}
        />
      </View>

      <View style={{ paddingVertical: 12, marginTop: 10 }}>
        <InteractiveSecondaryButton
          label={loading ? 'ADDING...' : `ADD ${institutionType.toUpperCase()}`}
          borderColor={dark.colors.secondary}
          buttonContentStyle={{
            paddingHorizontal: 0,
            paddingVertical: 0,
          }}
          buttonContainerStyle={{ height: 48 }}
          labelStyle={{
            color: dark.colors.textLight,
            fontSize: 12,
            fontFamily: 'Montserrat-800',
          }}
          onPress={handleSubmitPress}
          disabled={loading}
        />
      </View>
    </View>
  );
};

export default React.memo(AddInstitutionForm);
