import PropTypes from "prop-types"
import styles from "./IconWithRoundedBorder.style"
import _Nil from "lodash/isNil"
import { TouchableOpacity } from "react-native"
import React from "react"

const IconWithRoundedBorder = (props) => {
    const { renderIconComponent, onPress } = props

    if(_Nil(renderIconComponent)){
        return null
    }

    return (<TouchableOpacity style={styles.container} onPress={onPress}>
        {renderIconComponent()}
    </TouchableOpacity>)
}

IconWithRoundedBorder.propTypes = {
    renderIconComponent: PropTypes.func,
    onPress: PropTypes.func
}

export default React.memo(IconWithRoundedBorder)