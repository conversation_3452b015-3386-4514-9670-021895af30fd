import React, { useCallback } from 'react';
import {
  Image,
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import TextureImage from '@/assets/images/backgrounds/contests_tab_bg.png';
import { useRouter } from 'expo-router';
import matiksLogo from 'assets/images/LinearGradientIcons/matiksBolt.png';
import LinearGradient from 'atoms/LinearGradient';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import leagueReader from 'modules/league/readers/leagueReader';
import { getLeaguePropertiesToTrack } from '../../../../utils/leagueEvents';
import useLeagueCardStyles from './LeagueCard.style';

const LeagueCard = (props) => {
  const { leagueId, registered, isCompleted, title, leagueDetails } =
    props ?? EMPTY_OBJECT;

  const styles = useLeagueCardStyles();
  const router = useRouter();

  const timeLeftToStart = useCountDownTimer({
    targetTime: leagueDetails?.startTime,
  });

  const onPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.LEAGUES.CLICKED_ON_LEAGUE_CARD, {
      ...getLeaguePropertiesToTrack({ league: leagueDetails }),
      [PAGE_NAME_KEY]: PAGE_NAMES.LEAGUES,
    });
    router.push(`/league/${leagueId}`);
  }, [router, leagueId, leagueDetails]);

  const renderIconContainer = () => {
    const iconImage = leagueReader.hostedByLogo(leagueDetails);
    return (
      <View style={styles.iconContainer}>
        <Image
          source={iconImage ? { uri: iconImage } : matiksLogo}
          style={[styles.logoStyle, iconImage && styles.logoImage]}
        />
      </View>
    );
  };

  const leagueParticipationLabel =
    isCompleted && !registered
      ? 'Ended'
      : registered && isCompleted
        ? 'Participated'
        : !isCompleted && registered
          ? `Starts in ${timeLeftToStart}`
          : 'Registration Open';

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <ImageBackground
        source={TextureImage}
        style={styles.imageContainer}
        resizeMode="stretch"
      >
        {renderIconContainer()}
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
        <Text
          style={styles.hostedBy}
          numberOfLines={1}
        >{`by ${leagueDetails?.hostedBy ?? 'Matiks'}`}</Text>
        <Text style={styles.dateTime} numberOfLines={1}>
          {new Date(leagueDetails.leagueStart).toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}{' '}
          |{' '}
          {new Date(leagueDetails.leagueStart).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
          })}{' '}
        </Text>
        <LinearGradient
          colors={['#1F7A3F12', '#2BAD4E88', '#1F7A3F12']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            width: '90%',
            height: 20,
            marginTop: 11,
            paddingHorizontal: 24,
            paddingVertical: 1.5,
            justifyContent: 'center',
            alignItem: 'center',
          }}
        >
          <Text
            style={{
              color: 'white',
              fontFamily: 'Montserrat-500',
              fontSize: 10,
              textAlign: 'center',
            }}
          >
            {leagueParticipationLabel}
          </Text>
        </LinearGradient>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default React.memo(LeagueCard);
