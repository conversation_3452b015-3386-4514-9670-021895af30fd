import { gql, useQuery } from '@apollo/client';

const GET_LEAGUES_BY_STATUS = gql`
  query GetLeaguesByStatus(
    $statuses: [LeagueStatus!]!
    $page: Int
    $pageSize: Int
    $sortDirection: String
  ) {
    getLeaguesByStatus(
      statuses: $statuses
      page: $page
      pageSize: $pageSize
      sortDirection: $sortDirection
    ) {
      league {
        id
        name
        hostedBy
        registrationStart
        registrationEnd
        leagueStart
        leagueEnd
        hostedByV2 {
          name
          logo
        }
      }
      totalCount
    }
  }
`;

const useGetLeaguesByStatus = ({ statuses }) => {
  const { data, error, loading, refetch } = useQuery(GET_LEAGUES_BY_STATUS, {
    variables: {
      statuses,
      sortDirection: 'desc',
    },
  });

  return {
    leagues: data?.getLeaguesByStatus,
    error,
    loading,
    refetch,
  };
};

export default useGetLeaguesByStatus;
