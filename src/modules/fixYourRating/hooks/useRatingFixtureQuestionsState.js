import _reduce from 'lodash/reduce'
import _isEqual from 'lodash/isEqual'
import _isEmpty from 'lodash/isEmpty'
import _compact from 'lodash/compact'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import _head from 'lodash/head'
import _keys from 'lodash/keys'
import _find from 'lodash/find'
import _findIndex from 'lodash/findIndex'
import _values from 'lodash/values'
import _toString from 'lodash/toString'
import _map from 'lodash/map'
import useSubmitRatingFixtureMutation from './graphql/useSubmitRatingFixtureMutation'
import { useSession } from '../../auth/containers/AuthProvider'

let submittedRatingFixture = false

const GAME_TIME = 60

const useRatingFixtureQuestionsState = ({ questions: initialQuestions }) => {
    const [timeSpent, setTimeSpent] = useState(0)
    const { user } = useSession()
    const { hasFixedRating = false } = user
    const { submitRatingFixtureResponses, loading: submittingUserResponse } = useSubmitRatingFixtureMutation()

    const [userSubmission, setUserSubmission] = useState();

    const initialValue = _map(initialQuestions, (questionObject) => ({
        ...questionObject,
        hasSolved: false,
        totalAttempts: 0,
    }))

    const [questions, setQuestions] = useState(initialValue)

    const questionsMap = useMemo(
        () =>
            _reduce(
                questions,
                (acc, questionObj) => ({
                    ...acc,
                    [questionObj?.id]: questionObj,
                }),
                {}
            ),
        [questions]
    )

    const currentScore = useMemo(
        () =>
            _reduce(
                questions,
                (acc, { hasSolved }) => acc + (hasSolved ? 1 : 0),
                0
            ),
        [questions]
    )

    const updateQuestion = useCallback(
        ({ qid, value }) => {
            setQuestions((prevQuestions) =>
                _map(prevQuestions, (q) =>
                    q.id === qid ? { ...q, ...value } : q
                )
            )
        },
        [setQuestions]
    )

    const [currentQuestionId, setCurrentQuestionId] = useState(
        _head(_map(questions, 'id'))
    )

    const [solvedAllQuestions, setSolvedAllQuestions] = useState(false)

    const updateCurrentQuestion = useCallback(() => {
        const firstUnSolvedQuestionIndex = _findIndex(
            questions,
            (q) => !q.hasSolved
        )

        if (firstUnSolvedQuestionIndex === -1) {
            setSolvedAllQuestions(true)
            return
        }

        setCurrentQuestionId(questions[firstUnSolvedQuestionIndex].id)
    }, [setSolvedAllQuestions, questions])

    const submitAnswer = useCallback(
        ({ questionId, value, timeTaken }) => {
            const originalQuestion = questionsMap[questionId]
            const { answers } = originalQuestion
            if (_isEqual(_toString(answers[0]), _toString(value))) {
                updateQuestion({
                    qid: questionId,
                    value: {
                        hasSolved: true,
                        timeTaken,
                    },
                })
            }
        },
        [updateQuestion, questionsMap, updateCurrentQuestion]
    )

    const totalTimeTaken = useMemo(
        () =>
            _reduce(questions, (acc, { timeTaken = 0 }) => acc + timeTaken, 0),
        [questions]
    )

    const onSubmitAllAnswers = useCallback(() => {
        const submittedTimes = _compact(_map(questions, 'timeTaken'))
        submitRatingFixtureResponses({
            submission: submittedTimes,
            timeTaken: timeSpent,
        }).then(response => {
            const submittedResponse = response?.data?.submitRatingFixtureResponses;
            setUserSubmission(submittedResponse);
        })
    }, [questions, submitRatingFixtureResponses, timeSpent])

    const onSubmitAllAnswersRef = useRef(onSubmitAllAnswers)
    onSubmitAllAnswersRef.current = onSubmitAllAnswers

    const updateCurrentQuestionRef = useRef(updateCurrentQuestion)
    updateCurrentQuestionRef.current = updateCurrentQuestion

    useEffect(() => {
        updateCurrentQuestionRef.current()
    }, [questions])

    const timesUp = timeSpent >= GAME_TIME
    useEffect(() => {
        if (
            (solvedAllQuestions || timesUp) &&
            !hasFixedRating &&
            !submittedRatingFixture
        ) {
            submittedRatingFixture = true
            onSubmitAllAnswersRef.current()
        }
    }, [solvedAllQuestions, timesUp, hasFixedRating])

    useEffect(() => {
        const intervalId = setInterval(
            () => setTimeSpent((prevTime) => Math.min(GAME_TIME, prevTime + 1)),
            1000
        )
        return () => clearInterval(intervalId)
    }, [setTimeSpent])

    return {
        currentQuestionId,
        submitAnswer,
        currentScore,
        currentQuestion: questionsMap[currentQuestionId],
        solvedAllQuestions,
        timeSpent,
        gameEnded: submittedRatingFixture,
        submittingUserResponse,
        userSubmission
    }
}

export default useRatingFixtureQuestionsState
