import React from 'react';
import { Text } from 'react-native';
import styles from './InfoIcon.style';
import { TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';

const InfoIcon = (props) => {
  const { onPress } = props

  return (
    <TouchableOpacity onPress={onPress}>
      <Text style={styles.icon}>ⓘ</Text>
    </TouchableOpacity>
  );
};

InfoIcon.propTypes = {
  onPress: PropTypes.func
}

export default React.memo(InfoIcon);