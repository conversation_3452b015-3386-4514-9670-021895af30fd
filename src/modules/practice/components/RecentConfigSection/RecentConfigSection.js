import React, {useCallback, useEffect, useState} from "react"
import { Dimensions, ScrollView, Text, View } from "react-native"
import { TabView, TabBar } from 'react-native-tab-view'
import { RECENT_CONFIG_TAB_KEYS } from "../../constants/tabsKeys"
import RecentConfigTab from "./RecentConfigTab"
import SavedConfigTab from "./SavedConfigTab"
import useRecentConfigSectionStyles from "./RecentConfigSection.style"
import dark from "../../../../core/constants/themes/dark"
import useMediaQuery from 'core/hooks/useMediaQuery'
import PropTypes from "prop-types"
import Analytics from "../../../../core/analytics";
import {ANALYTICS_EVENTS} from "../../../../core/analytics/const";

const initialLayout = { width: Dimensions.get('window').width }

const RecentConfigSection = (props) => {
    const { controller } = props
    const styles = useRecentConfigSectionStyles()
    const { isMobile: isCompactMode } = useMediaQuery()
    const [index, setIndex] = useState(0)

    const routes = [
        { key: RECENT_CONFIG_TAB_KEYS.RECENT, title: 'Recent' },
        { key: RECENT_CONFIG_TAB_KEYS.SAVED, title: 'Saved' },
    ]

    const onIndexChange = useCallback((updatedIndex) => {
        setIndex(updatedIndex);
        const event = updatedIndex === 1 ? ANALYTICS_EVENTS.PRACTICE_MODULE.CLICKED_ON_SAVED_TAB : ANALYTICS_EVENTS.PRACTICE_MODULE.CLICKED_ON_RECENT_TAB;
        Analytics.track(event);
    }, [setIndex]);

    const renderScene = useCallback(({ route, jumpTo }) => {
        switch (route.key) {
            case 'recent':
                return (<RecentConfigTab controller={controller} />)
            case 'saved':
                return (<SavedConfigTab controller={controller} />)
        }
    }, [])

    useEffect(() => {
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.VISITED_RECENT_AND_SAVED_PAGE)
    }, []);

    const renderTabBar = useCallback((props) => (
        <>
            <TabBar
                {...props}
                indicatorStyle={styles.indicator}
                style={styles.tabBar}
                tabStyle={styles.tabStyle}
                labelStyle={styles.label}
                activeColor={dark.colors.secondary}
                inactiveColor={dark.colors.textDark}
                renderLabel={({ route, focused, color }) => (
                    <Text style={{ ...styles.label, color: color }}>
                        {route.title}
                    </Text>
                )}
            />
            <View style={styles.fullWidthLine} />
        </>
    ), [])



    return (
        <View style={{
            flex: isCompactMode ? 8 : 5,
        }}>
            <TabView
                navigationState={{ index, routes }}
                renderScene={renderScene}
                onIndexChange={onIndexChange}
                initialLayout={initialLayout}
                renderTabBar={renderTabBar}
            />
        </View>
    )
}

RecentConfigSection.propTypes = {
    controller: PropTypes.object
}

export default React.memo(RecentConfigSection)