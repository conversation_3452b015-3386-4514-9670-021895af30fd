import { Overlay } from "@rneui/themed"
import PropTypes from "prop-types";
import { TouchableOpacity } from "react-native";
import { Text,View } from "react-native";
import PrimaryButton from 'atoms/PrimaryButton'
import dark from "../../../../core/constants/themes/dark";
import React, { useCallback } from "react";


const UpdateConfigModal = (props) => {

    const { isVisible, onClose } = props

    return (
        <Overlay overlayStyle={{ backgroundColor: dark.colors.primary, borderRadius: 16, borderWidth: 1, borderColor: dark.colors.tertiary, marginHorizontal: 16, gap: 10, paddingHorizontal: 20, paddingVertical: 18 }} isVisible={isVisible}>
            <Text style={{ fontSize: 17, fontFamily: "Montserrat-500", lineHeight: 24, color: 'white' }}>
                Confirm Action
            </Text>
            <Text style={{ fontSize: 14, fontFamily: "Montserrat-400", lineHeight: 20, color: 'white' }}>
            Please note that any changes made will be reflected in the preset already added
            </Text>
            <View style={{justifyContent:'flex-end',alignItems:'flex-end'}}>
            <PrimaryButton
                buttonStyle={{ height: 40, width: 120 ,marginTop:10}}
                label={'Understood'}
                radius={'xl'}
                onPress={onClose}
            />
            </View>
        </Overlay>
    )
}

UpdateConfigModal.propTypes = {
    isVisible: PropTypes.bool,
    onClose: PropTypes.func
}

export default React.memo(UpdateConfigModal)