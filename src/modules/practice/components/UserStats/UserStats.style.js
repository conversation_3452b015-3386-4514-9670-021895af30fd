import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 16,
    },
    headerText: {
        fontSize: 12,
        lineHeight: 14,
        fontFamily: "Montserrat-600",
        color: dark.colors.textDark,
        marginVertical:16
    },
    durationOuterContainer: {
        flexDirection: "row",
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        borderRadius: 8,
        backgroundColor: dark.colors.primary,
        alignItems: "center",
        marginTop: 8,
        marginBottom: 16,
        marginHorizontal: 16,
        overflow: "hidden"
    },
    durationContainer: {
        flex: 1,
        height: 32,
        justifyContent: "center",
        borderRightColor: dark.colors.tertiary,
        borderRightWidth: 1,
    },
    durationLabelText: {
        fontSize: 11,
        lineHeight: 20,
        fontFamily: "Montserrat-500",
        textAlign: "center",
        color: dark.colors.textDark,
        letterSpacing: 1
    },
    titleText: {
        fontSize: 15,
        lineHeight: 24,
        fontFamily: "Montserrat-500",
        color: 'white',
    },
    headerRow: {
        justifyContent: "space-between",
        marginVertical: 16,
        marginHorizontal:16,
        flexDirection: "row",
    }
})

export default styles