import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";
import dark from "core/constants/themes/dark";

const createStyles = () => StyleSheet.create({
    container: {
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 12,
        flex: 1,
    },
    valueText: {
        fontSize: 19,
        fontFamily: "Montserrat-700",
        lineHeight: 28,
        color: "white"
    },
    labelText: {
        fontFamily: 'Montserrat-500',
        fontSize: 12,
        lineHeight: 20,
        color: dark.colors.textDark
    }
})

const useCardWithLabelAndValueStyles = () => {
    const { isMobile } = useMediaQuery()
    const styles = useMemo(() => createStyles(isMobile), [isMobile])
    return styles
}

export default useCardWithLabelAndValueStyles