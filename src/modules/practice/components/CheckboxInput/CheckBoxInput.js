import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';
import AntDesign from '@expo/vector-icons/AntDesign';
import PropTypes from 'prop-types';
import styles from './CheckboxInput.style';

const CheckboxInput = (props) => {
    const { label, value, onValueChange } = props
    return (
        <TouchableOpacity style={styles.container} onPress={() => onValueChange(!value)}>
            <Text style={styles.label}>{label}</Text>
            <View style={[styles.checkbox, value && styles.checked]} >
                {value && (<AntDesign name="check" size={15} color={dark.colors.tertiary} />)}
            </View>
        </TouchableOpacity>
    );
};

CheckboxInput.propTypes = { 
    label: PropTypes.string,
    value: PropTypes.bool,
    onValueChange: PropTypes.func
}

export default React.memo(CheckboxInput);