import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        paddingVertical: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBlockColor: dark.colors.tertiary,
        borderBottomWidth: 1,
    },
    label: {
        fontSize: 12,
        fontFamily: 'Montserrat-500',
        color: '#FFFFFF',
    },
    checkbox: {
        width: 20,
        height: 20,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
        marginRight: 30,
        borderRadius: 3
    },
    checked: {
        borderColor: dark.colors.tertiary,
        backgroundColor: dark.colors.secondary
    },
});

export default styles