import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'
import dark from '../../../../core/constants/themes/dark';

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        width: "100%",
        borderColor: dark.colors.tertiary,
        borderRadius: 12,
        borderWidth: 1,
        paddingHorizontal: 14,
        paddingVertical: 12,
        gap: 12,
        marginBottom: isCompactMode ? 0 : 15,
        maxWidth: 292,
    },
    timerIconStyle: {
        width: 15,
        height: 15
    },
    label: {
        fontFamily: 'Montserrat-800',
        fontSize: 11,
        lineHeight: 16,
        color: "white",
    },
    categoryText: {
        fontSize: 10,
        fontFamily: "Montserrat-600",
        lineHeight: 13,
        color:dark.colors.textDark
    },
    addText: {
        color: dark.colors.secondary,
        fontSize: 10,
        fontFamily: 'Montserrat-600'
    },
    addButton: {
        width: 64,
        height: 20,
        borderColor: dark.colors.tertiary,
        borderRadius: 20,
        borderWidth: 1,
        justifyContent: "center",
        alignItems: "center"
    },
})

const useRecentConfigCardStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useRecentConfigCardStyles
