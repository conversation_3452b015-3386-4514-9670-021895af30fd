import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, View } from 'react-native';
import usePrevious from '@/src/core/hooks/usePrevious';
import _toString from 'lodash/toString';
import useMediaQuery from 'core/hooks/useMediaQuery';
import PropTypes from 'prop-types';
import { SOLUTION_STATUS } from '@/src/modules/game/pages/PlayGame/Footer/AnswerEvaluators';
import dark from 'core/constants/themes/dark';
import styles from './PracticeFooter.style';
import CustomTextInput from '../../../../components/shared/CustomTextInput';

const screenWidth = Dimensions.get('window').width;

const PracticeFooter = (props) => {
  const {
    question = {},
    submitAnswerRef,
    typingDirection = 'ltr',
    focusAgain,
    answer,
  } = props;

  const [value, setValue] = useState('');
  const [incorrectAttempts, setIncorrectAttempts] = useState(0);

  const [isIncorrect, setIsIncorrect] = useState(false);
  const [solutionStatus, setSolutionStatus] = useState(null);

  const { id: questionId } = question;
  const previousQuestionId = usePrevious(questionId);
  const inputRef = useRef(null);

  const { isMobile, isMobileBrowser } = useMediaQuery();

  const focusInput = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [focusAgain]);

  const handleInputChange = useCallback(
    (text) => {
      let answerText = '';
      const lastChar = text.slice(-1);

      if (typingDirection === 'rtl') {
        if (text.length < value.length) {
          answerText = value.slice(1);
          setValue(value.slice(1));
        } else {
          answerText = lastChar + value;
          setValue(answerText);
        }
      } else {
        answerText = text;
        setValue(text);
      }

      if (_toString(answer) === _toString(answerText)) {
        submitAnswerRef.current?.({
          questionId,
          value: _toString(answerText),
          incorrectAttempts,
        });
        setIsIncorrect(false);
        setSolutionStatus(SOLUTION_STATUS.CORRECT);
        setIncorrectAttempts(0);
      } else if (answerText.length >= _toString(answer).length) {
        setIsIncorrect(true);
        setSolutionStatus(SOLUTION_STATUS.INCORRECT);
        setIncorrectAttempts((prev) => prev + 1);
      } else {
        setIsIncorrect(false);
        setSolutionStatus(null);
      }
    },
    [
      setValue,
      submitAnswerRef,
      answer,
      value,
      questionId,
      typingDirection,
      isIncorrect,
      setIsIncorrect,
      incorrectAttempts,
      setIncorrectAttempts,
    ],
  );

  useEffect(() => {
    if (previousQuestionId !== questionId) {
      setValue('');
      setSolutionStatus(null);
      setTimeout(focusInput, 100);
    }
  }, [previousQuestionId, questionId, focusInput]);

  useEffect(() => {
    setTimeout(focusInput, 100);
  }, [focusInput]);

  const onPressClearAll = useCallback(() => {
    setIsIncorrect(false);
    setValue('');
    focusInput();
  }, [handleInputChange, focusInput]);

  const setInputRef = useCallback((ref) => {
    inputRef.current = ref;
  }, []);

  const inputProps = {
    value,
    style: [
      styles.inputStyle,
      {
        width: _toString(answer).length * 22.5 + 24,
        fontSize: 14,
        letterSpacing: 13.5,
        maxWidth: Math.min(
          _toString(answer).length * 23 + 24,
          screenWidth - 16,
        ),
        writingDirection: typingDirection,
        textAlign: typingDirection === 'ltr' ? 'left' : 'right',
      },
    ],
    keyboardAppearance: 'dark',
    placeholderTextColor: dark.colors.inputPlaceholder,
    onFocus: focusInput,
    solutionStatus,
    showNetworkError: false,
    customKeyboard: isMobile,
    onChangeText: handleInputChange,
    keyboardType: 'decimal-pad',
    caretHidden: typingDirection !== 'ltr',
    contextMenuHidden: true,
    autoCorrect: false,
    blurOnSubmit: false,
    autoFocus: true,
    maxLength: _toString(answer).length,
  };

  return (
    <View style={styles.container}>
      <CustomTextInput {...inputProps} />
    </View>
  );
};

PracticeFooter.propTypes = {
  question: PropTypes.object,
  submitAnswerRef: PropTypes.object,
  typingDirection: PropTypes.string,
  answer: PropTypes.string,
};
export default PracticeFooter;
