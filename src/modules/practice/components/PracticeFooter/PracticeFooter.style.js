import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        width: '100%',
        maxWidth: 420,
        alignItems: 'center',
        justifyContent: "center",
        flexDirection: 'row',
    },
    inputStyle: {
        justifyContent: "center",
        alignItems: "center",
        fontSize: 14,
        color: 'white',
        borderRadius:12,
        fontFamily: 'Montserrat-400',
        outlineStyle: 'none',
        borderWidth: 1,
        height:36,
        borderColor: 'white',
        paddingHorizontal: 8,
        marginBottom:12,
        paddingVertical: 10,
        minWidth:35
    },
    errorContainer: {
        position: 'absolute',
        alignSelf: 'center',
        right: '30%',
        top: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeIconContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
    }
})

export default styles