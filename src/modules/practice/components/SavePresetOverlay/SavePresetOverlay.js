import { Overlay } from "@rneui/base"
import PropTypes from "prop-types"
import { TextInput, View } from "react-native"
import Entypo from '@expo/vector-icons/Entypo';
import PrimaryButton from 'atoms/PrimaryButton'
import { showToast, TOAST_TYPE } from 'molecules/Toast'
import useSavePresetOverlayStyles from "./SavePresetOverlay.style";
import dark from "../../../../core/constants/themes/dark";
import { useCallback, useMemo, useState } from "react";
import { Text } from "react-native";
import _map from 'lodash/map'
import _isEmpty from 'lodash/isEmpty'
import { getConfigTagsFromIdentifier } from "../../utils/getIdentifierStringFromConfig";
import ConfigTags from "../ConfigTags/ConfigTags";
import useSaveUserPreset from "../../hooks/mutations/useSavePresetMutation";
import { useRouter } from "expo-router";
import useGetRecentPresetsQuery from "../../hooks/queries/useGetRecentPresetsQuery";
import useGetSavedPresetQuery from "../../hooks/queries/useGetSavedPresetQuery";

const SavePresetOverlay = (props) => {
    const styles = useSavePresetOverlayStyles()
    const [presetName, setPresetName] = useState('')
    const [isSaving, setIsSaving] = useState(false);
    const { reFetchUserRecentPresets } = useGetRecentPresetsQuery();
    const { reFetchSavedPresets } = useGetSavedPresetQuery();

    const { saveUserPresetMutation } = useSaveUserPreset()

    const { isVisible, onClose, identifier } = props

    const onTextChange = useCallback((text) => {
        setPresetName(text)
    }, [setPresetName])

    const tagsData = useMemo(() => {
        return getConfigTagsFromIdentifier({ identifier })
    }, [identifier])

    const handleSavePressed = useCallback(async () => {
        if (_isEmpty(presetName)) {
            return
        }
        if (isSaving) {
            return
        }
        try {
            setIsSaving(true)
            await saveUserPresetMutation({ identifier, name: presetName });
            reFetchSavedPresets();
            reFetchUserRecentPresets();
            showToast({
                type: TOAST_TYPE.SUCCESS,
                description:"Preset Saved Successfully ",
            })
            onClose(presetName)
        } catch (e) {
            showToast({
                type: TOAST_TYPE.ERROR,
                description:"Oops!, Something Went Wrong",
            })
        } finally {
            setIsSaving(false)
        }

    }, [identifier, presetName, saveUserPresetMutation, isSaving, setIsSaving, onClose])

    return (
        <Overlay isVisible={isVisible} onBackdropPress={onClose} overlayStyle={styles.overlayStyle}>
            <View style={styles.buttonContainer}>
                <Entypo name="cross" size={20} color={dark.colors.textDark} onPress={onClose} />
                <Text style={{ fontFamily: "Montserrat-500", fontSize: 16, lineHeight: 20, color: "white" }}>
                    Save Preset
                </Text>
            </View>
            <Text style={{ color: "white", fontFamily: "Montserrat-500", fontSize: 14 }}>
                Preset's Name
            </Text>
            <TextInput
                placeholder="Enter Preset Name"
                placeholderTextColor={'grey'}
                style={styles.textInput}
                onChangeText={onTextChange} />

            <View style={{ flexWrap: 'wrap', flexDirection: 'row', gap: 8 }}>
                {
                    _map(tagsData, (tag, index) => <ConfigTags key={`${index}`} label={tag} />)
                }
            </View>

            <View style={{ width: "100%", alignItems: "flex-end", marginVertical: 15 }}>
                <PrimaryButton
                    label={isSaving ? 'Saving....' : 'Save'}
                    radius={'xl'}
                    buttonStyle={{ height: 25, justifyContent: "center", padding: 0, width: 100 }}
                    labelStyle={{ fontSize: 12, fontFamily: 'Montserrat-600' }}
                    onPress={handleSavePressed} />
            </View>

        </Overlay>
    )
}

SavePresetOverlay.propTypes = {
    isVisible: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    identifier: PropTypes.string.isRequired
}

export default SavePresetOverlay