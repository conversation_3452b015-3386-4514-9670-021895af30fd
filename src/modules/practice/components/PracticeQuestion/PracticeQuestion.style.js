import { Platform, StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'

const createStyles = (isNativeDevice, isCompactMode) => StyleSheet.create({
    questionExpression: {
        fontSize: isCompactMode ? 14 : 18,
        fontFamily: 'Montserrat-500',
        color: 'white',
        textAlign: "center",
        letterSpacing: 10
    },
    expressionContainer: {
        gap: isCompactMode ? 12 : 20,
        width: '100%',
        justifyContent: 'flex-end',
        alignItems: 'center',
    },
    expressionRow: {
        flexDirection: 'row',
        gap: 8,
        alignItems: 'center',
        justifyContent: 'center',
        // width: 120,
        // paddingRight: 36,
    },
    operatorContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    operator: {
        width: 16,
        // maxWidth: 24,
        fontSize: 14,
        color: 'white',
    },
    numberContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    digitBox: {
        width: isNativeDevice ? 15 : 7,
        height: isCompactMode ? 16 : 20,
        // alignItems: 'center',
        // justifyContent: 'center'
    },
    digitContainer: {
        flexDirection: 'row',
        gap: isNativeDevice ? 5 : 12,
        alignItems: 'center'
    }
})


const usePracticeQuestionStyles = () => {
    const { isMobile } = useMediaQuery();
    const isNativeDevice = Platform.OS !== "web"
    const styles = useMemo(() => createStyles(isNativeDevice, isMobile), [isNativeDevice, isMobile]);

    return styles;
};

export default usePracticeQuestionStyles
