import React, { useCallback } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import _toString from 'lodash/toString';
import styles from './NumberInput.style';
import InfoIcon from '../InfoIcon/InfoIcon';

const NumberInput = (props) => {
  const {
    label,
    value,
    onValueChange,
    showInfoIcon,
    onInfoIconPressed,
    minValue = 1,
    maxValue = 10,
    hideBorder = false,
    incrementBy = 1,
  } = props;

  const onCheckLimit = useCallback(
    (text) => {
      const parsedInt = Number.parseInt(text);
      if (parsedInt < minValue) {
        onValueChange(Math.max(minValue, parsedInt - 1));
        return;
      }

      if (parsedInt > maxValue) {
        onValueChange(Math.min(maxValue, parsedInt - 1));
        return;
      }
      if (Number.isNaN(parsedInt)) {
        onValueChange(minValue);
        return;
      }
      onValueChange(parsedInt);
    },
    [onValueChange, minValue, maxValue],
  );

  return (
    <View
      style={[
        styles.container,
        hideBorder && { borderBottomWidth: 0, paddingVertical: 0 },
      ]}
    >
      <View style={styles.labelAndIconRow}>
        <Text style={styles.label}>{label}</Text>
        {showInfoIcon && <InfoIcon onPress={onInfoIconPressed} />}
      </View>
      <View style={styles.inputContainer}>
        <TouchableOpacity
          onPress={() =>
            value > minValue ? onValueChange(value - incrementBy) : null
          }
          style={styles.subButton}
        >
          <Text style={styles.subText}>-</Text>
        </TouchableOpacity>
        <TextInput
          defaultValue={_toString(value)}
          style={styles.value}
          value={value}
          onChangeText={onCheckLimit}
          blurOnSubmit={false}
          keyboardType="number-pad"
        />
        <TouchableOpacity
          onPress={() =>
            value < maxValue ? onValueChange(value + incrementBy) : null
          }
          style={styles.addButton}
        >
          <Text style={styles.addText}>+</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

NumberInput.propTypes = {
  label: PropTypes.string,
  value: PropTypes.number,
  minValue: PropTypes.number,
  maxValue: PropTypes.number,
  onValueChange: PropTypes.func,
  showInfoIcon: PropTypes.bool,
  onInfoIconPressed: PropTypes.func,
  hideBorder: PropTypes.bool,
  incrementBy: PropTypes.number,
};

export default React.memo(NumberInput);
