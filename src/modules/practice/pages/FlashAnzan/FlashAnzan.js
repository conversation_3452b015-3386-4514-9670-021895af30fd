import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import Header from 'shared/Header';

import AntDesign from '@expo/vector-icons/AntDesign';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { SOLUTION_STATUS } from 'modules/game/pages/PlayGame/Footer/AnswerEvaluators';
import useFlashAnzanPlayPageStyles from './FlashAnzan.style';
import useFlashAnzanPlayController from '../../hooks/useFlashAnzanPlayController';
import FlashAnzanResult from './components/FlashAnzanResult/FlashAnzanResult';
import CustomTextInput from '../../../../components/shared/CustomTextInput';
import dark from '../../../../core/constants/themes/dark';
import FlashNumber from '../../../game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame/FlashNumber';

const FlashAnzanPlayPage = (props) => {
  const styles = useFlashAnzanPlayPageStyles();

  const { isMobile: isCompactMode, isMobileBrowser } = useMediaQuery();

  const { selectedConfig: config } = props;

  const { numberCount, noOfQuestions } = config;

  const {
    isGameCompleted,
    isFlashing,
    isInputWrong,
    isCorrect,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    gameResults,
    identifierString,
    countdown,
    startCountdown,
    isStartingCountdownCompleted,
    isSaved,
    bestStreak,
    setUserAnswer,
  } = useFlashAnzanPlayController({ config });

  const renderTrailingComponent = useCallback(
    () => (
      <View style={styles.trailingComponent}>
        <Text style={styles.questionCounter}>
          {currentQuestion}/{noOfQuestions}
        </Text>
      </View>
    ),
    [currentQuestion, noOfQuestions],
  );

  const inputProps = {
    value: userAnswer,
    style: [
      styles.input,
      isInputWrong && { borderColor: dark.colors.errorDark },
      isCorrect && { borderColor: dark.colors.secondary },
    ],
    keyboardAppearance: 'dark',
    placeholderTextColor: dark.colors.inputPlaceholder,
    onChangeText: setUserAnswer,
    keyboardType: 'number-pad',
    contextMenuHidden: true,
    autoCorrect: false,
    showNetworkError: false,
    blurOnSubmit: false,
    autoFocus: true,
    editable: isMobileBrowser ? false : !isCorrect,
    solutionStatus: isInputWrong ? SOLUTION_STATUS.INCORRECT : null,
    customKeyboard: isCompactMode,
  };

  const renderCountdown = useCallback(
    () => (
      <View style={styles.countdownContainer}>
        <Text
          style={[
            styles.countdownText,
            { fontSize: 35, fontFamily: 'Montserrat-800' },
          ]}
        >
          Starting in
        </Text>
        <Text style={styles.countdownText}>{startCountdown}</Text>
      </View>
    ),
    [startCountdown, styles.countdownContainer, styles.countdownText],
  );

  if (!isStartingCountdownCompleted) {
    return renderCountdown();
  }

  if (isGameCompleted) {
    return (
      <FlashAnzanResult
        gameResults={gameResults}
        identifierString={identifierString}
        isSaved={isSaved}
        bestStreak={bestStreak}
      />
    );
  }

  return (
    <View style={styles.container}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      {!isCompactMode && renderTrailingComponent()}
      <View
        style={[
          styles.innerContainer,
          (isFlashing || !isMobileBrowser) && { justifyContent: 'center' },
        ]}
      >
        {isFlashing && currentNumberIndex < numberCount && (
          <FlashNumber number={numbers[currentNumberIndex]} />
        )}

        {!isFlashing && (
          <View style={styles.answerContainer}>
            <Text style={styles.countdown}>
              {currentQuestion < noOfQuestions
                ? 'Next question in'
                : 'Results in '}
            </Text>
            <Text style={styles.countdownTime}>{countdown}</Text>
            <View
              style={{
                flexDirection: 'row',
                gap: 5,
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                maxWidth: 400,
              }}
            >
              <CustomTextInput {...inputProps} />
              <View
                style={{
                  width: 30,
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'absolute',
                  top: 8,
                  right: isCompactMode ? 10 : '40%',
                }}
              >
                {isCorrect && (
                  <View
                    style={{
                      backgroundColor: dark.colors.secondary,
                      height: 15,
                      width: 15,
                      borderRadius: 7.5,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <AntDesign
                      name="check"
                      size={10}
                      color={dark.colors.tertiary}
                    />
                  </View>
                )}
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

export default React.memo(FlashAnzanPlayPage);
