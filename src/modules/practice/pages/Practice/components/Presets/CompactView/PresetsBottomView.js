import { Dimensions, Image, Text } from "react-native"
import _isEmpty from 'lodash/isEmpty'
import { View } from "react-native"
import dark from "../../../../../../../core/constants/themes/dark"
import { TouchableOpacity } from "react-native"
import React, { useCallback, useState } from "react"
import { Overlay } from "@rneui/themed"
import styles from './PresetsBottomView.style'
import PresetsCard from "../../../../../components/PresetsCard"
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import PropTypes from "prop-types"
import { useNavigation } from "@react-navigation/native";
import _map from "lodash/map"
import { usePracticeContext } from "../../../../../../../../app/_layout"
import { useRouter } from "expo-router"
import Analytics from "../../../../../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../../../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../../../../../core/constants/pageNames";
import _uniqBy from 'lodash/uniqBy'
import { ScrollView } from "react-native"
import {DMAS_CATEGORIES, PRACTICE_LEFT_PANE_TABS_CONFIGS} from "../../OperatorSelector/constants/practice"
import _includes from "lodash/includes";

const PresetsBottomView = (props) => {

    const router = useRouter()

    const [isExpanded, setExpanded] = useState(false)

    const { selectedPresets, onRemove } = props

    const { setPracticeConfig } = usePracticeContext()

    const navigateToPlayGround = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CLICK_ON_PRACTICE, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_PAGE,
            selectedPresets
        })
        setPracticeConfig({ selectedPresets })
        setExpanded(false)

        const { categoryId } = selectedPresets?.[0];
        const { tag } = PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId]

        const isDmasTag = _includes(DMAS_CATEGORIES, tag);
        if(isDmasTag){
            router.push('/nets/play')
        }else{
            router.push(`/nets/${tag}`);
        }
    }, [selectedPresets, setPracticeConfig, setExpanded, router]);

    const bottomSection = useCallback(() => {
        return (
            <View style={styles.bottomSection}>
                <TouchableOpacity style={styles.itemStackDisplay} onPress={() => { setExpanded((prev) => !prev) }}>
                    <View style={{ flexDirection: "row", marginLeft: 15 }}>
                        {
                            _map(
                                _uniqBy(selectedPresets, (preset) => preset?.categoryId),
                                (preset, index) => {
                                    const { categoryId, config, id } = preset
                                    const { operationName, icon } = PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId]
                                    return (
                                        <View key={index} style={styles.selectedPresetImage}>
                                            <Image source={icon} style={{ height: 20, width: 20 }} />
                                        </View>
                                    )
                                }
                            )
                        }
                    </View>
                    <Text style={styles.itemCountText}>
                        {`${selectedPresets.length} categories`}
                    </Text>
                    <MaterialIcons name={isExpanded ? "keyboard-arrow-down" : "keyboard-arrow-up"} size={24} color={dark.colors.secondary} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.practiceButton} onPress={navigateToPlayGround}>
                    <Text style={styles.practiceText}>
                        Practice
                    </Text>
                </TouchableOpacity>
            </View>
        )
    }, [isExpanded, selectedPresets, setExpanded])

    if (_isEmpty(selectedPresets)) {
        return null
    }

    if (isExpanded) {
        return (
            <Overlay overlayStyle={[styles.overlayStyle, isExpanded && styles.expandedOverlay]} isVisible={true} transparent={true} backdropStyle={{ display: isExpanded ? "flex" : "none" }} onBackdropPress={() => setExpanded(false)}>
                {
                    isExpanded && (
                        <View style={styles.expandedView}>
                            <View style={{ justifyContent: 'center', alignItems: "center" }}>
                                <View style={styles.sheetLook} />
                            </View>
                            <Text style={styles.categoryText}>
                                Categories Preview
                            </Text>
                            <ScrollView style={{ maxHeight: Dimensions.get('window').height * 0.4 }} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
                                {
                                    _map(selectedPresets, (preset, index) => {
                                        return (<PresetsCard
                                            onRemove={() => onRemove(preset?.id)}
                                            selectedPreset={preset}
                                            key={`${index}`} />)
                                    }
                                    )
                                }
                            </ScrollView>
                        </View>
                    )
                }
                {bottomSection()}
            </Overlay>
        )
    }
    return (
        <View style={[styles.overlayStyle, { bottom: 0 }]} >
            {bottomSection()}
        </View>
    )
}

PresetsBottomView.propTypes = {
    selectedPresets: PropTypes.array,
    onRemove: PropTypes.func,
}

export default React.memo(PresetsBottomView)