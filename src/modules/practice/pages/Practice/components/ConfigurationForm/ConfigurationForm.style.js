import { StyleSheet } from "react-native";
import dark from "../../../../../../core/constants/themes/dark";
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: isCompactMode ? 8 : 5,
    },
    innerContainer: {
        paddingTop: isCompactMode ? 16 : 36,
        paddingBottom:100,
        gap:15,
        paddingHorizontal: isCompactMode ? 16 : 24,
        // overflow:"scroll"
    },
    header: {
        fontFamily: 'Montserrat-600',
        fontSize: isCompactMode ? 12 : 14,
        color: dark.colors.textDark,
        marginBottom: 16,
    },
    label: {
        color: '#FFFFFF',
        fontFamily: "Montserrat-500",
        marginRight: 10,
    },
    addButton: {
        width: 94,
        height: 32,
        borderColor: dark.colors.secondary,
        borderRadius: 20,
        borderWidth: 1,
        justifyContent: "center",
        alignItems: "center"
    },
    selectedPreset: {
        paddingHorizontal: 12,
        paddingVertical: 4,
        backgroundColor: dark.colors.primary,
        borderRadius: 16,
        justifyContent: "center",
        alignItems: "center"
    },
    addOrRemoveRow: {
        justifyContent: 'flex-end',
        alignItems: "flex-end",
        paddingTop: 20
    },
    addOrRemoveText: {
        color: dark.colors.secondary,
        fontSize: 14,
        fontFamily: 'Montserrat-600'
    },
    selectedPresetText: {
        color: 'white',
        lineHeight: 20,
        fontSize: 12,
        fontFamily: 'Montserrat-700'
    },


    presetButtonText: {
        color: dark.colors.textDark,
        lineHeight: 20,
        fontSize: 14,
        fontFamily: 'Montserrat-600'
    },

    addPresetButton: {
        maxWidth: !isCompactMode ? 250 : "100%",
        height: 35,
        justifyContent: "center",
        alignItems: "center",
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        borderRadius: 24,
        marginVertical: 15
    }
});

const useConfigurationFormStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useConfigurationFormStyles
