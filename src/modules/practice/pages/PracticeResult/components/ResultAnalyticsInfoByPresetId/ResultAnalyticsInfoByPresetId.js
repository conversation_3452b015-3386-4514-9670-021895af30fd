import { View, Text, ScrollView } from "react-native"
import ConfigTags from "../../../../components/ConfigTags/ConfigTags"

import _map from 'lodash/map'
import _round from 'lodash/round';
import _isEmpty from 'lodash/isEmpty'
import CardWithLabelAndValue from "../../../../components/CardWithLabelAndValue"
import useResultAnalyticsInfoWithConfigCardStyles from "./ResultAnalyticsInfoByPresetId.style"
import PropTypes from "prop-types"
import { getFormattedTimeWithMS } from "../../../../../../core/utils/general"
import { TouchableOpacity } from "react-native"
import { useCallback, useState } from "react"
import { useRouter } from "expo-router"
import SavePresetOverlay from "../../../../components/SavePresetOverlay/SavePresetOverlay"
import Analytics from "../../../../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../../../../core/constants/pageNames";
import WavyGraphComponent from "../../../../../../components/shared/WavyGraphComponent"
import UserStats from "../../../../components/UserStats"
import useMediaQuery from "core/hooks/useMediaQuery"
import { showRightPane } from "molecules/RightPane/RightPane"
import AnalyticsIcon from "../../../../../../components/svg/Icons/Analytics"
import dark from "../../../../../../core/constants/themes/dark"
import _reduce from "lodash/reduce";
import _size from "lodash/size";
import {useSession} from "../../../../../auth/containers/AuthProvider";
import {showToast, TOAST_TYPE} from "molecules/Toast";

const ResultAnalyticsInfoByPresetId = (props) => {
    const { user } = useSession();
    const [showToSaveModal, setShowToSaveModal] = useState(false)
    const { presetsAnalyticsInfo } = props
    const { isMobile: isCompactMode } = useMediaQuery()
    const { isSaved: isSavedPreset, incorrectAttempts } = presetsAnalyticsInfo
    const router = useRouter()
    const styles = useResultAnalyticsInfoWithConfigCardStyles()

    const [graphContainerWidth, setGraphContainerWidth] = useState(300);

    const totalAttempts = _reduce(incorrectAttempts, (acc, attempt) => acc + attempt + 1, 0);

    const inaccuracy = _round((_size(incorrectAttempts)/totalAttempts)*100, 2);

    const [isSaved, setIsSaved] = useState(isSavedPreset)

    const showSavePresetModal = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CLICK_ON_SAVE_PRESET, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_RESULT,
        })
        setShowToSaveModal(true)
    }, [setShowToSaveModal])

    const handleCloseSaveModal = useCallback((presetName) => {
        setShowToSaveModal(false)
        if (!_isEmpty(presetName)) {
            setIsSaved(true)
        }
    }, [setShowToSaveModal, setIsSaved])

    const handleOnAnalyticsPressed = useCallback(() => {
        if(user.isGuest){
            showToast({
                type: TOAST_TYPE.ERROR,
                description: 'Please login to see analytics.',
            })
            return;
        }
        if(_isEmpty([presetsAnalyticsInfo?.identifier])){
            return
        }
        if (isCompactMode) {
            router.push(`/profile/${user?.username}/analytics?category=${presetsAnalyticsInfo?.identifier}`)
            return
        }
        showRightPane({
            content: <UserStats identifier={presetsAnalyticsInfo?.identifier} />,
        })

    }, [user, presetsAnalyticsInfo?.identifier, isCompactMode])

    const renderSaveConfigSection = useCallback(() => {
        if (isSaved) {
            return null
        }

        return (<View style={styles.savePresetContainer}>
            <Text style={styles.savePresetText}>
                Save this preset ?
            </Text>
            <TouchableOpacity onPress={showSavePresetModal}>
                <Text style={styles.yesSaveText}>
                    Yes, save
                </Text>
            </TouchableOpacity>
        </View>)
    }, [showSavePresetModal, isSaved])

    const onWavyGraphContainerLayout = useCallback((event) => {
        const { width: containerWidth } = event.nativeEvent.layout;
        setGraphContainerWidth(containerWidth);
    }, [setGraphContainerWidth]);

    return (
        <View style={styles.container} >
            <View style={{ flexDirection: "row", justifyContent: "space-between" ,marginBottom:5}}>
                <Text style={styles.sessionConfigText}>
                    YOUR SESSION CONFIG
                </Text>
                <AnalyticsIcon onPress={handleOnAnalyticsPressed} fillColor={dark.colors.secondary}/>
            </View>

            <View style={styles.configTagsContainer}>
                {_map(presetsAnalyticsInfo.configTags, (tag, index) => <ConfigTags key={`${index}`} label={tag} />)}
            </View>

            {!isSaved && (
                <View style={{ marginTop: 16 }}>
                    {renderSaveConfigSection()}
                </View>
            )}

            <Text style={styles.sessionStatsRow}>
                YOUR SESSION STATS
            </Text>

            <View style={styles.statsContainer}>
                <View style={styles.statsRow}>
                    <CardWithLabelAndValue label={'Total Time'} value={getFormattedTimeWithMS(presetsAnalyticsInfo.totalTime * 1000)} />
                    <CardWithLabelAndValue label={'Avg. time/qn'} value={getFormattedTimeWithMS(presetsAnalyticsInfo.avgTime * 1000)} />
                </View>

                <View style={styles.statsRow}>
                    <CardWithLabelAndValue label={'Fastest Question '} value={getFormattedTimeWithMS(presetsAnalyticsInfo.fastestTime * 1000)} />
                    <CardWithLabelAndValue label={'Slowest Question'} value={getFormattedTimeWithMS(presetsAnalyticsInfo.slowestTime * 1000)} />
                </View>

                <View style={styles.statsRow}>
                    <CardWithLabelAndValue label={'Accuracy '} value={`${inaccuracy}%`} />
                    <View style={{ flex: 1, paddingHorizontal: 12 }}/>
                </View>
            </View>
            <Text style={styles.sessionStatsRow}>
                CURRENT SESSION AVERAGE
            </Text>
            <View style={{ marginVertical: 12 }} onLayout={onWavyGraphContainerLayout}>
                <WavyGraphComponent dataPoints={presetsAnalyticsInfo?.submittedTimes} width={graphContainerWidth - 32}/>
            </View>
            <View style={{ height: 100 }} />
            <SavePresetOverlay
                isVisible={showToSaveModal}
                onClose={handleCloseSaveModal}
                identifier={presetsAnalyticsInfo?.identifier}
            />
        </View>
    )
}

ResultAnalyticsInfoByPresetId.propTypes = {
    presetsAnalyticsInfo: PropTypes.object
}


export default ResultAnalyticsInfoByPresetId