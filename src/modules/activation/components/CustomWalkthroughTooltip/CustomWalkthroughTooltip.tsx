import { Text, TouchableOpacity, View } from 'react-native';
import styles from 'modules/activation/pages/ExploreBlitzGame/ExploreBlitzGame.style';
import React, { useCallback, useEffect, useState } from 'react';
import { useCopilot } from 'react-native-copilot';
import { Ionicons } from '@expo/vector-icons';
import {
  CUSTOM_WALKTHROUGH_TOOLTIP_POSITION,
  getTooltipTitleBySectionName,
} from 'modules/activation/constants/getTooltipInfos';
import _get from 'lodash/get';
import Triangle from './Triangle';

const ARROW_BORDER_WIDTH = 20;
const TOOLTIP_MARGIN = 16;
const ARRROW_POSITION_DELTA = ARROW_BORDER_WIDTH + TOOLTIP_MARGIN;

const CustomWalkthroughTooltip = (props: any) => {
  const { isLastStep, goToNext, stop, currentStep } = useCopilot();

  const { color, arrowPosition, arrowStyles, labels } = props;

  const [stepViewDimension, setStepViewDimension] = useState({
    centerX: 0,
    centerY: 0,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  const { name: sectionName, text, measure } = currentStep ?? EMPTY_OBJECT;

  useEffect(() => {
    measure().then((rect) => {
      const { width, height, x, y } = rect;
      const centerX = x + width / 2;
      const centerY = y + height / 2;
      setStepViewDimension({ centerX, centerY, x, y, width, height });
    });
  }, [measure]);

  const title = getTooltipTitleBySectionName({
    sectionName,
  });

  const isTopPosition = arrowPosition.vertical !== 'top';
  const isBottomPosition = !isTopPosition;

  const arrowDirection =
    arrowPosition.vertical === 'top'
      ? CUSTOM_WALKTHROUGH_TOOLTIP_POSITION.BOTTOM
      : CUSTOM_WALKTHROUGH_TOOLTIP_POSITION.TOP;

  const tooltipContainerStyle: any = {
    ...styles.tooltipContainer,
    justifyContent: isTopPosition ? 'flex-end' : 'flex-start',
  };

  const tooltipContentStyle = {
    ...styles.tooltipContent,
    borderColor: color,
    borderTopLeftRadius: isBottomPosition ? 20 : 0,
    borderTopRightRadius: isBottomPosition ? 20 : 0,
    borderBottomLeftRadius: isTopPosition ? 20 : 0,
    borderBottomRightRadius: isTopPosition ? 20 : 0,
    borderTopWidth: isTopPosition ? 2 : 0,
    borderBottomWidth: isBottomPosition ? 2 : 0,
  };

  const renderTriangle = useCallback(
    () => (
      <View
        style={{
          width: '100%',
          minHeight: 20,
        }}
      >
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: stepViewDimension.centerX - ARRROW_POSITION_DELTA,
          }}
        >
          <Triangle direction={arrowDirection} color={color} />
        </View>
      </View>
    ),
    [arrowDirection, color, stepViewDimension],
  );

  return (
    <View style={tooltipContainerStyle}>
      {isTopPosition && renderTriangle()}
      <View style={tooltipContentStyle}>
        <View style={styles.infoTextsContainer}>
          <Text style={styles.tooltipTitleText}>{title}</Text>
          <Text style={styles.tooltipText}>{text}</Text>
        </View>
        <View style={styles.tooltipButtonContainer}>
          {!isLastStep ? (
            <View
              style={{
                width: '100%',
                justifyContent: 'space-around',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <TouchableOpacity
                style={styles.tooltipButtonTouchable}
                onPress={stop}
              >
                <Text style={styles.toolTipSkipText}>
                  {_get(labels, 'skip', 'Skip')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.tooltipButtonTouchable}
                onPress={goToNext}
              >
                <Text style={[styles.toolTipNextTask, { color }]}>
                  {_get(labels, 'next', 'Next')}
                </Text>
                <Ionicons name="arrow-forward" size={18} color={color} />
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.tooltipButtonTouchable}
              onPress={stop}
            >
              <Text style={[styles.toolTipNextTask, { color }]}>
                {_get(labels, 'finish', 'Finish')}
              </Text>
              <Ionicons name="arrow-forward" size={18} color={color} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      {isBottomPosition && renderTriangle()}
    </View>
  );
};

export default CustomWalkthroughTooltip;

export const withCustomProps = (WrappedComponent: any, customProps: any) =>
  function (props: any) {
    return <WrappedComponent {...props} {...customProps} />;
  };
