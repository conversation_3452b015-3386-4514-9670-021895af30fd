import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import { router } from 'expo-router';
import { stringifyQueryParams } from 'core/utils/general';
import _findIndex from 'lodash/findIndex';
import Scrollview from 'atoms/Scrollview';
import _map from 'lodash/map';
import {
  EXPLORE_MODULE_FEATURES,
  EXPLORE_MODULES,
} from '../../constants/explore';
import ModuleCard from './components/ModuleCard';
import useExplorationStatusFromStore from '../../../../store/useExploredFeaturesStore/useExplorationStatusFromStore';
import styles from './ExploreMatiks.style';

const { width: screenWidth } = Dimensions.get('window');

interface ExploreMatiksProps {
  onModulePress?: (moduleId: string) => void;
  onFeatureExplored?: (featureType: string) => void;
}

const ExploreMatiks = ({
  onModulePress,
  onFeatureExplored,
}: ExploreMatiksProps) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const carouselRef = useRef<any>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const carouselWidth = isCompactMode
    ? screenWidth
    : Math.min(screenWidth - 40, 800);

  const cardWidth = isCompactMode ? carouselWidth - 32 : 320;

  const { getModuleExplorationStatus, markFeatureAsExplored, loading, error } =
    useExplorationStatusFromStore();

  const moduleData = Object.keys(EXPLORE_MODULES).map((moduleKey) => {
    const moduleId = EXPLORE_MODULES[moduleKey as keyof typeof EXPLORE_MODULES];
    const features = EXPLORE_MODULE_FEATURES[moduleId] || [];
    const isExplored = getModuleExplorationStatus(moduleId);

    return {
      id: moduleId,
      key: moduleKey,
      features,
      isExplored,
    };
  });

  const firstUnexploredIndex = _findIndex(
    moduleData,
    (module) => !module.isExplored,
  );

  useEffect(() => {
    if (firstUnexploredIndex !== -1 && carouselRef.current) {
      const timer = setTimeout(() => {
        carouselRef.current?.scrollTo({
          index: firstUnexploredIndex,
          animated: true,
        });
        setCurrentIndex(firstUnexploredIndex);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [firstUnexploredIndex]);

  const handleModulePress = useCallback(
    (moduleId: string) => {
      onModulePress?.(moduleId);
      const stingifiedQueryParams = stringifyQueryParams({ module: moduleId });
      router.push(`/explore/module?${stingifiedQueryParams}`);
    },
    [onModulePress],
  );

  const handleFeatureExplored = useCallback(
    async (featureType: string) => {
      try {
        await markFeatureAsExplored(featureType);
        onFeatureExplored?.(featureType);
      } catch (err) {
        console.error('Failed to mark feature as explored:', err);
      }
    },
    [markFeatureAsExplored, onFeatureExplored],
  );

  const renderModuleCard = useCallback(
    ({ item }: { item: any }) => (
      <View style={styles.carouselItemContainer}>
        <ModuleCard
          module={item}
          onPress={() => handleModulePress(item.id)}
          onFeatureExplored={handleFeatureExplored}
        />
      </View>
    ),
    [handleModulePress, handleFeatureExplored],
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <Loading />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <ErrorView
          errorMessage="Failed to load exploration data. Please try again."
          onRetry={getModuleExplorationStatus}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Scrollview horizontal contentContainerStyle={styles.carousel}>
        <View />
        {_map(moduleData, (module) => renderModuleCard({ item: module }))}
        <View />
      </Scrollview>
    </View>
  );
};

export default React.memo(ExploreMatiks);
