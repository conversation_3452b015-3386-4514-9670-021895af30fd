import React, { useCallback, useEffect, useRef } from 'react';
import { Dimensions, Text, View } from 'react-native';
import Loading from 'atoms/Loading';
import { router } from 'expo-router';
import { stringifyQueryParams } from 'core/utils/general';
import _findIndex from 'lodash/findIndex';
import Scrollview from 'atoms/Scrollview';
import _map from 'lodash/map';
import {
  EXPLORE_MODULE_FEATURES,
  EXPLORE_MODULES,
} from '../../constants/explore';
import ModuleCard from './components/ModuleCard';
import useExplorationStatusFromStore from '../../../../store/useExploredFeaturesStore/useExplorationStatusFromStore';
import styles from './ExploreMatiks.style';

const { width: screenWidth } = Dimensions.get('window');

interface ExploreMatiksProps {
  onModulePress?: (moduleId: string) => void;
  onFeatureExplored?: (featureType: string) => void;
  moduleData?: any[];
}

const CARD_WIDTH = 240;
const CARD_SPACING = 20;

const ExploreMatiks = ({
  onModulePress,
  onFeatureExplored,
  moduleData,
}: ExploreMatiksProps) => {
  const scrollViewRef = useRef<any>(null);
  const { markFeatureAsExplored } = useExplorationStatusFromStore();

  const firstUnexploredIndex = _findIndex(
    moduleData,
    (module) => !module.isExplored,
  );

  useEffect(() => {
    if (firstUnexploredIndex !== -1 && scrollViewRef.current) {
      const timer = setTimeout(() => {
        const xOffset = firstUnexploredIndex * (CARD_WIDTH + CARD_SPACING);
        scrollViewRef.current?.scrollTo({ x: xOffset, animated: true });
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [firstUnexploredIndex]);

  const handleModulePress = useCallback(
    (moduleId: string) => {
      onModulePress?.(moduleId);
      const stingifiedQueryParams = stringifyQueryParams({ module: moduleId });
      router.push(`/explore/module?${stingifiedQueryParams}`);
    },
    [onModulePress],
  );

  const handleFeatureExplored = useCallback(
    async (featureType: string) => {
      try {
        await markFeatureAsExplored(featureType);
        onFeatureExplored?.(featureType);
      } catch (err) {
        console.error('Failed to mark feature as explored:', err);
      }
    },
    [markFeatureAsExplored, onFeatureExplored],
  );

  const renderModuleCard = useCallback(
    ({ item }: { item: any }) => (
      <View style={styles.carouselItemContainer}>
        <ModuleCard
          module={item}
          onPress={() => handleModulePress(item.id)}
          onFeatureExplored={handleFeatureExplored}
        />
      </View>
    ),
    [handleModulePress, handleFeatureExplored],
  );

  return (
    <View style={styles.container}>
      <Scrollview
        horizontal
        contentContainerStyle={styles.carousel}
        ref={scrollViewRef}
      >
        <View />
        {_map(moduleData, (module) => renderModuleCard({ item: module }))}
        <View />
      </Scrollview>
    </View>
  );
};

const ExploreMatiksContainer = (props) => {
  const {
    getModuleExplorationStatus,
    loading,
    error,
    needToShowExploreFeatures,
  } = useExplorationStatusFromStore();

  const moduleData = Object.keys(EXPLORE_MODULES).map((moduleKey) => {
    const moduleId = EXPLORE_MODULES[moduleKey as keyof typeof EXPLORE_MODULES];
    const features = EXPLORE_MODULE_FEATURES[moduleId] || [];
    const isExplored = getModuleExplorationStatus(moduleId);

    return {
      id: moduleId,
      key: moduleKey,
      features,
      isExplored,
    };
  });

  if (loading) {
    return (
      <View style={styles.container}>
        <Loading />
      </View>
    );
  }

  if (error || !needToShowExploreFeatures) {
    return <View />;
  }

  return (
    <View style={styles.exploreSectionContainer}>
      <Text style={styles.exploreTitleText}>EXPLORE MATIKS</Text>
      <ExploreMatiks moduleData={moduleData} {...props} />
    </View>
  );
};

export default React.memo(ExploreMatiksContainer);
