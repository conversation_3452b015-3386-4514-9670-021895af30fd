import React, { useMemo } from 'react';
import { View } from 'react-native';
import gameReader from 'core/readers/gameReader';
import { Header as GameHeader } from 'modules/game/pages/PlayGame/Header/Header';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';

interface ExploreGameHeaderProps {
  game: any;
}

const ExploreGameHeader: React.FC<ExploreGameHeaderProps> = ({ game }) => {
  const players = gameReader.players(game);
  const leaderboard = gameReader.leaderBoard(game);

  const playersScores = useMemo(
    () =>
      _reduce(
        leaderboard,
        (acc, leaderboardEntry) => {
          acc[leaderboardEntry.userId] = leaderboardEntry.correct;
          return acc;
        },
        {},
      ),
    [leaderboard],
  );

  if (_isEmpty(players) || _isEmpty(players)) return null;

  return (
    <View style={{ width: '100%', alignItems: 'center', paddingTop: 16 }}>
      <GameHeader playersScores={playersScores} game={game} players={players} />
    </View>
  );
};

export default React.memo(ExploreGameHeader);
