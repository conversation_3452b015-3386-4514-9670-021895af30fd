import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 16,
    alignItems: 'center',
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepsContainer: {
    flex: 1,
    height: 8,
    gap: 4,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  step: {
    flex: 1,
    height: 8,
    minHeight: 8,
    borderRadius: 100,
    backgroundColor: dark.colors.tertiary,
  },
  activeStep: {
    backgroundColor: dark.colors.streak,
  },
  stepsCount: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    lineHeight: 16,
    color: dark.colors.textDark,
  },
});

export default styles;
