import React from 'react';
import { Text, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { Overlay } from '@rneui/themed';
import dark from 'core/constants/themes/dark';
import { Image } from 'expo-image';
import Pressable from 'atoms/Pressable';
import AntDesign from '@expo/vector-icons/AntDesign';
import _toUpper from 'lodash/toUpper';
import styles from './StartActivationModal.style';

interface StartWalkthroughModalProps {
  isVisible: boolean;
  onStart: () => void;
  onSkip: () => void;
  activationConfig: {
    title: string;
    description: string;
    color: string;
    imageUrl: string;
  };
}

const StartWalkthroughModal: React.FC<StartWalkthroughModalProps> = ({
  isVisible,
  onStart,
  onSkip,
  activationConfig,
}) => {
  const {
    title,
    description,
    imageUrl,
    color = dark.colors.blitzGameColor,
  } = activationConfig ?? EMPTY_OBJECT;
  return (
    <Overlay
      isVisible={isVisible}
      overlayStyle={styles.overlayContainer}
      animationType="none"
      onBackdropPress={onSkip}
    >
      <View style={styles.contentContainer}>
        <Animatable.View
          animation="slideInUp"
          duration={600}
          style={[styles.bottomContent, { borderColor: color }]}
        >
          <Image
            style={styles.activationImage}
            source={imageUrl}
            contentFit="cover"
            transition={500}
          />
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 32,
              gap: 32,
            }}
          >
            <View style={styles.textContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.description}>{_toUpper(description)}</Text>
            </View>
            <View style={styles.buttonContainer}>
              <View style={styles.buttonWrapper}>
                <Pressable onPress={onSkip}>
                  <View style={styles.proceedButtonContainer}>
                    <Text style={styles.skipButtonText}>GO BACK</Text>
                  </View>
                </Pressable>
              </View>
              <View style={styles.buttonWrapper}>
                <Pressable onPress={onStart}>
                  <View style={styles.proceedButtonContainer}>
                    <Text style={[styles.startButtonText, { color }]}>
                      PROCEED
                    </Text>
                    <AntDesign name="arrowright" size={16} color={color} />
                  </View>
                </Pressable>
              </View>
            </View>
          </View>
        </Animatable.View>
      </View>
    </Overlay>
  );
};

export default React.memo(StartWalkthroughModal);
