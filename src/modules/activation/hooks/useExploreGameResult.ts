import { useCallback, useMemo } from 'react';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _find from 'lodash/find';
import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { closePopover } from 'molecules/Popover/Popover';
import gameReader from 'core/readers/gameReader';
import gameLeaderboardReader from 'core/readers/gameLeaderboardReader';
import { stringifyQueryParams } from '@/src/core/utils/general';
import _every from 'lodash/every';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useGoBack from 'navigator/hooks/useGoBack';
import { GAME_RESULT_STATUS } from 'modules/game/constants/game';
import { GAME_TYPES } from '../../home/<USER>/gameTypes';

const useExploreGameResult = ({ game }) => {
  const players = gameReader.players(game);

  const { user } = useSession();

  const router = useRouter();

  const { goBackToHome } = useGoBack();

  const leaderBoard = gameReader.leaderBoard(game);
  const gameType = gameReader.gameType(game);
  const gamePlayers = gameReader.players(game);

  const timeLimit = gameReader.timeLimit(game);

  const isFlashAnzan = gameType === GAME_TYPES.FLASH_ANZAN;

  const adaptedPlayers = useMemo(
    () =>
      _map(players, (player) => {
        const { _id: playerId } = player;
        const leaderBoardEntry = gameLeaderboardReader.findEntryForPlayer(
          leaderBoard,
          playerId,
        );

        return {
          ...player,
          ratingChange: gameLeaderboardReader.ratingChange(leaderBoardEntry),
          statikCoinsEarned:
            gameLeaderboardReader.statikCoinsEarned(leaderBoardEntry),
          score: gameLeaderboardReader.getScore(leaderBoardEntry, isFlashAnzan),
          isWinner: gameLeaderboardReader.isWinner(leaderBoardEntry),
        };
      }),
    [players, leaderBoard, isFlashAnzan],
  );

  const currentPlayer = useMemo(
    () => _find(adaptedPlayers, (player) => player?._id === user?._id),
    [adaptedPlayers, user],
  );

  const currentPlayerStatus = currentPlayer?.isWinner
    ? GAME_RESULT_STATUS.WIN
    : GAME_RESULT_STATUS.LOSS;

  const currentPlayerOriginalRating = useMemo(
    () =>
      _get(
        _find(gamePlayers, (player) => player?.userId === user?._id),
        'rating',
      ),
    [gamePlayers, user],
  );

  const currentPlayerOriginalStatikCoins = useMemo(
    () =>
      _get(
        _find(gamePlayers, (player) => player?.userId === user?._id),
        'statikCoins',
        0,
      ),
    [gamePlayers, user],
  );

  const player1 = useMemo(() => adaptedPlayers[0], [adaptedPlayers]);
  const player2 = useMemo(() => adaptedPlayers[1], [adaptedPlayers]);

  const navigateToNewGame = useCallback(() => {
    closePopover?.();
    Analytics.track(
      ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_NEW_GAME_ON_RESULT_MODAL,
    );
    if (gameType === GAME_TYPES.FLASH_ANZAN) {
      const queryParams = {
        timeLimit: 1.5,
        gameType,
      };
      const stringifiedQueryParams = stringifyQueryParams(queryParams);
      router.replace(`/search?${stringifiedQueryParams}`);
      return;
    }
    router.replace(`/search?timeLimit=1&gameType=${gameType}`);
  }, [router, gameType]);

  const navigateToHome = useCallback(() => {
    closePopover?.();
    Analytics.track(
      ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_GO_HOME_ON_RESULT_MODAL,
    );
    goBackToHome();
  }, [goBackToHome]);

  const isCurrentPlayerWinner = useMemo(
    () => currentPlayer?.isWinner,
    [currentPlayer],
  );

  const isMatchTied = useMemo(() => {
    if (!adaptedPlayers?.length) return false;
    const player1Score = adaptedPlayers[0]?.score;
    return _every(adaptedPlayers, (player) => player?.score === player1Score);
  }, [adaptedPlayers]);

  return {
    game,
    currentPlayer,
    player1,
    player2,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    currentPlayerStatus,
    players,
    adaptedPlayers,
    isCurrentPlayerWinner,
    timeLimit,
    isFlashAnzan,
    navigateToHome,
    navigateToNewGame,
    isMatchTied,
  };
};

export default useExploreGameResult;
