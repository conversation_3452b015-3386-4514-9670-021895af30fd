import _get from 'lodash/get';
import _set from 'lodash/set';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import _findIndex from 'lodash/findIndex';

export const updateLeaderboardOnSubmitAnswer = ({ state, userId, score }) => {
  const userLeaderboardEntryIndex = _findIndex(
    state.currentGame.leaderBoard,
    (entry) => entry?.userId === userId,
  );
  const userLeaderboardEntry = _get(state, [
    'currentGame',
    'leaderBoard',
    userLeaderboardEntryIndex,
  ]);

  if (_isEmpty(userLeaderboardEntry)) return;
  const updatedUserLeaderboardEntry = {
    ...userLeaderboardEntry,
    totalPoints: score ?? _toNumber(userLeaderboardEntry.score) + 1,
    correct: _toNumber(userLeaderboardEntry.correct) + 1,
  };
  _set(
    state,
    ['currentGame', 'leaderBoard', userLeaderboardEntryIndex],
    updatedUserLeaderboardEntry,
  );
};
