import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

export interface PuzzleWalkthroughState {
  timeSpent: number;
  statikCoinsEarned: number;
  setResult: ({ timeSpent }: { timeSpent: number }) => void;
  reset: () => void;
}

const usePuzzleWalkthroughStore = create<PuzzleWalkthroughState>()(
  immer((set) => ({
    timeSpent: 0,
    statikCoinsEarned: 0,
    setResult: ({ timeSpent }: { timeSpent: number }) => {
      set((state) => {
        state.timeSpent = timeSpent;
        state.statikCoinsEarned = 15;
      });
    },
    reset: () => {
      set((state) => {
        state.timeSpent = 0;
        state.statikCoinsEarned = 0;
      });
    },
  })),
);

export default usePuzzleWalkthroughStore;
