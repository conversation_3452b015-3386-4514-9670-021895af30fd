// All features explored screen
import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import { router } from 'expo-router';
import { ICON_TYPES } from 'atoms/Icon';
import Header from 'shared/Header';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import styles from './AllFeaturesExploredScreen.style';

interface AllFeaturesExploredScreenProps {
  moduleInfo: any;
  moduleProgress: any;
}

const AllFeaturesExploredScreen = ({
  moduleInfo,
  moduleProgress,
}: AllFeaturesExploredScreenProps) => {
  const gotToHome = useCallback(() => {
    router.navigate('/home');
  }, []);

  return (
    <View style={styles.allExploredContainer}>
      <Header />
      <View style={styles.allExploredContent}>
        <Text style={styles.allExploredIcon}>🎉</Text>
        <View style={{ gap: 25 }}>
          <Text style={styles.allExploredTitle}>All Features Explored!</Text>
          <Text style={styles.allExploredSubtitle}>
            You've successfully explored all features in {moduleInfo?.title}
          </Text>
        </View>
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: moduleInfo?.color || dark.colors.primary,
                  width: '100%',
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {moduleProgress?.explored}/{moduleProgress?.total} Features
            Completed
          </Text>
        </View>
      </View>
      <View style={{ width: '100%', paddingBottom: 20, paddingHorizontal: 16 }}>
        <InteractiveSecondaryButton
          label="Back to Home"
          onPress={gotToHome}
          iconConfig={{
            name: 'home',
            type: ICON_TYPES.ENTYPO,
            size: 20,
          }}
          labelStyle={styles.homeButtonLabel}
          buttonContainerStyle={styles.homeButton}
          buttonStyle={styles.homeButtonStyle}
          buttonBackgroundStyle={{
            backgroundColor: dark.colors.secondary,
          }}
        />
      </View>
    </View>
  );
};
export default React.memo(AllFeaturesExploredScreen);
