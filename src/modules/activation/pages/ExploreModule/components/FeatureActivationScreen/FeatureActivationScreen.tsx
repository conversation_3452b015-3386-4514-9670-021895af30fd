import React, { useCallback, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { router, useLocalSearchParams } from 'expo-router';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
import {
  EXPLORE_MODULE_TITLE,
  FEATURE_ACTIVATION_INFO,
} from 'modules/activation/constants/explore';
import { stringifyQueryParams } from 'core/utils/general';
import styles from './FeatureActivationScreen.style';

const { width: screenWidth } = Dimensions.get('window');

interface FeatureItem {
  id: string | number;
  title?: string;
}

interface FeatureActivationScreenProps {
  featuresToExplore: FeatureItem[];
}

const FeatureActivationScreen: React.FC<FeatureActivationScreenProps> = ({
  featuresToExplore,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const searchParams = useLocalSearchParams();
  const { module }: { module: string } = searchParams ?? EMPTY_OBJECT;

  const carouselRef = useRef(null);

  const handleSkipAll = useCallback(() => {
    router.navigate('/explore');
  }, []);

  const parallaxScrollingOffset = 204;

  const infoOfCurrentFeature =
    FEATURE_ACTIVATION_INFO[featuresToExplore[currentIndex] as any];

  const onPressFeature = useCallback(() => {
    const feature = featuresToExplore[currentIndex];
    const stingifiedQueryParams = stringifyQueryParams({ feature });
    router.navigate(`/explore/walkthrough?${stingifiedQueryParams}`);
  }, [currentIndex, featuresToExplore]);

  const renderCarouselItem = ({
    item,
    index,
  }: {
    item: FeatureItem;
    index: number;
  }) => {
    const featureInfo =
      FEATURE_ACTIVATION_INFO[featuresToExplore[index] as any];

    return (
      <TouchableOpacity
        key={featureInfo?.title}
        style={[styles.carouselItemContainer]}
        onPress={onPressFeature}
      >
        <Image
          source={{ uri: featureInfo?.imageUrl }}
          style={styles.carouselItemView}
          resizeMode="contain"
        />
      </TouchableOpacity>
    );
  };

  const moduleTitle = EXPLORE_MODULE_TITLE[module];

  return (
    <View style={styles.container}>
      <View style={styles.infoContainer}>
        <View style={styles.titleContainer}>
          <View style={styles.blitzTypeContainer}>
            {currentIndex === 0 && (
              <Text style={styles.blitzTypeText}>TYPES OF {moduleTitle}</Text>
            )}
          </View>
          <Text style={styles.mainTitle}>{infoOfCurrentFeature?.title}</Text>
          <Text style={styles.subTitle}>
            {infoOfCurrentFeature?.description}
          </Text>
        </View>

        <View style={styles.carouselContainer}>
          <Carousel
            ref={carouselRef}
            loop={false}
            width={screenWidth}
            height={220}
            autoPlay={false}
            data={featuresToExplore}
            scrollAnimationDuration={500}
            onSnapToItem={(index) => setCurrentIndex(index)}
            renderItem={renderCarouselItem}
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 0.95,
              parallaxScrollingOffset,
              parallaxAdjacentItemScale: 0.5,
            }}
          />
        </View>
      </View>

      <View style={styles.footerSection}>
        <InteractiveSecondaryButton
          label="LEARN HOW TO PLAY"
          onPress={onPressFeature}
          buttonContainerStyle={styles.learnButton}
          labelStyle={styles.learnButtonLabel}
          borderColor={dark.colors.victoryColor}
        />
        <Pressable onPress={handleSkipAll} style={styles.skipAllButton}>
          <Text style={styles.skipAllText}>SKIP ALL</Text>
        </Pressable>
      </View>
    </View>
  );
};

export default React.memo(FeatureActivationScreen);
