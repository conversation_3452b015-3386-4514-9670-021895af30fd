import React from 'react';
import { Text, View } from 'react-native';
import useFlashAnzanPlayPageStyles from 'modules/game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame/PlayFlashAnzanDuelGame.style';
import FlashNumber from 'modules/game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame/FlashNumber';

import NewNumberInput from 'molecules/NewNumberInput';
import ToggleSwitch from 'molecules/ToggleSwitch';
import Icon from 'atoms/Icon';
import TextWithShadow from 'shared/TextWithShadow';
import UserImage from 'atoms/UserImage';
import styles from 'modules/game/pages/FlashAnzan/Components/FlashAnzanConfigSelection/FlashAnzanConfigSelection.style';
import dark from 'core/constants/themes/dark';

export const MockPlayerInfo = () => (
  <View style={styles.header}>
    <View style={styles.playerInfo}>
      <TextWithShadow text={120} textStyle={styles.score} />
      <View style={styles.playerNameAndRatingContainer}>
        <View style={styles.playerNameContainer}>
          <UserImage size={12} />
          <Text style={styles.playerName} numberOfLines={1}>
            Player 1
          </Text>
        </View>
        <Text style={styles.userRatingText}>(1500)</Text>
      </View>
    </View>
    <Text style={styles.questionCount}>1/3</Text>
    <View style={styles.playerInfo}>
      <TextWithShadow text={100} textStyle={styles.score} />
      <View style={styles.playerNameAndRatingContainer}>
        <View style={styles.playerNameContainer}>
          <UserImage size={12} />
          <Text style={styles.playerName} numberOfLines={1}>
            Player 2
          </Text>
        </View>
        <Text style={styles.userRatingText}>(1450)</Text>
      </View>
    </View>
  </View>
);

export const MockPrepPhaseCountdown = () => (
  <Text style={styles.countdownText}>PREP PHASE ENDS IN 10</Text>
);

export const MockConversion = () => (
  <View style={styles.conversionTopContainer}>
    <Text style={styles.conversionNameText}>CONVERSION</Text>
    <View style={styles.conversionContainer}>
      <View style={styles.conversionItem}>
        <Text style={styles.conversionText}>5 NUMBERS</Text>
      </View>
      <Icon name="arrow-right" size={15} color={dark.colors.secondary} />
      <View style={styles.conversionItem}>
        <Text style={styles.conversionText}>50 POINTS</Text>
      </View>
    </View>
  </View>
);

export const MockGameSettings = () => (
  <View style={{ paddingHorizontal: 16, gap: 32 }}>
    <NewNumberInput
      label="Number of digits"
      value={2}
      onValueChange={() => {}}
      minValue={1}
      maxValue={5}
      valueSuffix=" DIGIT"
    />
    <NewNumberInput
      label="Time Interval"
      value={500}
      minValue={200}
      onValueChange={() => {}}
      maxValue={5000}
      incrementBy={100}
      valueSuffix="MS"
    />
    <ToggleSwitch
      label="Include Subtraction"
      value={false}
      onValueChange={() => {}}
    />
  </View>
);

export const MockStartingSection = () => {
  const styles = useFlashAnzanPlayPageStyles();
  return (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>Starting in 3</Text>
    </View>
  );
};

export const MockFlashingSection = () => (
  <View
    style={{
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
    }}
  >
    <FlashNumber number={8} />
  </View>
);

export const MockNextQuestionCountdownSection = () => {
  const styles = useFlashAnzanPlayPageStyles();
  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
      }}
    >
      <Text style={styles.countdown}>Next Question in</Text>
      <Text style={styles.countdownTime}>10</Text>
    </View>
  );
};
