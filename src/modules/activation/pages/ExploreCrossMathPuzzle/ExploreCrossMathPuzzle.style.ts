import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  infoTextsContainer: {
    gap: 12,
  },
  tooltipContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tooltipContent: {
    backgroundColor: dark.colors.background,
    gap: 12,
    paddingTop: 32,
    paddingHorizontal: 16,
  },
  invertedTriangle: {
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderLeftWidth: 20,
    borderRightWidth: 20,
    borderTopWidth: 20,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    alignSelf: 'center',
  },
  triangle: {
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderLeftWidth: 20,
    borderRightWidth: 20,
    borderBottomWidth: 20,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    alignSelf: 'center',
  },
  tooltipTitleText: {
    color: dark.colors.text,
    fontSize: 24,
    lineHeight: 28,
    fontFamily: 'Montserrat-600',
    textAlign: 'center',
  },
  tooltipText: {
    color: withOpacity(dark.colors.text, 0.6),
    fontSize: 12,
    lineHeight: 15,
    textAlign: 'center',
    fontFamily: 'Montserrat-600',
    textTransform: 'uppercase',
  },
  tooltipButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    height: 80,
  },
  tooltipButtonTouchable: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    minWidth: 80,
    alignItems: 'center',
    gap: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  toolTipSkipText: {
    fontSize: 12,
    color: withOpacity(dark.colors.text, 0.4),
    fontFamily: 'Montserrat-800',
  },
  toolTipNextTask: {
    color: '#E29B36',
    fontSize: 12,
    fontFamily: 'Montserrat-800',
  },

  tooltipButtonText: {
    color: dark.colors.secondary,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default styles;
