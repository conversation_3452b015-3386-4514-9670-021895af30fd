import React, { useCallback, useEffect, useState } from 'react';
import { Dimensions, SafeAreaView, View } from 'react-native';

import ActivationProgress from 'modules/activation/components/ActivationProgress';
import useGoBack from 'navigator/hooks/useGoBack';
import {
  Co<PERSON>lotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import StartActivationModal from 'modules/activation/components/StartActivationModal';
import dark from 'core/constants/themes/dark';
import {
  ACTIVATION_GAME_CONFIG,
  FEATURE_ACTIVATION_INFOS,
} from 'modules/activation/constants/explore';
import { router } from 'expo-router';
import { BLITZ_GAME_WALKTHROUGH_STEPS } from 'modules/activation/constants/getTooltipInfos';
import _map from 'lodash/map';
import useExplorationStatusFromStore from 'store/useExploredFeaturesStore/useExplorationStatusFromStore';
import styles from './ExploreBlitzGame.style';
import CustomTooltip, {
  withCustomProps,
} from '../../components/CustomWalkthroughTooltip';
import PlayFirstGameModal from '../../components/PlayFirstGameModal';

const BlitzGameTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.blitzGameColor,
});

const WalkthroughableView = walkthroughable(View);

const ExploreBlitzGame = React.memo(({ feature }: any) => {
  const { goBack } = useGoBack();
  const { markFeatureAsExplored } = useExplorationStatusFromStore();
  const [currentStep, setCurrentStep] = useState(0);

  const TOTAL_STEPS = BLITZ_GAME_WALKTHROUGH_STEPS.length + 3;

  const [playFirstGameModalVisible, setPlayFirstGameModalVisible] =
    useState(false);
  const [startActivationModalVisible, setStartActivationModalVisible] =
    useState(true);

  const GAME_ACTIVATION_INFO = FEATURE_ACTIVATION_INFOS[feature] as any;

  const gameConfig = ACTIVATION_GAME_CONFIG[feature] as any;

  const { start, copilotEvents } = useCopilot();

  const onClickStep = useCallback(
    (index: number) => {
      // if (index >= 1 && index <= TOTAL_STEPS) {
      //   setCurrentStep(index);
      // }
    },
    [TOTAL_STEPS],
  );

  const onPressSkip = useCallback(async () => {
    setStartActivationModalVisible(false);
    goBack();
    await markFeatureAsExplored(feature);
    // TODO: @mohan mark feature explored
  }, [feature, goBack, markFeatureAsExplored]);

  const onPressStartActivation = useCallback(() => {
    setStartActivationModalVisible(false);
    start();
  }, [start]);

  const onPressStartBlitzGame = useCallback(async () => {
    setPlayFirstGameModalVisible(false);
    markFeatureAsExplored(feature);
    router.push(
      `/explore/game-play?gameType=${gameConfig?.gameType}&feature=${feature}`,
    );
  }, [markFeatureAsExplored, feature, gameConfig?.gameType]);

  useEffect(() => {
    copilotEvents.on('stepChange', (step) => {
      setCurrentStep(step ? step.order : 0);
    });

    copilotEvents.on('stop', () => {
      setPlayFirstGameModalVisible(true);
    });
  }, [copilotEvents]);

  return (
    <SafeAreaView style={styles.container}>
      <ActivationProgress
        currentStep={currentStep}
        stepsCount={TOTAL_STEPS}
        onClose={goBack}
        onClickStep={onClickStep}
      />

      {_map(BLITZ_GAME_WALKTHROUGH_STEPS, (step) => {
        const WalkThroughComponent = step.component;
        const { name, text, order, walkthroughableViewStyle } =
          step ?? EMPTY_OBJECT;

        return (
          <CopilotStep key={name} text={text} order={order} name={name}>
            <WalkthroughableView style={walkthroughableViewStyle}>
              <WalkThroughComponent />
            </WalkthroughableView>
          </CopilotStep>
        );
      })}

      <StartActivationModal
        onSkip={onPressSkip}
        activationConfig={GAME_ACTIVATION_INFO}
        onStart={onPressStartActivation}
        isVisible={startActivationModalVisible}
      />

      <PlayFirstGameModal
        isVisible={playFirstGameModalVisible}
        onClickPlayNow={onPressStartBlitzGame}
        gameConfig={gameConfig}
      />
    </SafeAreaView>
  );
});

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const ExploreBlitzGameContainer = (props: any) => (
  <CopilotProvider
    tooltipComponent={BlitzGameTooltip as any}
    tooltipStyle={{
      backgroundColor: 'transparent',
      padding: 0,
      borderRadius: 0,
      width: WIDTH,
      maxWidth: WIDTH,
      left: MARGIN,
    }}
    arrowColor="transparent"
    stopOnOutsideClick
    androidStatusBarVisible
    stepNumberComponent={() => null} // Hide step numbers
    backdropColor="rgba(0, 0, 0, 0.6)"
    labels={{
      previous: 'Previous',
      next: 'Next',
      skip: 'Skip',
      finish: 'Got it!',
    }}
  >
    <ExploreBlitzGame {...props} />
  </CopilotProvider>
);

export default React.memo(ExploreBlitzGameContainer);
