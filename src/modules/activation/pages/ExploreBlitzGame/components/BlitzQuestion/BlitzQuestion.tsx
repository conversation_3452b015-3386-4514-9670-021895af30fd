import React from 'react';
import <PERSON><PERSON><PERSON><PERSON> from 'shared/QuestionsRenderer';
import {
  dummyDMASAbilityQuestion,
  dummyDMASQuestion,
} from 'modules/activation/dummyData/data';
import { useLocalSearchParams } from 'expo-router';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';

const BlitzQuestion = () => {
  const searchParams = useLocalSearchParams();

  const { feature } = searchParams ?? EMPTY_OBJECT;

  const question =
    feature === EXPLORE_FEATURES.ABILITY_DUELS
      ? dummyDMASAbilityQuestion
      : dummyDMASQuestion;

  return <QuestionRenderer question={question} />;
};

export default React.memo(BlitzQuestion);
