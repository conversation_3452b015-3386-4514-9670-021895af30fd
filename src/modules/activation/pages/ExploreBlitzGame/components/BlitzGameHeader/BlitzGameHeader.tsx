import React from 'react';
import { View } from 'react-native';
import { Image } from 'expo-image';

const HEADER_IMAGE_URL =
  'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_gameActivationHeader.png?timestamp=1750669711';

const BlitzGameHeader = () => (
  <View>
    <Image
      style={{ width: '100%', height: 100 }}
      source={HEADER_IMAGE_URL}
      contentFit="cover"
      transition={1000}
    />
  </View>
);

export default React.memo(BlitzGameHeader);
