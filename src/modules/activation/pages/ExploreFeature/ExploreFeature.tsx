import React from 'react';
import { View } from 'react-native';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';
import ErrorView from 'atoms/ErrorView';
import ExploreBlitzGame from 'modules/activation/pages/ExploreBlitzGame';
import ExploreCrossMathPuzzle from 'modules/activation/pages/ExploreCrossMathPuzzle';
import ExploreKenKenPuzzle from 'modules/activation/pages/ExploreKenKenPuzzle';
import ExploreFlashAnzanGame from '../ExploreFlashAnzanGame';

const EXPLORE_FEATURE_COMPONENT_FACTORY = {
  // blitz
  [EXPLORE_FEATURES.ONLINE_DUELS]: ExploreBlitzGame,
  [EXPLORE_FEATURES.FASTEST_FINGER]: ExploreBlitzGame,

  // classic
  [EXPLORE_FEATURES.ABILITY_DUELS]: ExploreBlitzGame,

  // memory
  [EXPLORE_FEATURES.FLASH_ANZAN]: ExploreFlashAnzanGame,

  // puzzle duels
  [EXPLORE_FEATURES.PUZZLE_DUELS]: () => <View />,

  // puzzles
  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: ExploreCrossMathPuzzle,
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: ExploreKenKenPuzzle,
  [EXPLORE_FEATURES.MATH_MAZE]: () => <View />,
};

const ExploreFeature = (props: any) => {
  const { feature } = props ?? EMPTY_OBJECT;

  const FeatureComponent = EXPLORE_FEATURE_COMPONENT_FACTORY[feature];

  if (!FeatureComponent) {
    return <ErrorView errorMessage="Invalid Feature" />;
  }
  return <FeatureComponent feature={feature} />;
};

export default React.memo(ExploreFeature);
