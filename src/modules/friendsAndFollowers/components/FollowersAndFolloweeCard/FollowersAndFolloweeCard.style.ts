import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 15,
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 1,
  },
  userImage: {
    height: 36,
    width: 36,
    borderRadius: 4,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  userName: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 17,
    maxWidth: 300,
    color: 'white',
  },
  userRating: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    maxWidth: 300,
    lineHeight: 15,
    color: dark.colors.textDark,
  },
  challengeText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 20,
    color: dark.colors.secondary,
  },
  userInfoWithImage: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  userInfo: {
    gap: 4,
  },
});

export default styles;
