import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";
import dark from "core/constants/themes/dark";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        paddingVertical: 12,
        flexDirection: 'row',
        justifyContent: "space-between",
        borderBottomColor:dark.colors.tertiary,
        borderBottomWidth:1
    },
    userImage: {
        height: 36,
        width: 36,
        borderRadius: 4,
        backgroundColor: 'white',
        overflow: "hidden",
    },
    userName: {
        fontFamily: "Montserrat-500",
        fontSize: 14,
        lineHeight: 17,
        color: "white",
        maxWidth:300,
    },
    userRating: {
        fontFamily: "Montserrat-500",
        fontSize: 12,
        lineHeight: 15,
        maxWidth:300,
        color: dark.colors.textDark
    },
    acceptText: {
        fontFamily: "Montserrat-500",
        fontSize: 12,
        lineHeight: 20,
        color: dark.colors.secondary
    },
    rejectText: {
        fontFamily: "Montserrat-500",
        fontSize: 12,
        lineHeight: 20,
        color: dark.colors.textDark
    },
    userInfoWithImage: {
        flexDirection: "row",
        gap: 15,
        alignItems: "flex-start",
        justifyContent: "center"
    },
    userInfo: {
        gap: 4
    }
})

const useFriendRequestCardStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useFriendRequestCardStyles