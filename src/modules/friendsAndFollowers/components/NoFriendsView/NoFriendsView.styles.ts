import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 50,
  },
  image: {
    width: 120,
    height: 120,
    marginTop: 30,
    marginBottom: 20,
  },

  addFriendsContainerStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    position: 'relative',
  },
  addFriendsStyle: {
    fontSize: 20,
    fontFamily: 'Montserrat-800',
    letterSpacing: 2,
    color: dark.colors.textLight,
    alignSelf: 'center',
    textAlign: 'center',
  },
  subtractText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    letterSpacing: 0,
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: 16,
  },
  findFriendButton: {
    borderWidth: 0.8,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
    height: 36,
  },
  findFriendButtonContianer: {
    width: 112,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginTop: 36,
  },
  findFriendText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    lineHeight: 17,
    color: dark.colors.secondary,
  },
  findFriendBackground: {
    width: 112,
    backgroundColor: dark.colors.tertiary,
    height: 36,
  },
  playWithLinkText: {
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: '20%',
  },
  noProblemText: {
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: 12,
  },
  playWithLinkButton: {
    borderWidth: 0.8,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
    height: 35,
  },
  playWithLinkButtonContianer: {
    width: 112,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginTop: 18,
  },
  playWithLinkTextStyle: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    lineHeight: 17,
    color: dark.colors.textLight,
  },
  playWithLinkBackground: {
    width: 112,
    height: 35,
    backgroundColor: dark.colors.tertiary,
  },
  headerText: {
    fontSize: 16,
    fontFamily: 'Montserrat-900',
    letterSpacing: 1,
    color: dark.colors.textLight,
    marginTop: 20,
  },
  instructionsContainer: {
    width: '100%',
    height: 'auto',
    padding: 10,
    marginTop: 20,
    flexDirection: 'column',
    gap: 12,
  },
  instructionContainer: {
    width: '100%',
    height: 64,
    borderRadius: 12,
    borderWidth: 0.5,
    paddingHorizontal: 12,
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderColor: dark.colors.textDark,
    flexDirection: 'row',
    gap: 10,
  },
  iconFrame: {
    width: 44,
    height: 44,
    borderWidth: 2,
    borderColor: 'black',
    backgroundColor: dark.colors.textLight,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainerStyle: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    position: 'relative',
  },
  keyStyles: {
    alignSelf: 'center',
    fontSize: 28,
    fontFamily: 'Montserrat-900',
    color: dark.colors.victoryColor,
  },
  contentContainer: {
    width: '100%',
    gap: 8,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  stepText: {
    fontFamily: 'Montserrat-500',
    fontSize: 10,
    color: dark.colors.textLight,
    letterSpacing: 1,
    opacity: 0.6,
  },
  textTitleContainer: {
    width: '85%',
  },
  titleText: {
    fontFamily: 'Montserrat-500',
    fontSize: 11,
    color: dark.colors.textLight,
    opacity: 0.9,
  },

  bodyContainer: {
    // height: 'auto',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: 10,
    alignItems: 'center',
  },
  footer: {
    width: '100%',
    // height: "20%",
    marginBottom: 20,
    paddingHorizontal: 8,
    paddingVertical: 10,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  playNowButton: {
    borderWidth: 0.8,
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    height: 50,
  },
  playNowButtonContainer: {
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: 400,
    height: 50,
    borderRadius: 12,
  },
  playNowButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    lineHeight: 17,
    color: dark.colors.textLight,
  },
  playNowBackground: {
    flex: 1,
    backgroundColor: dark.colors.victoryColor,
    borderColor: dark.colors.secondary,
    borderWidth: 1,
  },
  overlayStyle: {
    position: 'absolute',
    top: 94,
    width: '100%',
    maxWidth: 400,
    left: '15%',
    backgroundColor: dark.colors.gradientBackground,
    maxHeight: 400,
    flex: 1,
    flexGrow: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 0,
    borderColor: dark.colors.tertiary,
  },
  backdropStyle: {
    backgroundColor: 'transparent',
  },
  userListStyle: {
    marginTop: 0,
    paddingHorizontal: 16,
  },
  inputBoxContainer: {
    paddingHorizontal: 16,
    width: 'auto',
    maxWidth: 400,
    marginTop: 8,
    height: 'auto',
    justifyContent: 'flex-start',
  },
  inputContainerStyle: {
    borderRadius: 8,
    backgroundColor: dark.colors.primary,
    height: 38,
  },
  inputStyle: {
    color: 'white',
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 20,
  },
  compactOverlay: {
    position: 'absolute',
    width: '100%',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    backgroundColor: dark.colors.gradientBackground,
    height: '100%',
    padding: 0,
  },
  searchBarContainerStyle: {
    flex: 1,
    backgroundColor: dark.colors.background,
    borderWidth: 2,
    padding: 0,
    borderRadius: 8,
    borderTopColor: dark.colors.tertiary,
    borderBottomColor: dark.colors.tertiary,
    borderColor: dark.colors.tertiary,
    height: 40,
  },
});

export default styles;
