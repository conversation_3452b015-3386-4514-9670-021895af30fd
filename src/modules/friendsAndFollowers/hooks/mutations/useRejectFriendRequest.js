import { gql, useMutation } from "@apollo/client";
import { useCallback } from "react";

const REJECT_FRIEND_REQUEST = gql`
  mutation RejectFriendRequest($rejectRequestInput: FriendRequestInput!) {
    rejectFriendRequest(rejectRequestInput: $rejectRequestInput)
  }
`;

export const useRejectFriendRequest = () => {

    const [rejectFriendRequestQuery] = useMutation(REJECT_FRIEND_REQUEST);

    const rejectFriendRequest = useCallback(async ({ senderId }) => {
        const response = await rejectFriendRequestQuery({
            variables: {
                rejectRequestInput: {
                    userId: senderId
                }
            }
        })

        return response
        
    },[rejectFriendRequestQuery])

    return {
        rejectFriendRequest
    }

};
