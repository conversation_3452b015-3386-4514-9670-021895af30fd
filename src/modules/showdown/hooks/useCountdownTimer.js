import { addSeconds, intervalToDuration } from 'date-fns';
import { useEffect, useRef, useState } from 'react';
import { formatTime } from '../utils/time';

const useCountDownTimer = ({ targetTime, targetTimeStamp }) => {
  if (!targetTimeStamp) {
    targetTimeStamp = new Date(targetTime).getTime();
  }

  const [TimeLeft, setTimeLeft] = useState({
    timer: targetTimeStamp - getCurrentTime(),
    formattedTime: formatTime(targetTimeStamp - getCurrentTime()),
  });

  const timerIntervalRef = useRef(null);

  useEffect(() => {
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);

    timerIntervalRef.current = setInterval(() => {
      const timeLeft = targetTimeStamp - getCurrentTime();
      if (timeLeft > 0) {
        setTimeLeft({ timer: timeLeft, formattedTime: formatTime(timeLeft) });
      } else {
        clearInterval(timerIntervalRef.current);
      }
    }, 1000);

    return () => clearInterval(timerIntervalRef.current);
  }, [targetTimeStamp]);

  return TimeLeft;
};

export default useCountDownTimer;
