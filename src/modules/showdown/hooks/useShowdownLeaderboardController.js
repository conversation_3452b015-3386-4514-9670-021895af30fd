import { useMemo } from 'react'
import useShowdownLeaderboard from './query/useLeaderboardDetails'
import useShowdownLeaderboardSubscription from './useShowdownLeaderboardSubscription'
import _isNil from 'lodash/isNil'
import _isEmpty from 'lodash/isEmpty'

const useShowdownLeaderboardController = ({ showdownId, page = 1 }) => {
    const { event, payload } = useShowdownLeaderboardSubscription(
        showdownId,
        page
    )
    const { leaderboard, loading } = useShowdownLeaderboard({ showdownId, page })

    const data = useMemo(()=> {
        if (event ==="LEADERBOARD_UPDATE" && !_isNil(payload) &&!_isEmpty(payload)) {
            return {...leaderboard,...payload}
        } 
        return leaderboard ?? EMPTY_OBJECT
    },[event, payload, leaderboard])

    return {leaderboard: data, loading}
}

export default useShowdownLeaderboardController;
