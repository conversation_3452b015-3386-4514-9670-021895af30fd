import { useMutation, gql } from '@apollo/client'
import { useCallback } from 'react'

const SUBMIT_CONTEST_ANSWER_QUERY = gql`
    mutation SubmitContestAnswer(
        $contestId: ID!
        $questionId: String!
        $answer: String!
    ) {
        submitContestAnswer(
            contestId: $contestId
            questionId: $questionId
            answer: $answer
        )
    }
`

const useSubmitContestAnswer = () => {
    const [submitContestAnswerQuery] = useMutation(SUBMIT_CONTEST_ANSWER_QUERY)

    const submitContestAnswer = useCallback(
        ({ contestId, questionId, answer }) =>
            submitContestAnswerQuery({
                variables: {
                    contestId,
                    questionId,
                    answer,
                },
            }),
        [submitContestAnswerQuery]
    )

    return {
        submitContestAnswer,
    }
}

export default useSubmitContestAnswer
