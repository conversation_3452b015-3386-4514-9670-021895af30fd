import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import { useMemo } from 'react';
import _get from 'lodash/get';

const useRoundDetailInfo = ({ showdown }) => {
  const {
    currentRound,
    rounds,
    roundConfig,
    startTime,
    currentUserParticipation,
  } = showdown ?? EMPTY_OBJECT;
  const { maxGapBwGame, numOfGames, maxWaitTime, gameDuration } =
    roundConfig ?? EMPTY_OBJECT;

  const joinRoundWithinTime = useMemo(() => {
    if (_isNil(showdown) || _isEmpty(showdown)) {
      return 0;
    }
    const roundTime =
      numOfGames * gameDuration + (numOfGames - 1) * maxGapBwGame + maxWaitTime;
    const currentRoundStartTime =
      (currentRound - 1) * roundTime * 1000 + new Date(startTime)?.getTime();
    const joinRoundWithinTime = currentRoundStartTime - maxWaitTime;
    return joinRoundWithinTime ?? getCurrentTime();
  }, [showdown]);

  const participantRoundInfo = _get(
    currentUserParticipation,
    'rounds',
    EMPTY_ARRAY,
  );

  const { currentGame, currentRoundInfo } = useMemo(() => {
    if (_isEmpty(participantRoundInfo)) {
      return { currentGame: 1, currentRoundInfo: {} };
    }

    const currentRoundInfo = participantRoundInfo?.find(
      (item) => item?.round === currentRound,
    );

    if (_isEmpty(currentRoundInfo)) {
      return { currentGame: 1 };
    }
    const currentGame =
      numOfGames === currentRoundInfo?.totalGamesPlayed
        ? currentRoundInfo?.totalGamesPlayed
        : currentRoundInfo?.totalGamesPlayed + 1;
    return { currentGame, currentRoundInfo };
  }, [participantRoundInfo]);

  const currentGameWaitUntil = useMemo(() => {
    return (
      (currentRoundInfo?.totalGamesPlayed * gameDuration +
        (currentRoundInfo?.totalGamesPlayed - 1) * maxGapBwGame +
        maxWaitTime) *
        1000 +
      new Date(startTime).getTime()
    );
  }, [currentRoundInfo, gameDuration, maxGapBwGame]);

  return {
    currentGame,
    participantRoundInfo,
    currentRound,
    numOfGames,
    startTime,
    joinRoundWithinTime,
    currentRoundInfo,
    currentGameWaitUntil,
  };
};

export default useRoundDetailInfo;
