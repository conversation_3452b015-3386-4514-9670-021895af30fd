import { ScrollView, View } from 'react-native'
import Loading from '@/src/components/atoms/Loading'
import styles from '../components/CompactShowdownDetail/CompactShowdownDetail.style'

function CompactContestDetailsShimmer() {
    return (
        <ScrollView contentContainerStyle={styles.container}>
            <Loading label="Loading Contest Details" />
            {/* <View style={styles.header}>
        <ShimmerPlaceHolder style={styles.shimmerHeaderButton} />
        <ShimmerPlaceHolder style={styles.shimmerHeaderText} />
        <ShimmerPlaceHolder style={styles.shimmerHeaderButton} />
      </View>
      <View style={styles.contestDetails}>
        <ShimmerPlaceHolder style={styles.shimmerImage} />
        <ShimmerPlaceHolder style={styles.shimmerTitle} />
        <ShimmerPlaceHolder style={styles.shimmerSubText} />
        <ShimmerPlaceHolder style={styles.shimmerDetailsRow} />
      </View>
      <ShimmerPlaceHolder style={styles.shimmerExpandable} />
  
      <ShimmerPlaceHolder style={styles.shimmerFab} /> */}
        </ScrollView>
    )
}

export default CompactContestDetailsShimmer
