import React from 'react'

import PropTypes from 'prop-types'
import ShowDownCTAsForUnRegisteredUsers from './ShowDownCTAsForUnRegisteredUsers'
import useShowdownCurrentStatus from '../../hooks/useShowdownCurrentStatus'
import ShowDownCTAsForRegisteredUsers from './ShowDownCTAsForRegisteredUsers'

const ShowDownCTAButton = ({ state, onAction }) => {
  const { hasUserRegistered } = useShowdownCurrentStatus({ state })

  if (hasUserRegistered) {
    return <ShowDownCTAsForRegisteredUsers state={state} onAction={onAction} />
  }
  return <ShowDownCTAsForUnRegisteredUsers state={state} onAction={onAction} />
}

ShowDownCTAButton.propTypes = {
  state: PropTypes.object.isRequired,
  onAction: PropTypes.func.isRequired,
}

export default React.memo(ShowDownCTAButton)
