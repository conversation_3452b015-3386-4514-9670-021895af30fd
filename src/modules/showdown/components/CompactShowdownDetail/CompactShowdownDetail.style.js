import { StyleSheet } from 'react-native';

import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  innerContainer: {
    height: 'auto',
    width: '100%',
  },
  gradient: {
    width: '100%',
    height: 320,
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
  },
  container: {
    overflow: 'hidden',
    position: 'relative',
    flex: 1,
    width: '100%',
    height: '100%',
    gap: 4,
    // backgroundColor: dark.colors.primary
  },
  showdownTimerContainer: {
    paddingHorizontal: 16,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 23,
    fontFamily: 'Montserrat-700',
  },
  contestDetails: {
    marginLeft: 16,
    marginRight: 16,
    // marginTop:12,
    width: '100%',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'row',
    gap: 12,
  },
  contestTimeAndDesc: {
    borderRadius: 8,
    flex: 1,
    marginBottom: 10,
    gap: 5,
  },
  contestImage: {
    width: 50,
    height: 50,
    marginBottom: 10,
  },
  contestTitle: {
    color: '#fff',
    fontFamily: 'Montserrat-600',
    fontSize: 16,
    lineHeight: 20,
    flexShrink: 1,
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    flexWrap: 'wrap',
    lineHeight: 12,
    display: 'flex',
    marginRight: 16,
  },
  gradientBox: {
    width: 60,
    height: 60,
    marginBottom: 15,
    borderRadius: 10,
    justifyContent: 'center',
    rotation: 50,
    alignItems: 'center',
    overflow: 'hidden',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    borderRadius: 10,

  },
  detailsRow: {
    width: '95%',
    flexDirection: 'row',
    height: 'auto',
    marginRight: 16,
    gap: 4,
  },
  detailsText: {
    flex: 2,
    color: '#FFFFFF',
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    lineHeight: 15,
    marginBottom: 5,
  },
  expandableContainer: {
    borderRadius: 10,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  expandableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    alignItems: 'center',
    height: 44,
    backgroundColor: dark.colors.primary,
    // paddingVertical: 10,
  },
  expandableText: {
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
    letterSpacing: -0.15,
  },
  expandedContent: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    // backgroundColor: '#2A2A2A',
  },
  contestDetailContainer: {
    paddingVertical: 10,
    paddingBottom: 80,
    width: '100%',
  },
  aboutText: {
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  CTAContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: dark.colors.background,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#00ff66',
    padding: 15,
    borderRadius: 50,
    alignItems: 'center',
    // elevation: 8,
  },
  fabText: {
    fontSize: 16,
    color: dark.colors.card,
    fontFamily: 'Montserrat-600',
  },
  unRegisterButton: {
    backgroundColor: dark.colors.primary,
    paddingVertical: 15,
    borderRadius: 50,
    width: '90%',
    alignItems: 'center',
  },
  unregisterText: {
    fontFamily: 'Montserrat-600',
    fontSize: 13,
    lineHeight: 20,
    color: '#FF7777',
  },
  lockedRegisterButton: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    width: '90%',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  lockedRegisterText: {
    color: dark.colors.text,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  shimmerHeaderButton: {
    width: 25,
    height: 25,
    borderRadius: 12.5,
  },
  shimmerHeaderText: {
    width: 100,
    height: 20,
    borderRadius: 4,
  },
  shimmerImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 10,
  },
  shimmerTitle: {
    width: '60%',
    height: 20,
    borderRadius: 4,
    marginBottom: 5,
  },
  shimmerSubText: {
    width: '40%',
    height: 15,
    borderRadius: 4,
    marginBottom: 10,
  },
  shimmerDetailsRow: {
    width: '100%',
    height: 20,
    borderRadius: 4,
  },
  shimmerExpandable: {
    width: '100%',
    height: 40,
    borderRadius: 4,
    marginTop: 10,
  },
  shimmerFab: {
    width: 150,
    height: 50,
    borderRadius: 25,
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  tabSectionContainer: {
    flex: 1,
  },
  scene: {
    height: 'auto',
    justifyContent: 'center',
  },
  text: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
  },
  tabBarContainer: {
    width: '100%',
    alignSelf: 'flex-start',
    justifyContent: 'space-between',
  },
  tabBar: {
    width: '100%',
    backgroundColor: 'transparent',
    elevation: 0,
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 2,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  indicator: {
    height: 4,
    justifyContent: 'center',
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
    backgroundColor: dark.colors.secondary,
  },
  tabStyle: {
    flexGrow: 1,
    alignItems: 'center',
    overflow: 'hidden',
    justifyContent: 'space-between',
  },
  label: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 20,
  },
  registerNowDetail: {
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
    justifyContent: 'center',
  },
  liveDetail: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  registrationEnds: {
    color: dark.colors.offWhite,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
  },
  daysLeftDetails: {
    color: '#D3BFFF',
    fontSize: 12,
    fontFamily: 'Montserrat-700',
  },
  liveText: {
    color: '#CC1B1B',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  liveContainer: {
    backgroundColor: dark.colors.error,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 3,
    marginRight: 16,
  },
  registeredContainer: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderRadius: 20,
    marginVertical: 10,
  },
  checkedIcon: {
    marginRight: 10,
  },
  showdownDetailBoxContainer: {
    width: '100%',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  showdownDetailBox: {
    borderColor: '#3A3A3A',
    flexDirection: 'row',
    // gap: 13,
    borderWidth: 1,
    marginBottom: 10,
    borderRadius: 8,
    padding: 12,
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
    // marginHorizontal: 16
  },
  registeredMessageText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
