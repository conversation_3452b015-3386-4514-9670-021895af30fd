import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'
import PropTypes from 'prop-types'
import LinearGradient from '../../../../components/atoms/LinearGradient'

const styles = StyleSheet.create({
  gradientBox: {
    width: '88%',
    borderRadius: 16,
    marginBottom: 5,
  },
  innerContainer: {
    borderRadius: 16,
    flex: 1,
    padding: 15,
    margin: 1,
    backgroundColor: '#2E2E2E',
    alignContent: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameAndPosition: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
  },
  itemContainer: {
    alignContent: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    padding: 15,
    borderRadius: 16,
    marginVertical: 8,
    marginHorizontal: 15,
    backgroundColor: '#2E2E2E',
  },
  firstItem: {
    width: '86%',
    borderColor: '#F05F5F',
    borderWidth: 1,
  },
  secondItem: {
    borderColor: dark.colors.tertiary,
    width: '83%',
    borderWidth: 1,
  },
  thirdItem: {
    borderColor: dark.colors.tertiary,
    width: '80%',
    borderWidth: 1,
    shadowColor: 'white',
    shadowOffset: { width: 0, height: 9 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    backgroundColor: '#2F2F2F',
    transform: [{ translateY: 2 }],
  },
  position: {
    color: '#FFF',
    fontFamily: 'Montserrat-700',
    fontSize: 16,
  },
  name: {
    textAlign: 'left',
    maxWidth: 160,
    color: '#FFF',
    fontFamily: 'Montserrat-500',
    fontSize: 16,
  },
  score: {
    maxWidth: 100,
    color: '#FFF',
    fontFamily: 'Montserrat-500',
    fontSize: 16,
  },
})

const LeaderboardItem = ({ name, score, position }) => {
  if (position === 1) {
    return (
      <LinearGradient
        colors={dark.colors.congratulaotory}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientBox}
      >
        <View style={styles.innerContainer}>
          <View style={styles.nameAndPosition}>
            <Text style={styles.position}>#{position}</Text>
            <Text style={styles.name} numberOfLines={1}>
              {name}
            </Text>
          </View>
          <Text style={styles.score} numberOfLines={1}>
            {score}
          </Text>
        </View>
      </LinearGradient>
    )
  }
  return (
    <View
      style={[
        styles.itemContainer,
        position === 1
          ? styles.firstItem
          : position === 2
            ? styles.secondItem
            : position === 3
              ? styles.thirdItem
              : null,
      ]}
    >
      <View style={styles.nameAndPosition}>
        <Text style={styles.position} numberOfLines={1}>
          #{position}
        </Text>
        <Text style={styles.name} numberOfLines={1}>
          {name}
        </Text>
      </View>
      <Text style={styles.score} numberOfLines={1}>
        {score}
      </Text>
    </View>
  )
}

LeaderboardItem.propTypes = {
  name: PropTypes.string.isRequired,
  score: PropTypes.number.isRequired,
  position: PropTypes.number.isRequired,
}

export default React.memo(LeaderboardItem)
