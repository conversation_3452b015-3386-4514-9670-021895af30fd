import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
  infoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 15,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    paddingVertical: 20,
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  eligibilityTextHeading: {
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    fontSize: 16,
    color: dark.colors.textDark,
  },
  eligibilityText: {
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
    fontSize: 14,
    color: dark.colors.textDark,
  },
  infoItem: {
    display: 'flex',
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
})

export default styles
