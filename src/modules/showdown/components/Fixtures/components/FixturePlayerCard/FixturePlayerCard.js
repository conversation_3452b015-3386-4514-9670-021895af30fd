import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';

import PropTypes from 'prop-types';
import styles from './FixturePlayerCard.style';
import showdownParticipantReader from '../../../../readers/showdownParticipantReader';
import _truncate from 'lodash/truncate';

const FixturePlayerCard = ({ participant }) => {
  const userName = _truncate(showdownParticipantReader.username(participant), {
    length: 10,
    omission: '...',
  });

  return (
    <View style={styles.container}>
      <Image
        style={styles.profileImage}
        source={{
          uri: showdownParticipantReader.userImageUri(participant),
        }}
      />
      <Text style={styles.userName}>{userName}</Text>
      <Text style={styles.userRating}>
        {showdownParticipantReader.rating(participant)}
      </Text>
    </View>
  );
};

FixturePlayerCard.propTypes = {
  // participant: PropTypes.shape({
  //   name: PropTypes.string,
  //   rating: PropTypes.number,
  //   profileImageUrl: PropTypes.string,
  // }).isRequired,
};

export default React.memo(FixturePlayerCard);
