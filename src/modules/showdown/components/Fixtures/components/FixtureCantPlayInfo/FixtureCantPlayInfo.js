import React from 'react';
import { Image, Text, View } from 'react-native';

import _isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import styles from './FixtureCantPlayInfo.style';

import showdownReader from '../../../../readers/showdownReader';
import Confetti from '../../../../../../../assets/images/Confetti_SumdayShowdown.png';

const FixtureCantPlayInfo = ({ showdown, userCantPlayConfig }) => {
  if (_isEmpty(userCantPlayConfig) || _isEmpty(showdown)) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.roundInfoText}>
          {' '}
          ROUND {showdownReader.currentRoundNumber(showdown)}
        </Text>
        <Text style={[styles.cardTitle, { color: userCantPlayConfig.color }]}>
          {userCantPlayConfig.title}{' '}
        </Text>
        <Text style={styles.cardDescription}>{userCantPlayConfig.desc} </Text>
        <View
          style={{
            // backgroundColor: 'white',
            position: 'absolute',
            top: -2,
            right: 10,
          }}
        >
          <Image
            source={Confetti}
            style={{
              width: 114,
              height: 114,
              // zIndex: 10,
              // elevation: 4,
            }}
          />
        </View>
      </View>
    </View>
  );
};

FixtureCantPlayInfo.propTypes = {
  showdown: PropTypes.object,
  userCantPlayConfig: PropTypes.shape({
    title: PropTypes.string,
    desc: PropTypes.string,
    color: PropTypes.string,
  }),
};

export default React.memo(FixtureCantPlayInfo);
