import React, { useCallback, useEffect, useState } from 'react';
import { FlatList, Pressable, TextInput, View } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Dark from '@/src/core/constants/themes/dark';
import _filter from 'lodash/filter';
import styles from './ExpandedPeopleList.style';
import { Group } from '../../../types/groups';
import PeopleListCard from '../PeopleListCard';
import ListFooter from '../../ListFooter';
import useChatStore from 'store/useChatStore';
import { useRouter } from 'expo-router';

const ExpandedPeopleList = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const { isMessageGroupsLoading, messageGroupsShown, searchGroups } = useChatStore((state) => ({
    isMessageGroupsLoading: state.isMessageGroupsLoading,
    messageGroupsShown: state.messageGroupsShown,
    searchGroups: state.searchGroups,
  }));

  useEffect(() => {
    searchGroups(searchQuery);
  }, [searchQuery]);

  const renderListItem = useCallback(
    ({ item }: { item: Group }) => (
      <PeopleListCard
        containerStyle={{
          paddingHorizontal: 16,
        }}
        item={item}
        onPress={() => {
          router.replace(`/chat?groupId=${item?._id}`);
        }}
      />
    ),
    [],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={isMessageGroupsLoading} />,
    [isMessageGroupsLoading],
  );

  const onPressCloseIcon = useCallback(() => {
    setSearchQuery('');
  }, [setSearchQuery]);

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholderTextColor={Dark.colors.searchPlaceholder}
          placeholder="Search User..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <Pressable onPress={onPressCloseIcon}>
          <View style={styles.searchIconContainer}>
            <Ionicons
              name={searchQuery ? 'close' : 'search-sharp'}
              style={{ transform: [{ rotate: '5deg' }] }}
              size={18}
              color={Dark.colors.searchPlaceholder}
            />
          </View>
        </Pressable>
      </View>
      <FlatList
        data={messageGroupsShown}
        renderItem={renderListItem}
        keyExtractor={(item, index) => `${item._id}__${index}`}
        // contentContainerStyle={styles.listContainer}
        // onEndReached={loadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={ListFooterComponent as any}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default React.memo(ExpandedPeopleList);
