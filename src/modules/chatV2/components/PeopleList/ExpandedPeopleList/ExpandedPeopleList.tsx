import React, { useCallback, useEffect, useState } from 'react';
import { FlatList, Pressable, TextInput, View } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Dark from '@/src/core/constants/themes/dark';
import _filter from 'lodash/filter';
import styles from './ExpandedPeopleList.style';
import { Group } from '../../../types/groups';
import PeopleListCard from '../PeopleListCard';
import ListFooter from '../../ListFooter';
import useChatStore from 'store/useChatStore';



const ExpandedPeopleList = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const { isMessageGroupsLoading, messageGroupsShown, searchGroups } = useChatStore((state) => ({
    isMessageGroupsLoading: state.isMessageGroupsLoading,
    messageGroupsShown: state.messageGroupsShown,
    searchGroups: state.searchGroups,
  }));

  useEffect(() => {
    searchGroups(searchQuery);
  }, [searchQuery]);

  const renderListItem = useCallback(
    ({ item }: { item: Group }) => (
      <PeopleListCard
        containerStyle={{
          paddingHorizontal: 16,
        }}
        item={item}
        onPress={() => {
          Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICKED_ON_INDIVIDUAL_CHAT);
          router.replace(`/chat?groupId=${item?._id}`);
        }}
      />
    ),
    [],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={isMessageGroupsLoading} />,
    [isMessageGroupsLoading],
  );

  const onPressCloseIcon = useCallback(() => {
    setSearchQuery('');
  }, [setSearchQuery]);

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholderTextColor={Dark.colors.searchPlaceholder}
          placeholder="Search User..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <Pressable onPress={onPressCloseIcon}>
          <View style={styles.searchIconContainer}>
            <Ionicons
              name={searchQuery ? 'close' : 'search-sharp'}
              style={{ transform: [{ rotate: '5deg' }] }}
              size={18}
              color={Dark.colors.searchPlaceholder}
            />
          </View>
        </Pressable>
      </View>
      <FlatList
        data={messageGroupsShown}
        renderItem={renderListItem}
        keyExtractor={(item, index) => `${item._id}__${index}`}
        // contentContainerStyle={styles.listContainer}
        // onEndReached={loadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={ListFooterComponent as any}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default React.memo(ExpandedPeopleList);
