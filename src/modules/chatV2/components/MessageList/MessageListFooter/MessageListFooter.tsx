import React, { useCallback, useRef, useState } from 'react';
import { Image, TextInput, TouchableOpacity, View } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import sendImage from '@/assets/images/message/send.png';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import useSendMessage from 'modules/chatV2/hooks/useSendMessage';
import styles from './MessageListFooter.style';

const Footer = ({ containerStyle = {}, disabled, currentGroupId }: any) => {
  const inputRef = useRef(null);
  const [message, setMessage] = useState('');

  const { isMobile: isCompactMode } = useMediaQuery();

  const { sendMessage } = useSendMessage(currentGroupId);

  const _sendMessage = useCallback(() => {
    sendMessage(message);
    setMessage('');
  }, [sendMessage, message]);

  const handleKeyPress = useCallback(
    (e: any) => {
      if (isCompactMode) {
        return;
      }
      if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        _sendMessage();
        e.preventDefault();
      }
    },
    [_sendMessage, isCompactMode],
  );

  return (
    <View style={[styles.footer, containerStyle]}>
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder="Write your message..."
          placeholderTextColor={Dark.colors.tertiary}
          onChangeText={(text) => {
            setMessage(text);
          }}
          value={message}
          multiline
          maxLength={250}
          onKeyPress={handleKeyPress}
        />
        <TouchableOpacity
          style={[styles.sendButton, disabled && styles?.sendButtonDisabled]}
          // disabled={disabled}
          onPress={() => _sendMessage()}
        >
          <View style={styles.sendButtonGradient}>
            <Image source={sendImage} style={styles.sendButtonImage} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(Footer);
