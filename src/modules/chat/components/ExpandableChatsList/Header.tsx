import React from 'react';
import { View, Text, Image } from 'react-native';
import styles from './ExpandableChatsList.style';

const Header = ({ avatar, name }: { avatar: string; name: string }) => (
  <View style={styles.headerProfile}>
    <View style={styles.headerAvatarContainer}>
      <Image source={{ uri: avatar }} style={styles.headerAvatar} />
    </View>
    <View style={styles.headerInfo}>
      <Text style={styles.headerName}>{name}</Text>
    </View>
  </View>
);

export default React.memo(Header);
