import { useCallback } from 'react';
import _isEmpty from 'lodash/isEmpty';
import { TOAST_TYPE, showToast } from 'molecules/Toast';
import _get from 'lodash/get';
import SHOWDOWN_ACTIONS from '../constants/showdownActions';
import useCTAHandlers from './useCTAHandlers';

const useShowdownActionHandler = () => {
  const {
    onPressRegister,
    onPressUnRegister,
    onPressRegisterGuestUser,
    handleJoinNowGame,
    reFetchShowDown,
    onPressMarkAttendance,
  } = useCTAHandlers();

  const _showToast = ({ message, type }: { message: string; type: any }) => {
    showToast({
      type: type || TOAST_TYPE.ERROR,
      description: message,
    });
  };
  const onAction = useCallback(
    ({ type, payload }: { type: string; payload?: any }) => {
      if (_isEmpty(type)) {
        return;
      }
      switch (type) {
        case SHOWDOWN_ACTIONS.REGISTER_FOR_SHOWDOWN: {
          return onPressRegister();
        }
        case SHOWDOWN_ACTIONS.UN_REGISTER_FROM_SHOWDOWN: {
          return onPressUnRegister();
        }
        case SHOWDOWN_ACTIONS.JOIN_NOW: {
          return handleJoinNowGame();
        }
        case SHOWDOWN_ACTIONS.REGISTER_GUEST_USER: {
          return onPressRegisterGuestUser();
        }
        case SHOWDOWN_ACTIONS.REFETCH_SHOWDOWN: {
          return reFetchShowDown();
        }
        case SHOWDOWN_ACTIONS.MARK_ATTENDANCE: {
          return onPressMarkAttendance();
        }
        case SHOWDOWN_ACTIONS.SHOW_TOAST: {
          const message = _get(payload, 'message', '');
          const _type = _get(payload, 'type', TOAST_TYPE.ERROR);
          return _showToast({ message, type: _type });
        }
        default: {
          throw new Error('Invalid action type');
        }
      }
    },
    [
      onPressRegister,
      onPressUnRegister,
      handleJoinNowGame,
      onPressRegisterGuestUser,
      reFetchShowDown,
      onPressMarkAttendance,
    ],
  );

  return {
    onAction,
  };
};

export default useShowdownActionHandler;
