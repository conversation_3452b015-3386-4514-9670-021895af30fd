import React from 'react';
import useShowdownStore from 'store/useShowdownStore';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView/ErrorView';
import useMediaQuery from 'core/hooks/useMediaQuery';
import CompactShowdownDetails from './CompactShowdownDetails';
import ExpandedShowdownDetails from './ExpandedShowdownDetails';
import useShowdownLifeCycle from '../../hooks/useShowdownLifeCycle';

const ShowdownDetails = ({ showdownId }: { showdownId: string }) => {
  useShowdownLifeCycle(showdownId);

  const { isMobile } = useMediaQuery();

  const { loading, error, isShowdownAvailable } = useShowdownStore((state) => ({
    loading: state.showdownLoading,
    error: state.showdownError,
    isShowdownAvailable: state.isShowdownAvailable,
  }));

  if (loading) {
    return <Loading label="Fetching showdown details" />;
  }

  if (error || !isShowdownAvailable) {
    return (
      <ErrorView errorMessage="Something went Wrong while fetching showdown." />
    );
  }

  return isMobile ? <CompactShowdownDetails /> : <ExpandedShowdownDetails />;
};

export default React.memo(ShowdownDetails);
