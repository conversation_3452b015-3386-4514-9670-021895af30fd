/* eslint-disable import/prefer-default-export */
import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';

export const ShowdownRegistrationCountStyles = StyleSheet.create({
  iconContainer: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
  },
  infoItem: {
    display: 'flex',
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 15,
    padding: 20,
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },

  info: {
    gap: 4,
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  infoText: {
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    color: dark.colors.textDark,
    // marginTop: 5,
  },
  infoNumber: {
    lineHeight: 12,
    color: '#FFF',
    fontSize: 14,
    marginTop: 5,
  },
});
