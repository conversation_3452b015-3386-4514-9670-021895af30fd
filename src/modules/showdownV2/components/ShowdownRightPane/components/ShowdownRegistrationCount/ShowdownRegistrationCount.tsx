import React from 'react';
import { Image, Text, View } from 'react-native';
import groupIcon from 'assets/images/group.png';
import { ShowdownRegistrationCountStyles } from './ShowdownRegistrationCount.style';

const ShowdownRegistrationCount = ({
  registrationCount,
}: {
  registrationCount: number;
}) => (
  <View style={ShowdownRegistrationCountStyles.infoItem}>
    <View style={ShowdownRegistrationCountStyles.iconContainer}>
      <Image
        source={groupIcon}
        style={ShowdownRegistrationCountStyles.iconStyle}
      />
    </View>
    <View style={ShowdownRegistrationCountStyles.info}>
      <Text style={ShowdownRegistrationCountStyles.infoText}>Registered</Text>
      <Text style={ShowdownRegistrationCountStyles.infoNumber}>
        {registrationCount}
      </Text>
    </View>
  </View>
);

export default React.memo(ShowdownRegistrationCount);
