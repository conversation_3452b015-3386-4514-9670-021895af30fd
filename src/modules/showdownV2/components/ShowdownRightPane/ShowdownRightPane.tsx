import React from 'react';
import { View } from 'react-native';
import useShowdownStore from 'store/useShowdownStore';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import ShowdownRegistrationCount from './components/ShowdownRegistrationCount';
import styles from './ShowdownRightPane.style';
import ShowdownCTAButton from '../ShowdownCTAButton';
import ShowdownTimer from '../ShowdownTimer';

const ShowdownDetailsRightPane = () => {
  const { registrationCount } = useShowdownStore((state) => ({
    registrationCount: state.showdown
      ? showdownReader.registrationCount(state.showdown)
      : 0,
  }));

  return (
    <View style={styles.container}>
      <View style={styles.infoContainer}>
        <ShowdownRegistrationCount registrationCount={registrationCount} />
        <ShowdownTimer />
        <View style={{ marginTop: 20, width: '100%' }}>
          <ShowdownCTAButton />
        </View>
      </View>
    </View>
  );
};

export default React.memo(ShowdownDetailsRightPane);
