import { ScrollView } from 'react-native';
import React, { useState } from 'react';
import useShowdownStore from 'store/useShowdownStore';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import showdownReader from '../../readers/showdownReader';
import { ACCORDION } from '../../constants';
import ExpandableContainer from '../ExpandableContainer';
import styles from './SHowdownExpandedTabBar.style';

const DetailsRoute = () => {
  const [activeAccordian, setActiveAccordian] = useState('');
  const { showdownDetails } = useShowdownStore((state) => ({
    showdownDetails: showdownReader.details(state.showdown) ?? EMPTY_OBJECT,
  }));
  const changeActiveAccordian = (accordian: string) => {
    setActiveAccordian((prev) => {
      if (prev === accordian) {
        return '';
      }
      return accordian;
    });
  };
  return (
    <ScrollView
      style={styles.mainContainer}
      showsVerticalScrollIndicator={false}
    >
      {!_isEmpty(ACCORDION)
        ? ACCORDION.map((accordian) => {
            const accordianContent = _get(showdownDetails, accordian.key, '');
            return (
              <ExpandableContainer
                key={`SHOWDOWN_DETAIL_${accordian?.title}`}
                title={accordian?.title}
                isActive={activeAccordian === accordian?.title}
                content={accordianContent}
                changeActiveAccordian={changeActiveAccordian}
              />
            );
          })
        : null}
    </ScrollView>
  );
};

export default React.memo(DetailsRoute);
