import { Text, TouchableOpacity } from 'react-native';
import React, { useCallback } from 'react';
import { TOAST_TYPE } from '@/src/components/molecules/Toast';
import useShowdownStore from 'store/useShowdownStore';
import { SHOWDOWN_PLAYER_STATUS } from 'modules/showdownV2/constants/showdownPlayerStatus';
import {
  SHOWDOWN_PARTICIPANT_STATUS,
  ShowdownState,
} from '@/src/store/useShowdownStore/types';
import { useRegisteredButtonStyles } from './ShowdownCTAButton.style';
import useShowdownActionHandler from '../../hooks/useShowdownActionHandler';
import SHOWDOWN_ACTIONS from '../../constants/showdownActions';
import InactiveButton from './components/InactiveButton';
import showdownReader from '../../readers/showdownReader';
import ActiveButton from './components/ActiveButton';

type ShowdownCTAForRegisteredUsersProps = {
  startTime: string;
  otherStates: ShowdownState['otherStates'];
  playerStatus: string;
  hasJoined: boolean;
  isUnregisteringFromShowDown: boolean;
  participantStatus: SHOWDOWN_PARTICIPANT_STATUS;
  registrationEndTime: number;
};

const labels = {
  [SHOWDOWN_PLAYER_STATUS.DID_NOT_PLAY]: 'Missed The Round',
  [SHOWDOWN_PLAYER_STATUS.BOTH_DID_NOT_PLAY]: 'Both Players missed',
  [SHOWDOWN_PLAYER_STATUS.OPPONENT_ABSENT]: 'Opponent Absent',
  [SHOWDOWN_PLAYER_STATUS.PENDING_JOIN]: 'Pending Join',
  [SHOWDOWN_PLAYER_STATUS.ROUND_COMPLETED]: 'Round Completed',
  [SHOWDOWN_PLAYER_STATUS.BYE]: 'Got Bye',
};

const messages = {
  [SHOWDOWN_PLAYER_STATUS.DID_NOT_PLAY]:
    'You missed this round, You can still join next round.',
  [SHOWDOWN_PLAYER_STATUS.BOTH_DID_NOT_PLAY]:
    'You both did not show up in this round, Please wait for next round to start',
  [SHOWDOWN_PLAYER_STATUS.OPPONENT_ABSENT]:
    'You won the round as your opponent did not show up, Please wait for next round to start',
  [SHOWDOWN_PLAYER_STATUS.ROUND_COMPLETED]: 'Round Completed',
};

const ShowdownCTAForRegisteredUsers = () => {
  const buttonStyles = useRegisteredButtonStyles();
  const { onAction } = useShowdownActionHandler();

  const {
    startTime,
    isUnregisteringFromShowDown,
    otherStates,
    playerStatus,
    hasJoined,
    participantStatus,
    registrationEndTime,
  }: ShowdownCTAForRegisteredUsersProps = useShowdownStore((state) => ({
    isUnregisteringFromShowDown: state.isUnregisteringFromShowDown,
    startTime: showdownReader.startTime(state.showdown),
    otherStates: state.otherStates,
    playerStatus: showdownReader.playerStatus(state.showdown),
    hasJoined: showdownReader.hasCurrentUserJoined(state.showdown),
    participantStatus: showdownReader.currentUserStatus(state.showdown),
    registrationEndTime: new Date(
      showdownReader.registrationEndTime(state.showdown),
    ).getTime(),
  }));

  const onPressUnRegister = useCallback(() => {
    try {
      onAction?.({ type: SHOWDOWN_ACTIONS.UN_REGISTER_FROM_SHOWDOWN });
    } catch (error) {
      console.log(error);
    }
  }, [onAction]);

  const onPressJoinGame = useCallback(() => {
    try {
      onAction?.({ type: SHOWDOWN_ACTIONS.JOIN_NOW });
    } catch (error) {
      console.log(error);
    }
  }, [onAction]);

  const onPressMarkAttendance = useCallback(() => {
    try {
      onAction?.({ type: SHOWDOWN_ACTIONS.MARK_ATTENDANCE });
    } catch (error) {
      console.log(error);
    }
  }, [onAction]);

  const renderUnRegisterButton = useCallback(
    () => (
      <TouchableOpacity
        style={[buttonStyles.button as any, buttonStyles.unRegisterButton]}
        onPress={onPressUnRegister}
      >
        <Text style={buttonStyles.unregisterText}>
          {isUnregisteringFromShowDown ? 'Unregistering...' : 'Unregister'}
        </Text>
      </TouchableOpacity>
    ),
    [buttonStyles, onPressUnRegister, isUnregisteringFromShowDown],
  );

  if (otherStates.hasEnded) {
    return (
      <InactiveButton
        label="Showdown Ended"
        message="Showdown has ended, You cant participate in this Showdown"
        type={TOAST_TYPE.ERROR}
        buttonStyle={[buttonStyles.registerButton, buttonStyles.inactiveButton]}
        textStyle={buttonStyles.inactiveButtonText}
        // timer={new Date(startTime ?? '').getTime()}
      />
    );
  }

  if (
    otherStates.hasAttendanceStarted &&
    participantStatus !== SHOWDOWN_PARTICIPANT_STATUS.ATTENDANCE_MARKED
  ) {
    return (
      <ActiveButton
        label="Mark Attendance"
        buttonStyle={[buttonStyles.joinGameButton]}
        textStyle={buttonStyles.joinGameButtonText}
        timer={registrationEndTime}
        onPress={onPressMarkAttendance}
      />
    );
  }
  if (!otherStates.hasRegistrationEnded) {
    return renderUnRegisterButton();
  }

  if (!otherStates.isLive) {
    return (
      <InactiveButton
        label="Starts in"
        message="Showdown is not live, You cant participate in this Showdown"
        type={TOAST_TYPE.ERROR}
        buttonStyle={[buttonStyles.registerButton, buttonStyles.inactiveButton]}
        textStyle={buttonStyles.inactiveButtonText}
        timer={new Date(startTime ?? '').getTime()}
      />
    );
  }

  if (otherStates.isBreak) {
    return (
      <InactiveButton
        label="Break"
        message="Showdown is on break, You cant participate in this Showdown"
        type={TOAST_TYPE.ERROR}
        buttonStyle={[buttonStyles.registerButton, buttonStyles.inactiveButton]}
        textStyle={buttonStyles.inactiveButtonText}
        timer={otherStates.breakEndsAt}
      />
    );
  }

  if (playerStatus) {
    if (playerStatus === SHOWDOWN_PLAYER_STATUS.PENDING_JOIN) {
      if (!otherStates.canUserJoin && !hasJoined) {
        return (
          <InactiveButton
            label="Next Round In"
            message="You missed this round, You can still join next round"
            type={TOAST_TYPE.ERROR}
            buttonStyle={[
              buttonStyles.registerButton,
              buttonStyles.inactiveButton,
            ]}
            textStyle={buttonStyles.inactiveButtonText}
            timer={otherStates.breakEndsAt}
          />
        );
      }

      return (
        <ActiveButton
          label="Join Now"
          buttonStyle={[buttonStyles.joinGameButton]}
          textStyle={buttonStyles.joinGameButtonText}
          timer={hasJoined ? null : otherStates.joinUntilTimestamp}
          onPress={onPressJoinGame}
        />
      );
    }
    return (
      <InactiveButton
        label={labels[playerStatus]}
        message={messages[playerStatus]}
        type={TOAST_TYPE.ERROR}
        buttonStyle={[buttonStyles.registerButton, buttonStyles.inactiveButton]}
        textStyle={buttonStyles.inactiveButtonText}
        timer={otherStates.breakEndsAt}
      />
    );
  }

  return null;
};

export default React.memo(ShowdownCTAForRegisteredUsers);
