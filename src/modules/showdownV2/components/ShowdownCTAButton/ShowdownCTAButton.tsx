import React from 'react';

import useShowdownStore from 'store/useShowdownStore';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';
import ShowDownCTAsForRegisteredUsers from './ShowdownCTAForRegisteredUsers';
import ShowDownCTAsForUnRegisteredUsers from './ShowdownCTAForUnregisteredUsers';

const ShowDownCTAButton = () => {
  const { user } = useSession();
  const isGuest = userReader.isGuest(user);
  const { hasUserRegistered } = useShowdownStore((state) => ({
    hasUserRegistered: state.otherStates.hasUserRegistered,
  }));

  if (hasUserRegistered) {
    return <ShowDownCTAsForRegisteredUsers />;
  }
  return <ShowDownCTAsForUnRegisteredUsers isGuest={isGuest ?? false} />;
};

export default React.memo(ShowDownCTAButton);
