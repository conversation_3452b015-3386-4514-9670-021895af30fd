import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';
import FixtureAnnouncement from 'assets/images/showdown/fixture_announcement.png';
import TimerText from 'atoms/TimerText';
import styles from './FixtureCreationPending.style';

const FixtureCreationPending = ({
  currentRound,
  timer,
}: {
  currentRound: number;
  timer: number;
}) => {
  const timerComponent = useMemo(
    () => <TimerText timer={timer} style={styles.timerText} />,
    [timer],
  );

  return (
    <View style={styles.container}>
      <View style={{}}>
        <View style={styles.iconContainer}>
          <Image source={FixtureAnnouncement} style={styles.icon} />
        </View>
        <View style={styles.infoContainer}>
          <Text style={styles.roundInfoText}>ROUND {currentRound}</Text>
          <Text style={styles.descText}>
            {timerComponent
              ? 'Fixtures will be out within'
              : 'Fixtures will Be out soon'}
          </Text>
          {timerComponent}
        </View>
      </View>
    </View>
  );
};

export default React.memo(FixtureCreationPending);
