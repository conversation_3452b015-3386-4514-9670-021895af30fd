import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: { flexDirection: 'row', gap: 6, flex: 1 },
  userImage: {
    width: 28,
    height: 28,
    borderRadius: 4,
  },
  userName: {
    color: 'white',
    fontSize: 11,
    lineHeight: 13.5,
  },
  userRating: {
    color: dark.colors.textDark,
    fontSize: 11,
    lineHeight: 13.5,
  },
});

export default styles;
