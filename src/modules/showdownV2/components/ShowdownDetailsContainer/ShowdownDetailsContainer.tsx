import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';
import ShowdownLogo from 'assets/images/showdown_logo.png';
import useShowdownStore from 'store/useShowdownStore';
import _isNaN from 'lodash/isNaN';
import styles from './ShowdownDetailsContainer.style';
import showdownReader from '../../readers/showdownReader';

const getDate = (date: string) =>
  _isNaN(Date.parse(date)) ? 'Invalid date' : date;

const ShowdownDetailCard = React.memo(() => {
  const { showdownName, isLive, hasEnded, roundCount, startTime, duration } =
    useShowdownStore((state) => ({
      showdownName: state.showdown
        ? showdownReader.name(state.showdown) ?? ''
        : '',
      isLive: state.otherStates.isLive ?? false,
      hasEnded: state.otherStates.hasEnded ?? false,
      roundCount: state.showdown
        ? showdownReader.roundsCount(state.showdown)
        : 0,
      startTime: getDate(showdownReader.startTime(state.showdown)),
      duration: state.showdown ? showdownReader.duration(state.showdown) : 0,
    }));

  const headerContent = useMemo(
    () => (
      <View style={styles.header}>
        <View>
          <View style={styles.gradientBox}>
            <Image source={ShowdownLogo} style={styles.iconContainer} />
          </View>
        </View>
        <View>
          <View style={styles.liveDetail}>
            <Text style={styles.title}>{showdownName}</Text>
            {isLive && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            )}
            {hasEnded && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Completed</Text>
              </View>
            )}
          </View>
          <View style={styles.hostDetail}>
            <Text style={styles.hostName}>Hosted By Matiks</Text>
          </View>
        </View>
      </View>
    ),
    [showdownName, isLive, hasEnded],
  );

  const infoRowContent = useMemo(
    () => (
      <View style={styles.infoRow}>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Contest open time</Text>
          <Text style={styles.infoDetails}>
            {new Date(startTime).toLocaleString()}
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Duration</Text>
          <Text style={styles.infoDetails}>
            {Math.round(duration / 60)} Minutes
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Rounds</Text>
          <Text style={styles.infoDetails}>{roundCount}</Text>
        </View>
      </View>
    ),
    [startTime, duration, roundCount],
  );

  return (
    <View style={styles.container}>
      {headerContent}
      {infoRowContent}
    </View>
  );
});

export default React.memo(ShowdownDetailCard);
