import _get from 'lodash/get';

const showdownParticipantReader = {
  userImageUri: (_participant: any) =>
    _get(_participant, ['userInfo', 'profileImageUrl']),
  name: (_participant: any) => _get(_participant, ['userInfo', 'name']),
  username: (_participant: any) => _get(_participant, ['userInfo', 'username']),
  rating: (_participant: any) => _get(_participant, ['userInfo', 'rating']),
};

export default showdownParticipantReader;
