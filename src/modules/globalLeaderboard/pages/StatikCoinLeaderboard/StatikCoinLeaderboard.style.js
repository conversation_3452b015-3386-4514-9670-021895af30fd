import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from '../../../../core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      overflow: 'hidden',
      height: '100%',
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: isCompactMode ? 0 : 24,
    },
    innerContainer: {
      flex: 1,
      paddingHorizontal: 16,
      marginTop: isCompactMode ? 0 : 20,
      width: '100%',
      maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    },
    filterRow: {
      gap: 12,
      flexDirection: 'row',
    },
    listComponent: {
      flex: 1,
    },
    unSelectedFilterContainer: {
      borderWidth: 1,
      borderColor: dark.colors.tertiary,
      backgroundColor: dark.colors.tertiary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 4,
    },
    unSelectedFilterText: {
      fontFamily: 'Montserrat-600',
      fontSize: 14,
      lineHeight: 20,
      color: dark.colors.textDark,
    },
  });

const useStatikCoinLeaderboardStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useStatikCoinLeaderboardStyles;
