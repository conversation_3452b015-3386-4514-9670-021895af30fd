import { gql, useQuery } from '@apollo/client';
import { USER_PUBLIC_DETAIL_FRAGMENT } from 'core/graphql/fragments/userPublicDetail';

const GET_GLOBAL_TOP_PLAYERS = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetGlobalTopPlayers {
    getGlobalTopPlayers {
      globalRating {
        rating
        rank
        user {
          ...UserPublicDetailFields
        }
      }
      memoryRating {
        user {
          ...UserPublicDetailFields
        }
        rating
        rank
      }
      abilityRating {
        user {
          ...UserPublicDetailFields
        }
        rating
        rank
      }
      puzzleRating {
        user {
          ...UserPublicDetailFields
        }
        rating
        rank
      }
    }
  }
`;

const useGlobalTopPlayers = () => {
  const { data, loading, error, refetch } = useQuery(GET_GLOBAL_TOP_PLAYERS, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
  });

  return {
    data: data?.getGlobalTopPlayers,
    loading,
    error,
    refetch,
  };
};

export default useGlobalTopPlayers;
