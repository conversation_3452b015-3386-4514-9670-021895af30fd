import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 20
  },
  podiumContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 180,
    position: 'relative',
  },
  playerAvatarContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
    backgroundColor: dark.colors.primary,
    borderRadius: 50,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  firstPlaceImageContainer: {
    position: 'absolute',
    bottom: 40,
    left: -65,
    width: 240,
    height: 200,
  },
  firstPlaceImage: {
    width: 250,
    height: 250,
    borderRadius: 50,
  },
  playerContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
  },
  rank1Container: {
    zIndex: 3,
    marginBottom: 30,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: dark.colors.firstPlaceBorderColor,
    shadowColor: dark.colors.firstPlaceShadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
  },
  rank2Container: {
    zIndex: 2,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: dark.colors.textLight,
  },
  rank3Container: {
    zIndex: 1,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: dark.colors.thirdPlaceBorderColor,
  },
  playerImage: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  nameText: {
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    color: dark.colors.textLight,
    marginBottom: 2,
  },
  ratingText: {
    fontSize: 10,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
  rankBadge: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    bottom: 40,
    zIndex: 10,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    height: 160,
    gap: 16,
  },
  placeholderAvatar: {
    width: 80,
    height: 80,
    borderRadius: 80,
    backgroundColor: dark.colors.tertiary,
    opacity: 0.3,
  },
  emptyAvatarContainer: {
    width: 90,
    height: 90,
    backgroundColor: 'transparent',
    borderRadius: 50,
    marginHorizontal: 8,
  },
});

export default styles;