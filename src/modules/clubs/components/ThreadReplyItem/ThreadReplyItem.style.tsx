import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 1,
    // paddingHorizontal: 12,
    paddingVertical: 10,
  },
  title: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    color: 'white',
  },
  postedTime: {
    fontFamily: 'Montserrat-400',
    fontSize: 10,
    color: dark.colors.textDark,
  },
  description: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    paddingTop: 5,
    color: 'white',
  },
  userImageStyle: {
    height: 40,
    width: 40,
    borderRadius: 4,
    overflow: 'hidden',
    borderWidth: 0.3,
    borderColor: dark.colors.tertiary,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
});

export default styles;
