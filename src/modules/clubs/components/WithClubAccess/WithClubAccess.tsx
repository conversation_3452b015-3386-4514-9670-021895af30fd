import React, { cloneElement } from 'react';
import Loading from '@/src/components/atoms/Loading';
import _includes from 'lodash/includes';
import { Redirect, usePathname } from 'expo-router';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import { clubReader } from 'core/readers/clubReader';
import useGetClubById from '../../hooks/queries/useGetClubById';
import ClubDetailView from '../../pages/ClubDetailView/ClubDetailView';

const WithClubAccess = (props: any) => {
  const { clubId, children } = props;

  const { clubInfo, error, loading, refetch } = useGetClubById({ clubId });

  const currentUrl: string = usePathname();

  if (loading) {
    return <Loading label="Checking Access...." />;
  }

  if (!clubReader.isClubMember(clubInfo) || !clubReader.isAdmin(clubInfo)) {
    closeRightPane();
  }

  if (_includes(currentUrl, 'create') && !clubReader.isAdmin(clubInfo)) {
    closeRightPane();
    return <Redirect href={`/clubs/${clubId}`} />;
  }

  if (_includes(currentUrl, 'admin') && !clubReader.isAdmin(clubInfo)) {
    closeRightPane();
    return <Redirect href={`/clubs/${clubId}`} />;
  }

  if (!clubReader.isClubMember(clubInfo)) {
    return <ClubDetailView clubId={clubId} />;
  }

  return cloneElement(children, {
    clubId,
    ...props,
    clubInfo,
  });
};

export default WithClubAccess;
