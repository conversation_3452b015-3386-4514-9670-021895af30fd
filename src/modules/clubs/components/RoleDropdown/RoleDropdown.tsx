import React from 'react';
import { Picker } from '@react-native-picker/picker';

interface RoleDropdownProps {
  value: string;
  onChange: (value: string) => void;
}

const RoleDropdown: React.FC<RoleDropdownProps> = ({ value, onChange }) => (
  <Picker selectedValue={value} onValueChange={onChange}>
    <Picker.Item label="Admin" value="admin" />
    <Picker.Item label="Sub-Admin" value="sub-admin" />
    <Picker.Item label="Member" value="member" />
  </Picker>
);

export default RoleDropdown;
