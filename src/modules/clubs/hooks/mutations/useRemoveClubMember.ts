import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const REMOVE_CLUB_MEMBER_MUTATION = gql`
  mutation RemoveClubMember($clubId: ID!, $userId: ID!) {
    removeClubMember(clubId: $clubId, userId: $userId)
  }
`;

const useRemoveClubMember = () => {
  const [removeClubMemberQuery, { loading, error }] = useMutation(
    REMOVE_CLUB_MEMBER_MUTATION,
  );

  const { userId } = useSession();

  const [isRemovingClubMember, setIsRemovingClubMember] = useState(false);

  const removeClubMember = useCallback(
    async ({ clubId }: { clubId: string }) => {
      if (isRemovingClubMember) {
        return false;
      }

      try {
        setIsRemovingClubMember(true);
        await removeClubMemberQuery({
          variables: {
            clubId,
            userId,
          },
        });
        setIsRemovingClubMember(false);
        return true;
      } catch (e) {
        setIsRemovingClubMember(false);
        return false;
      } finally {
        setIsRemovingClubMember(false);
      }
    },
    [removeClubMemberQuery, isRemovingClubMember, userId],
  );

  return {
    removeClubMember,
    isRemovingClubMember,
    error,
  };
};

export default useRemoveClubMember;
