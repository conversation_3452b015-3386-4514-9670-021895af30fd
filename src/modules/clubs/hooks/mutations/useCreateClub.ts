import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';
import _isNil from 'lodash/isNil';
import { base64ToFile } from 'core/utils/base64DataToFile';
import uuid from 'react-native-uuid';
import _isEmpty from 'lodash/isEmpty';
import useGetClubs from 'modules/clubs/hooks/queries/useGetClubs';

const CREATE_CLUB_MUTATION = gql`
  mutation CreateClub($input: CreateClubInput!) {
    createClub(input: $input) {
      id
      name
      description
      visibility
      logoImage
      bannerImage
      category
      createdAt
      createdBy
      forumId
      chatRoomId
      updatedAt
      membersCount
      clubEventsCount
    }
  }
`;

export type Visibility = 'PUBLIC' | 'PRIVATE';

interface ClubInfoInput {
  name: string;
  description?: string;
  visibility: Visibility;
  logoImage?: any;
  bannerImage?: any;
  category: string;
}

const useCreateClub = () => {
  const [createClubMutationQuery, { error }] =
    useMutation(CREATE_CLUB_MUTATION);

  const [isCreatingClub, setIsCreatingClub] = useState<boolean>(false);

  const { updateClubsListCache } = useGetClubs({ pageSize: 50 });

  const createClub = useCallback(
    async ({ clubInfo }: { clubInfo: ClubInfoInput }) => {
      if (isCreatingClub) {
        return false;
      }
      if (!_isNil(clubInfo.logoImage)) {
        clubInfo.logoImage = await base64ToFile({
          fileUri: clubInfo.logoImage,
          fileName: uuid.v4(),
        });
      }

      if (!_isNil(clubInfo.bannerImage)) {
        clubInfo.bannerImage = await base64ToFile({
          fileUri: clubInfo.bannerImage,
          fileName: uuid.v4(),
        });
      }

      try {
        setIsCreatingClub(true);
        const response = await createClubMutationQuery({
          variables: {
            input: {
              ...clubInfo,
            },
          },
        });

        const { data } = response;
        const { createClub: club } = data ?? EMPTY_OBJECT;

        if (_isEmpty(club)) {
          return false;
        }

        updateClubsListCache({
          addedItems: [
            {
              ...club,
              membersCount: 1,
              isAdmin: true,
              isClubMember: true,
              hasRequestedToJoin: false,
            },
          ],
          pageNumber: 1,
        });
        return true;
      } catch (e) {
        setIsCreatingClub(false);
        return false;
      } finally {
        setIsCreatingClub(false);
      }
    },
    [createClubMutationQuery, isCreatingClub],
  );

  return {
    createClub,
    isCreatingClub,
    error,
  };
};

export default useCreateClub;
