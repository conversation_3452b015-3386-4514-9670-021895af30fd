import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _get from 'lodash/get';

const DEFAULT_PAGE_SIZE = 50;

const GET_FORUM_THREADS = gql`
  query ForumThreads($forumId: ID!, $page: Int, $pageSize: Int) {
    forumThreads(forumId: $forumId, page: $page, pageSize: $pageSize) {
      results {
        id
        forumId
        title
        content
        createdAt
        createdBy
        creatorInfo {
          username
          profileImageUrl
        }
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

export interface CreatorInfo {
  username: string;
  profileImageUrl: string;
}

export interface Thread {
  id: string;
  forumId: string;
  title: string;
  content: string;
  createdAt: string;
  createdBy: string;
  creatorInfo: CreatorInfo;
}

const useGetForumThreads = ({
  forumId,
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  forumId?: string;
  pageSize?: number;
}) => {
  const [fetchForumThreadsQuery, { loading, error, client, data }] =
    useLazyQuery(GET_FORUM_THREADS, {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    });

  const fetchThreads = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      if (loading) return;

      return fetchForumThreadsQuery({
        variables: {
          forumId,
          page: pageNumber,
          pageSize,
        },
      });
    },
    [fetchForumThreadsQuery, forumId, loading, pageSize],
  );

  const updateForumThreadsCache = useCallback(
    ({
      updateCacheForumId,
      addedItems = [],
      removedItemIds = [],
      pageNumber = 1,
    }: {
      addedItems?: any[];
      removedItemIds?: string[];
      pageNumber: number;
      updateCacheForumId: string;
    }) => {
      client.cache.updateQuery(
        {
          query: GET_FORUM_THREADS,
          variables: {
            page: pageNumber,
            pageSize,
            forumId: updateCacheForumId,
          },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { forumThreads } = data ?? EMPTY_OBJECT;

          const { results, totalResults } = forumThreads ?? EMPTY_OBJECT;

          if (_isEmpty(forumThreads)) {
            return data;
          }

          let updatedResults = results;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?.id),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          return {
            ...data,
            forumThreads: {
              ...forumThreads,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
        },
      );
    },
    [client.cache, pageSize],
  );

  return {
    threads: _get(data, 'forumThreads.results'),
    loading,
    error,
    fetchThreads,
    updateForumThreadsCache,
  };
};

export { useGetForumThreads };
