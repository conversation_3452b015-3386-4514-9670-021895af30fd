import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 18,
    width: '100%',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  headerTitle: {
    fontSize: 17,
    fontFamily: 'Montserrat-500',
    color: 'white',
    alignSelf: 'center',
  },
  joinButtonText: {
    fontFamily: 'Montserrat-400',
    color: dark.colors.secondary,
    fontSize: 12,
    // lineHeight: 20,
    textAlign: 'center',
  },
  joinButtonStyle: {
    borderRadius: 20,
    borderWidth: 2,
    borderColor: dark.colors.secondary,
    backgroundColor: dark.colors.gradientBackground,
    paddingHorizontal: 16,
    height: 30,
  },
  disabledButton: {
    borderColor: dark.colors.tertiary,
    opacity: 1,
    backgroundColor: dark.colors.gradientBackground,
  },
  disabledButtonText: {
    fontFamily: 'Montserrat-400',
    color: dark.colors.textDark,
    fontSize: 12,
  },
});

export default styles;
