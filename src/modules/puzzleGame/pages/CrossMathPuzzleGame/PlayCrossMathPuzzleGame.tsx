import { View } from 'react-native';
import React from 'react';
import usePuzzleGameQuestionsState from '../../hooks/usePuzzleGameQuestionState';
import CrossMathPuzzleGameQuestion from '../../components/CrossMathPuzzleGameQuestion';
import CrossMathPuzzleGameHeader from '../../components/CrossMathPuzzleGameHeader';
import usePuzzleGameWaitingTimer from '../../hooks/usePuzzleGameWaitingTimer';

const PlayCrossMathPuzzleGame = React.memo(({ game }: { game: any }) => {
  const { playersScores } = usePuzzleGameQuestionsState();

  const { isReady, renderWaitingTimer } = usePuzzleGameWaitingTimer({
    game,
  });

  const renderHeader = () => (
    <View
      style={[
        {
          paddingHorizontal: 16,
          paddingBottom: 15,
          paddingTop: 12,
          width: '100%',
          maxWidth: 480,
        },
      ]}
    >
      <CrossMathPuzzleGameHeader playersScores={playersScores} />
    </View>
  );

  if (!isReady)
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {renderHeader?.()}
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
          }}
        >
          {renderWaitingTimer?.()}
        </View>
      </View>
    );

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      {renderHeader?.()}
      <CrossMathPuzzleGameQuestion />
    </View>
  );
});

export default React.memo(PlayCrossMathPuzzleGame);
