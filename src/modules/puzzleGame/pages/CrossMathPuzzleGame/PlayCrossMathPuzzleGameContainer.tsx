import getCurrentTimeWithOffset from '@/src/core/utils/getCurrentTimeWithOffset';
import _toNumber from 'lodash/toNumber';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Loading from 'atoms/Loading';
import { Redirect } from 'expo-router';
import ErrorView from 'atoms/ErrorView';
import PlayCrossMathPuzzleGame from 'modules/puzzleGame/pages/CrossMathPuzzleGame/PlayCrossMathPuzzleGame';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useEndPuzzleGameQuery from '../../hooks/mutations/useEndPuzzleGame';
import usePuzzleGameContext from '../../hooks/usePuzzleGameContext';
import { PUZZLE_GAME_STATUS } from '../../constants/puzzleGame';

const MAX_WAITING_TIME = 5000; // 5 seconds
const MAX_TIME_TO_END_GAME = 3000; // 3 seconds

const PlayGameContainer = ({ game }) => {
  const { reFetchGame } = usePuzzleGameContext();
  const { startTime, config, _id: gameId, gameStatus, gameType } = game;
  const startTimeDate = new Date(startTime);
  const { timeLimit } = config;
  const { endPuzzleGame: endGame, endingGame } = useEndPuzzleGameQuery();

  const currentTime = getCurrentTimeWithOffset();
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;

  const fetchGameTimeoutRef = useRef();

  const [gameEndedForUser, setGameEndedForUser] = useState(
    currentTime > endTime,
  );

  const handleEndGame = useCallback(() => {
    if (gameStatus !== PUZZLE_GAME_STATUS.ENDED) {
      endGame({ gameId });
    }
  }, [gameStatus, endGame, gameId]);

  const handleEndGameRef = useRef(handleEndGame);
  handleEndGameRef.current = handleEndGame;

  const endGameTimeoutRef = useRef();

  const reFetechGameRef = useRef(reFetchGame);
  reFetechGameRef.current = reFetchGame;

  useEffect(() => {
    const timeToGameEnd = endTime - getCurrentTimeWithOffset();

    const timeToReFetchGame = timeToGameEnd + MAX_WAITING_TIME;

    if (endTime > currentTime) {
      setTimeout(() => setGameEndedForUser(true), timeToGameEnd);

      endGameTimeoutRef.current = setTimeout(() => {
        Analytics.track(
          ANALYTICS_EVENTS.PUZZLE_GAME.CALLING_END_GAME_FROM_CLIENT,
        );
        handleEndGameRef.current();
      }, timeToGameEnd + MAX_TIME_TO_END_GAME);

      if (fetchGameTimeoutRef.current) {
        clearTimeout(fetchGameTimeoutRef.current);
      }

      fetchGameTimeoutRef.current = setTimeout(() => {
        reFetechGameRef.current();
      }, timeToReFetchGame);
    }

    return () => {
      clearTimeout(fetchGameTimeoutRef.current);
      clearTimeout(endGameTimeoutRef.current);
    };
  }, [endTime]);

  if (
    (endTime <= currentTime || gameEndedForUser) &&
    gameStatus === PUZZLE_GAME_STATUS.STARTED
  ) {
    return <Loading label="Calculating result..." />;
  }

  if (gameStatus === PUZZLE_GAME_STATUS.ENDED) {
    return <Redirect href={`/puzzle-game/result/${gameId}`} />;
  }

  if (gameStatus !== PUZZLE_GAME_STATUS.STARTED) {
    return <ErrorView errorMessage="Something went wrong" />;
  }

  return <PlayCrossMathPuzzleGame game={game} />;
};

export default React.memo(PlayGameContainer);
