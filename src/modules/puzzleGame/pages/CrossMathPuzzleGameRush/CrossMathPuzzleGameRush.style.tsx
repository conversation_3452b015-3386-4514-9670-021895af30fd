import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    gap: 10,
    width: '100%',
    maxWidth: 420,
  },
  headerText: {
    fontSize: 13,
    fontFamily: 'Montserrat-400',
    color: 'white',
    textAlign: 'center',
  },
  timerContainer: {
    width: 80,
    height: 35,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    backgroundColor: 'transparent',
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  timerText: {
    marginBottom: -1,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.timerColor,
  },
  bottomButtonLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: 'white',
    alignSelf: 'center',
    opacity: 0.9,
  },
  bottomButtonStyle: {
    flex: 1,
    backgroundColor: dark.colors.card,
    paddingHorizontal: 8,
    minWidth: 100,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: dark.colors.defeatColor,
    borderRadius: 10,
    borderWidth: 1,
    maxWidth: 100,
    borderRadius: 12,
  },
  bottomButtonBackgroundStyle: {
    flex: 1,
    minWidth: 100,
    maxWidth: 100,
    height: 30,
    backgroundColor: dark.colors.error,
    opacity: 0.5,
    borderRadius: 13,
  },
});

export default styles;
