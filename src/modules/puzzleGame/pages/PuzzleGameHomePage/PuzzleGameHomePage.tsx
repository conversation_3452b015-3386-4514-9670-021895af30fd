import React, { useCallback } from 'react';
import { ScrollView, View } from 'react-native';
import GameType from 'modules/home/<USER>/GameConfigs/GameType/GameType';
import _map from 'lodash/map';
import {
  PUZZLE_GAME_DEFAULT_TIME_CONFIG,
  PUZZLE_GAME_TYPES,
} from 'modules/home/<USER>/puzzleGameTypes';
import { router } from 'expo-router';
import useCreatePuzzleGame from 'modules/puzzleGame/hooks/mutations/useCreatePuzzleGame';
import _get from 'lodash/get';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useSession } from 'modules/auth/containers/AuthProvider';
import UserRatingCardWithBgLines from '@/src/components/UserRatingCardWithBgLines';
import userReader from 'core/readers/userReader';
import dark from 'core/constants/themes/dark';
import styles from './PuzzleGameHomePage.style';

const VALID_PUZZLE_GAME_TYPES = [
  PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_DUEL,
  PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND,
];

const PuzzleGameHomePage = () => {
  const { createPuzzleGame } = useCreatePuzzleGame();
  const { user } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const handleGameTypeSelect = useCallback(
    (gameType) => {
      if (gameType === PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_DUEL) {
        router.push(`/puzzle/search-opponent`);
      }
      if (gameType === PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND) {
        createPuzzleGame({
          gameType: PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND,
          timeLimit: PUZZLE_GAME_DEFAULT_TIME_CONFIG,
        }).then((res) => {
          const { data } = res ?? EMPTY_OBJECT;
          const { game } = data ?? EMPTY_OBJECT;
          const gameId = _get(game, '_id');
          if (game) {
            router.push(`/puzzle-game/game/${gameId}`);
          }
        });
      }
    },
    [createPuzzleGame],
  );

  return (
    <View style={styles.container}>
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          paddingTop: 20,
          paddingBottom: 20,
        }}
      >
        <UserRatingCardWithBgLines
          ratingLabel="PUZZLE RATING"
          userRating={userReader.puzzleRating(user)}
          textStyle={{ color: dark.colors.puzzle.primary }}
        />
      </View>
      <ScrollView>
        <View
          style={[
            {
              paddingHorizontal: 16,
              gap: 15,
              paddingTop: 10,
              flexDirection: 'column',
            },
            !isCompactMode && {
              flexDirection: 'row',
              flexWrap: 'wrap',
            },
          ]}
        >
          {_map(VALID_PUZZLE_GAME_TYPES, (gameType) => (
            <GameType
              key={gameType}
              gameType={gameType}
              isSelected={false}
              setSelectedGameType={() => handleGameTypeSelect(gameType)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(PuzzleGameHomePage);
