import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import _map from 'lodash/map';
import _pick from 'lodash/pick';
import _size from 'lodash/size';
import _reduce from 'lodash/reduce';
import _isEmpty from 'lodash/isEmpty';

import { useSession } from 'modules/auth/containers/AuthProvider';
import useFetchUserByUserIdQuery from 'core/hooks/useFetchUserByUserIdQuery';

const usePuzzleGamePlayers = ({ game = EMPTY_OBJECT }) => {
  const { players } = game;

  const playersSize = _size(players);

  const {
    user: currentUser,
    updateCurrentUser,
    userId: currentUserId,
  } = useSession() ?? EMPTY_OBJECT;

  const { fetchUserById } = useFetchUserByUserIdQuery();

  const [resolvedPlayers, setResolvedPlayers] = useState({
    [currentUserId]: currentUser,
  });

  const playerIds = useMemo(() => {
    if (_isEmpty(players)) return EMPTY_ARRAY;

    const _playerIds = _map(players, 'userId');
    return _playerIds.sort((a, b) => (a === currentUserId ? -1 : 1));
  }, [currentUserId, players]);

  const userIdsToFetch: any = useMemo(
    () =>
      _reduce(
        players,
        (acc: any, player) => {
          const { userId } = player;
          if (!_isEmpty(resolvedPlayers[userId])) {
            return acc;
          }
          acc.push(userId);
          return acc;
        },
        [],
      ),
    [resolvedPlayers, players],
  );

  const fetchAndUpdateUsers = useCallback(
    ({ userIds, queryOptions }: { userIds: any; queryOptions?: any }) => {
      const promises = _map(userIds, (userId) =>
        fetchUserById({ userId, queryOptions }),
      );
      Promise.all(promises).then((responses) => {
        const usersMap = _reduce(
          responses,
          (acc: any, response: any) => {
            const resolvedUser = response?.data?.user;
            acc[resolvedUser?._id] = resolvedUser;

            if (resolvedUser?._id === currentUser?._id) {
              updateCurrentUser(resolvedUser);
            }
            return acc;
          },
          {},
        );
        setResolvedPlayers((prevResolvedPlayers) =>
          _pick(
            {
              ...prevResolvedPlayers,
              ...usersMap,
            },
            playerIds,
          ),
        );
      });
    },
    [setResolvedPlayers, fetchUserById, currentUser, playerIds],
  );

  const resolveUsers = useCallback(
    () => fetchAndUpdateUsers({ userIds: userIdsToFetch }),
    [fetchAndUpdateUsers, userIdsToFetch],
  );

  const refreshAllPlayers = useCallback(() => {
    const userIds = _map(players, 'userId');
    fetchAndUpdateUsers({
      userIds,
      queryOptions: { fetchPolicy: 'network-only' },
    });
  }, [players, fetchAndUpdateUsers]);

  const resolveUsersRef = useRef(resolveUsers);
  resolveUsersRef.current = resolveUsers;

  useEffect(() => {
    resolveUsersRef.current();
  }, [playersSize]);

  return {
    players: resolvedPlayers,
    refreshAllPlayers,
  };
};

export default usePuzzleGamePlayers;
