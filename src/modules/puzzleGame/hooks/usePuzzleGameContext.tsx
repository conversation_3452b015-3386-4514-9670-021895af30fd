import React, { useContext, useEffect, useMemo, useState } from 'react';

import _values from 'lodash/values';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';
import usePrevious from 'core/hooks/usePrevious';
import _isEqual from 'lodash/isEqual';
import puzzleGameReader from 'core/readers/puzzleGameReader';
import _includes from 'lodash/includes';
import { PuzzleGame } from 'modules/puzzleGame/types/puzzleGame';
import useGamePlayers from './queries/usePuzzleGamePlayers';
import usePuzzleGameEventSubscription from './usePuzzleGameEventsSubscription';
import useFetchGameQuery from './queries/useFetchPuzzleGameQuery';
import PuzzleGameContext, {
  PuzzleGameContextProvider,
} from '../contexts/puzzleGameContext';

const VALID_GAME_KEYS_TO_UPDATE = ['gameStatus', 'players'];

const usePuzzleGame = ({ gameId }: { gameId: string }) => {
  const {
    game: initialGame,
    loading,
    error,
    reFetchGame,
  } = useFetchGameQuery({ gameId });

  const prevInitialGame = usePrevious(initialGame);

  const [currentGame, setCurrentGame] = useState<PuzzleGame>(initialGame);

  const {
    game: updatedGame,
    event,
    submitAnswer,
  } = usePuzzleGameEventSubscription({
    gameId,
    gameStatus: puzzleGameReader.gameStatus(currentGame),
  });

  const game = useMemo(() => {
    const tempGame = currentGame ?? initialGame ?? updatedGame;
    if (puzzleGameReader.id(tempGame) !== gameId) {
      return undefined;
    }
    return tempGame;
  }, [gameId, currentGame, initialGame, updatedGame]);

  const { players, refreshAllPlayers } = useGamePlayers({
    game,
  });

  useEffect(() => {
    if (
      (_isNil(prevInitialGame) && !_isNil(initialGame)) ||
      (!_isEqual(prevInitialGame, initialGame) && !_isNil(initialGame))
    ) {
      setCurrentGame((prevData: any) => {
        const gameDataToMerge = _reduce(
          initialGame,
          (acc: any, val, key) => {
            if (
              !_isNil(val) &&
              (_isNil(prevData?.[key]) ||
                (!_isNil(val) && _includes(VALID_GAME_KEYS_TO_UPDATE, key)))
            ) {
              acc[key] = val;
            }
            return acc;
          },
          {},
        );
        return { ...prevData, ...gameDataToMerge };
      });
    }
  }, [prevInitialGame, initialGame]);

  useEffect(() => {
    if (_isNil(updatedGame)) return;
    const gameDataToMerge = _reduce(
      updatedGame,
      (acc: any, val, key) => {
        if (!_isNil(val)) {
          acc[key] = val;
        }
        return acc;
      },
      {},
    );
    setCurrentGame((prevGame) => ({ ...prevGame, ...gameDataToMerge }));
  }, [updatedGame]);

  useEffect(() => {
    setCurrentGame((prevGame) => {
      if (puzzleGameReader.id(prevGame) !== gameId) {
        return undefined;
      }
      return prevGame;
    });
  }, [gameId]);

  return {
    game,
    updatedGame: updatedGame ?? game,
    gameEvent: event,
    players: _values(players),
    refreshAllPlayers,
    submitAnswer,
    reFetchGame,
    gameMeta: {
      loading,
      error,
    },
  };
};

export const WithPuzzleGameContext = (Component: any) => {
  const PuzzleGameContextWrapper = (props: any) => {
    const { gameId } = props ?? EMPTY_OBJECT;
    const contextValue = usePuzzleGame({ gameId });
    return (
      <PuzzleGameContextProvider value={contextValue}>
        <Component {...props} />
      </PuzzleGameContextProvider>
    );
  };

  return React.memo(PuzzleGameContextWrapper);
};

const usePuzzleGameContext = () => useContext(PuzzleGameContext);

export default usePuzzleGameContext;
