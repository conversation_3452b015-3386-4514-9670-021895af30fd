import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const REJECT_CHALLENGE_REQ_OF_PUZZLE_GAME_MUTATION = gql`
  mutation RejectChallengeOfPuzzleGame($gameId: ID!) {
    rejectChallenge(gameId: $gameId)
  }
`;

const useRejectChallengeRequestOfPuzzleGame = () => {
  const [rejectChallengeRequestOfPuzzleGameQuery, { loading }] = useMutation(
    REJECT_CHALLENGE_REQ_OF_PUZZLE_GAME_MUTATION,
  );

  const rejectChallengeRequestOfPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      rejectChallengeRequestOfPuzzleGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      }),
    [rejectChallengeRequestOfPuzzleGameQuery],
  );

  return {
    rejectChallengeRequestOfPuzzleGame,
    isRejectingChallenge: loading,
  };
};

export default useRejectChallengeRequestOfPuzzleGame;
