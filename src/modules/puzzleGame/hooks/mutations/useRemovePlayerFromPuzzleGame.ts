import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';

const REMOVE_PLAYER_MUTATION = gql`
  mutation RemovePlayerFromPuzzleGame($gameId: ID!, $playerId: ID!) {
    removePlayerFromPuzzleGame(gameId: $gameId, playerId: $playerId)
  }
`;

const useRemovePlayerFromPuzzleGame = () => {
  const [removePlayerFromPuzzleGameQuery] = useMutation(REMOVE_PLAYER_MUTATION);
  const [removingPlayerFromPuzzleGame, setIsRemovingPlayerFromPuzzleGame] =
    useState(false);

  const removePlayerFromPuzzleGame = useCallback(
    async ({ gameId, playerId }: { gameId: string; playerId: string }) => {
      if (removingPlayerFromPuzzleGame) {
        return false;
      }
      try {
        setIsRemovingPlayerFromPuzzleGame(true);
        const responseOfRemovePlayer = await removePlayerFromPuzzleGameQuery({
          variables: {
            gameId,
            playerId,
          },
        });
        const { data } = responseOfRemovePlayer ?? EMPTY_OBJECT;

        setIsRemovingPlayerFromPuzzleGame(false);

        return data?.removePlayerFromPuzzleGame ?? false;
      } catch (e) {
        setIsRemovingPlayerFromPuzzleGame(false);
        return false;
      } finally {
        setIsRemovingPlayerFromPuzzleGame(false);
      }
    },
    [removePlayerFromPuzzleGameQuery, removingPlayerFromPuzzleGame],
  );

  return {
    removePlayerFromPuzzleGame,
    removingPlayerFromPuzzleGame,
  };
};

export default useRemovePlayerFromPuzzleGame;
