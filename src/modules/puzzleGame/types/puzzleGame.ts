export interface PuzzleGameConfig {
  timeLimit: number;
  numPlayers: number;
  maxTimePerQuestion: number;
}

export interface PuzzleGameLeaderboard {
  userId: string;
  correct: number;
  incorrect: number;
  totalPoints: number;
  ratingChange: number;
  statikCoinsEarned: number;
  rank: number;
}

export interface PuzzleGamePlayer {
  userId: string;
  rating: number;
  statikCoins: number;
  status: string;
  timeLeft: number;
}

export interface PuzzleGame {
  _id: string;
  players: PuzzleGamePlayer[];
  gameStatus: string;
  rematchRequestedBy: string;
  gameType: string;
  createdBy: string;
  config: PuzzleGameConfig;
  minifiedQuestions: string;
  questions: any[];
  encryptedQuestions: string[];
  leaderBoard: PuzzleGameLeaderboard[];
  startTime: string;
  endTime: string;
  seriesId: string;
}
