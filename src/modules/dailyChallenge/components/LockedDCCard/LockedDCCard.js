import { View, Text, TouchableOpacity, Image } from "react-native"
import { getFormattedDate } from "../../hooks/useLeaderboardSwitcher"
import styles from "./LockedDCCard.style"
import { useCallback } from "react"
import divisionIcon from 'assets/images/dc/division_icon.png'
import FontAwesome6 from '@expo/vector-icons/FontAwesome6'
import useMediaQuery from "../../../../core/hooks/useMediaQuery.js"
import GoogleLoginButton from "../../../../core/oauth/components/GoogleLoginButton"

import EvilIcons from '@expo/vector-icons/EvilIcons';
import dark from "../../../../core/constants/themes/dark.js"
import { getAdditionalChallengeFromRating } from "../../hooks/useGetEligibleDailyChallenges.js"
import { useSession } from "../../../auth/containers/AuthProvider.js"
import Analytics from "../../../../core/analytics";
import {ANALYTICS_EVENTS} from "../../../../core/analytics/const";
import {PAGE_NAME_KEY, PAGE_NAMES} from "../../../../core/constants/pageNames";
import TextWithShadow from "@/src/components/shared/TextWithShadow"
import groupIcon from '@/assets/images/dc/group_icon.png'

const LockedDCCard = () => {
    const { user } = useSession()
    const { isGuest, rating } = user ?? EMPTY_OBJECT

    const { isMobile: isCompactDevice } = useMediaQuery()

    const eligbleChallenge = getAdditionalChallengeFromRating({ rating })

    const renderButtonComponent = useCallback(() => {
        return (
        <View style={[styles.container, { borderWidth: 0, width: isCompactDevice ? 271 : 260 }]}>
            <View style={{ position: 'relative' }}>
                <View style={styles.imageContainer}>
                    <Image source={groupIcon} style={{ height: 24, width: 24 }} />
                </View>
            </View>
            <View style={{  flex: 1 }}>
                <TextWithShadow
                    text={eligbleChallenge}
                    textStyle={styles.titleText}
                    shadowOffsetY={-3}
                    shadowOffsetX={0}
                    shadowColor={dark.colors.background}
                />
                <Text style={styles.subTitle}>BLITZ MODE COMPETITION</Text>
            </View>
            
            <View style={[styles.overlay, {width: isCompactDevice ? 271 : 260, zIndex: 10}]}>
                <View style={[styles.lockedContainer]}>
                    <EvilIcons name="lock" size={25} color={dark.colors.textDark} />
                    <Text style={styles.buttonText}>
                        Sign In to Unlock
                    </Text>
                </View>
            </View>
        </View>)
    },[eligbleChallenge])

    const onPress = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.DAILY_CHALLENGE.CLICKED_ON_UNLOCK_DAILY_CHALLENGE, {
            [PAGE_NAME_KEY]: PAGE_NAMES.ARENA_PAGE,
        })
    }, []);

    if (!isGuest) {
        return null
    }

    return (
        <GoogleLoginButton renderButtonComponent={renderButtonComponent} onPress={onPress}/>
    )
}

export default LockedDCCard