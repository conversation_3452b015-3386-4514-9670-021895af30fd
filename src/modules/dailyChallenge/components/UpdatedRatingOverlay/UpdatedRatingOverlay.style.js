import { Dimensions, StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery"
import dark from "../../../../core/constants/themes/dark";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: "center",
        width: isCompactMode ? "100%" : 400,
        gap: 10
    },
    bottomSheetIndicator: {
        height: isCompactMode ? 4 : 0,
        width: isCompactMode ? 32 : 0,
        marginVertical: isCompactMode ? 10 : 0,
        backgroundColor: dark.colors.tertiary,
        borderRadius: 3
    },
    ratingIcon: {
        height: 95,
        width: 95
    },
    keepOldButton: {
        width: isCompactMode ? (Dimensions.get("window").width - 44) / 2 : 190,
        borderRadius: 20,
        borderWidth: 2,
        borderColor: dark.colors.textDark,
        justifyContent: "center",
        alignItems: "center",
        height: 40,
        backgroundColor: dark.colors.background
    },
    keepOldButtonLabel: {
        fontFamily: "Montserrat-600",
        fontSize: 14,
        lineHeight: 20,
        color: dark.colors.secondary,
    },
    acceptNewButton: {
        width: isCompactMode ? (Dimensions.get("window").width - 44) / 2 : 190,
        justifyContent: "center",
        alignItems: "center",
        height: 40
    },
    ratingText: {
        fontFamily: "Montserrat-700",
        fontSize: 40,
        lineHeight: 49,
        textAlign: "center",
        color: "white"
    },
    infoText: {
        fontFamily: "Montserrat-600",
        fontSize: 15,
        lineHeight: 19,
        textAlign: "center",
        color: "white"
    },
    infoSection: {
        paddingHorizontal: 60,
        justifyContent: "center",
        alignItems: "center",
        gap:15,
    },
    motiveText: {
        fontFamily: "Montserrat-600",
        fontSize: 11,
        lineHeight: 18,
        color: "white",
        textAlign: "center"
    },
    buttonsSection: {
        flexDirection: "row",
        gap: 12,
        marginVertical:12
    }
})

const useUpdatedRatingOverlayStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useUpdatedRatingOverlayStyles