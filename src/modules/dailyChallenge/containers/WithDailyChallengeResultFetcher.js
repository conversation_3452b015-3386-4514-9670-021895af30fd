import React from "react";
import useGetUserDailyChallengeResult from "core/graphql/queries/useGetUserDailyChallengeResult";
import _isEmpty from "lodash/isEmpty";
import Loading from "atoms/Loading";
import ErrorView from "atoms/ErrorView";

const WithDailyChallengeResultFetcher = (Component) => {
    const WrappedComponent = React.forwardRef((props, ref) => {
        const { dailyChallenge } = props;
        const { challengeNumber,division } = dailyChallenge;

        const { result, error, loading } = useGetUserDailyChallengeResult({
            challengeNumber,
            division
        })

        if(loading && _isEmpty(result)){
            return <Loading label={'Loading Daily Challenge Result...'} />
        }

        if(error){
            return <ErrorView errorMessage={"Something went wrong while fetching daily challenge result"}/>
        }

        return <Component ref={ref} {...props} dailyChallengeResult={result}/>
    });

    return WrappedComponent;
}

export default WithDailyChallengeResultFetcher;