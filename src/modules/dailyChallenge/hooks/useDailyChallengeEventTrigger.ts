import { useEffect, useRef } from "react";
import { TriggerPointType } from "@/src/store/useTriggerPointsStore/types";
import { TriggerPointEvent } from "@/src/store/useTriggerPointsStore/types";
import useTriggerPointsStore from "@/src/store/useTriggerPointsStore";
import _isEmpty from "lodash/isEmpty";
import _isDate from "lodash/isDate";

const useDailyChallengeEventTrigger = (completedAt: string) => {
    const pushTriggerPoint = useTriggerPointsStore((state) => state.pushTriggerPoint);
    const runOnce = useRef(false);
    useEffect(() => {
        if (_isEmpty(completedAt) || runOnce.current) return;
        const diffInSec = Math.abs(Math.floor((Date.now() - new Date(completedAt).getTime()) / 1000));
        if (diffInSec > 100) return;
        runOnce.current = true;
        pushTriggerPoint(TriggerPointType.DAILY_CHALLENGE_COMPLETE, [
            TriggerPointEvent.NOTIFICATION_REQUEST,
        ]);
    }, [pushTriggerPoint, completedAt]);
};

export default useDailyChallengeEventTrigger;
