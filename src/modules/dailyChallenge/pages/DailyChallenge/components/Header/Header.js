import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';

import {View} from 'react-native';
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {Text} from "@rneui/themed";
import _size from "lodash/size";

import styles from './Header.style';

const Header = props => {
    const { dailyChallenge, currentScore } = props;
    const { questions } = dailyChallenge;
    const [timeSpent, setTimeSpent] = useState(0);

    const getFormattedTime = useCallback(() => {
        const minutes = Math.floor(timeSpent / 60)
        const remainingSeconds = timeSpent % 60
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
    }, [timeSpent]);

    const renderScore = useCallback(() => {
        return <Text style={styles.playerScore}>{currentScore}/{_size(questions)}</Text>
    }, [currentScore, questions])

    useEffect(() => {
        const intervalId = setInterval(() => setTimeSpent(prevTime => prevTime + 1), 1000);
        return () => clearInterval(intervalId);
    }, [setTimeSpent]);

    return (
        <View style={styles.container}>
            <View style={styles.timerBox}>
                <MaterialIcons name={'timer'} color={'white'} size={20} />
                <Text style={styles.timerText}> {getFormattedTime()}</Text>
            </View>
            {renderScore()}
        </View>
    );
};

Header.propTypes = {

};

export default React.memo(Header);