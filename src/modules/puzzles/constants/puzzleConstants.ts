import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';

export const CROSS_MATH_PUZZLE_ACTIONS = {
  UNDO: 'UNDO',
  REDO: 'REDO',
  CLEAR: 'CLEAR',
  HINT: 'HINT',
  CHECK_PUZZLE: 'CHECK_PUZZLE',
  TAP_FOOTER_ITEM: 'TAP_FOOTER_ITEM',
  TAP_GRID_ITEM: 'TAP_GRID_ITEM',
};

export const PUZZLE_TYPES_VS_LABEL = {
  [PUZZLE_TYPES.CROSS_MATH_PUZZLE]: 'Cross Math',
  [PUZZLE_TYPES.KEN_KEN_PUZZLE]: '<PERSON>',
  [PUZZLE_TYPES.HECTOC_PUZZLE]: 'Hectoc',
  [PUZZLE_TYPES.MATH_MAZE]: 'Math Maze',
};

export const PUZZLE_DETAILS = {
  [PUZZLE_TYPES.CROSS_MATH_PUZZLE]: {
    title: 'Cross-Math Puzzle',
    captialisedTitle: 'CROSS-MATH',
    puzzleTypeText: 'Cross-Math',
    animationUrl: RIVE_ANIMATIONS.PUZZLE_ANIMATION,
  },
  [PUZZLE_TYPES.KEN_KEN_PUZZLE]: {
    title: 'Ken Ken Puzzle',
    captialisedTitle: 'KEN-KEN',
    puzzleTypeText: 'Ken Ken',
    animationUrl: RIVE_ANIMATIONS.KEN_KEN_ANIMATION,
  },
  [PUZZLE_TYPES.HECTOC_PUZZLE]: {
    title: 'Hectoc Puzzle',
    captialisedTitle: 'HECTOC',
    puzzleTypeText: 'Hectoc',
    animationUrl: RIVE_ANIMATIONS.HECTOC_ANIMATION,
  },
  [PUZZLE_TYPES.MATH_MAZE]: {
    title: 'Math Maze',
    captialisedTitle: 'MATH MAZE',
    puzzleTypeText: 'Math Maze',
    animationUrl: RIVE_ANIMATIONS.MATH_MAZE_ANIMATION,
  },
};
