import { User } from '@/src/core/types/userTypes';
import _get from 'lodash/get';

export enum CompletionStatus {
  NOT_PLAYED = 'NOT_PLAYED',
  COMPLETED = 'COMPLETED',
}

export interface Participant {
  timeSpent: number;
  rank: number;
  completionStatus: CompletionStatus;
  user: User;
}

export interface PuzzleLeaderboard {
  _id: string;
  totalParticipants: number;
  participants: Participant[];
}

const puzzleLeaderboardReader = {
  id: (puzzleLeaderboard: PuzzleLeaderboard): string =>
    _get(puzzleLeaderboard, '_id'),
  totalParticipants: (puzzleLeaderboard: PuzzleLeaderboard): number =>
    _get(puzzleLeaderboard, 'totalParticipants', 0),
  participants: (puzzleLeaderboard: PuzzleLeaderboard): Participant[] =>
    _get(puzzleLeaderboard, 'participants', []),
  user: (participant: Participant): User => _get(participant, 'user'),
  timeSpent: (participant: Participant): number =>
    _get(participant, 'timeSpent'),
  rank: (participant: Participant): number => _get(participant, 'rank'),
  completionStatus: (participant: Participant): CompletionStatus =>
    _get(participant, 'completionStatus', CompletionStatus.COMPLETED),
  name: (participant: Participant): string =>
    _get(participant, ['user', 'name']),
  userName: (participant: Participant): string =>
    _get(participant, ['user', 'username']),
  profileImageUrl: (participant: Participant): string =>
    _get(participant, ['user', 'profileImageUrl']),
};

export default puzzleLeaderboardReader;
