import _size from 'lodash/size';

const PUZZLE_RESULT_TEMPLATES = [
  '{puzzleType} in {timeSpent} secs. I do puzzles faster than your crush replies.',
  'Solved it before your attention span gave up, {timeSpent} secs btw. Brain rot who?',
  'Slayed Mat<PERSON>s {puzzleType} in {timeSpent} secs. Think you’ve got the guts to play? (<PERSON> better)',
  'DiD YOu EvEN BreAk a SweAT? Bro I think I just broke today’s {puzzleType} lmfao',
  'I have solved Matiks {humanReadableDate} {puzzleType} 🧩 in {timeSpent} 🥳',
];

export const getPuzzleRandomLabel = (
  puzzleType: string,
  timeSpent: string,
  humanReadableDate: string,
) => {
  const randomIndex = Math.floor(
    Math.random() * _size(PUZZLE_RESULT_TEMPLATES),
  );
  const selectedTemplate = PUZZLE_RESULT_TEMPLATES[randomIndex];

  return selectedTemplate
    .replace(/{puzzleType}/g, puzzleType)
    .replace(/{timeSpent}/g, timeSpent)
    .replace(/{humanReadableDate}/g, humanReadableDate)
};
