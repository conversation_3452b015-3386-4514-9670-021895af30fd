export interface HectocPuzzle {
  numbers: number[];
  target: number;
}

export function parseHectocPuzzleString(puzzleString: string): HectocPuzzle {
  const [numbersStr, targetStr] = puzzleString.split(':');
  const numbers = numbersStr.split(',').map((n) => parseInt(n, 10));
  const target = parseInt(targetStr, 10);
  return { numbers, target };
}

export function extractNumbersFromExpression(expression: string): number[] {
  const matches = expression.match(/\d+/g);
  if (!matches) {
    return [];
  }
  return matches.map((numStr) => parseInt(numStr, 10));
}

function tokenize(expression: string): string[] {
  const tokens: string[] = [];
  let currentNumber = '';
  const operators = ['+', '-', '*', '/', '%', '^', '(', ')'];

  for (let i = 0; i < expression.length; i++) {
    const char = expression[i];

    if (char >= '0' && char <= '9') {
      currentNumber += char;
    } else {
      if (currentNumber !== '') {
        tokens.push(currentNumber);
        currentNumber = '';
      }
      if (operators.includes(char)) {
        tokens.push(char);
      } else if (char !== ' ') {
      }
    }
  }

  if (currentNumber !== '') {
    tokens.push(currentNumber);
  }

  return tokens;
}

function parseExpression(tokens: string[]): number {
  let left = parseTerm(tokens);

  while (tokens.length > 0 && (tokens[0] === '+' || tokens[0] === '-')) {
    const operator = tokens.shift()!;
    const right = parseTerm(tokens);

    if (operator === '+') {
      left += right;
    } else {
      left -= right;
    }
  }
  return left;
}

function parseTerm(tokens: string[]): number {
  let left = parsePower(tokens);

  while (
    tokens.length > 0 &&
    (tokens[0] === '*' || tokens[0] === '/' || tokens[0] === '%')
  ) {
    const operator = tokens.shift()!;
    const right = parsePower(tokens);

    if (operator === '*') {
      left *= right;
    } else {
      if (right === 0) {
        throw new Error(
          `Division or Modulo by zero: ${left} ${operator} ${right}`,
        );
      }
      if (operator === '/') {
        left = Math.floor(left / right);
      } else {
        left %= right;
      }
    }
  }
  return left;
}

function parsePower(tokens: string[]): number {
  let left = parseFactor(tokens);

  while (tokens.length > 0 && tokens[0] === '^') {
    const operator = tokens.shift()!;
    const right = parseFactor(tokens);
    left **= right;
  }
  return left;
}

function parseFactor(tokens: string[]): number {
  if (tokens.length === 0) {
    throw new Error(
      'Unexpected end of expression, expected number or parenthesis',
    );
  }

  const token = tokens.shift()!;

  if (token === '(') {
    const value = parseExpression(tokens);
    if (tokens.length === 0 || tokens.shift() !== ')') {
      throw new Error('Missing closing parenthesis');
    }
    return value;
  }
  if (/^\d+$/.test(token)) {
    return parseInt(token, 10);
  }
  throw new Error(`Unexpected token: "${token}"`);
}

export function evaluateExpression(expression: string): number {
  const cleanedExpression = expression.replace(/\s+/g, '');
  if (!/^[0-9+\-*/%^()]+$/.test(cleanedExpression)) {
    throw new Error(
      'Invalid characters in expression. Only numbers, +, -, *, /, %, ^, (, ) are allowed.',
    );
  }
  if (cleanedExpression.length === 0) {
    throw new Error('Expression is empty');
  }

  const tokens = tokenize(cleanedExpression);
  if (tokens.length === 0) {
    throw new Error('Expression contains no evaluatable tokens');
  }

  const result = parseExpression([...tokens]);

  return result;
}

export function validateHectocSolution(
  puzzle: HectocPuzzle,
  userExpression: string,
): { isValid: boolean; message?: string } {
  try {
    const cleanedExpression = userExpression.replace(/\s+/g, '');
    if (!cleanedExpression) {
      return { isValid: false, message: 'Expression is empty.' };
    }

    if (!/^[0-9+\-*/%^()]+$/.test(cleanedExpression)) {
      return { isValid: false, message: 'Invalid characters used.' };
    }
    const usedNumbers = extractNumbersFromExpression(cleanedExpression);

    if (usedNumbers.length !== puzzle.numbers.length) {
      return {
        isValid: false,
        message: `Incorrect number of digits used. Expected ${puzzle.numbers.length}, got ${usedNumbers.length}.`,
      };
    }
    for (let i = 0; i < puzzle.numbers.length; i++) {
      if (usedNumbers[i] !== puzzle.numbers[i]) {
        return {
          isValid: false,
          message: `Numbers are not used in the correct sequence. Expected sequence: ${puzzle.numbers.join(', ')}.`,
        };
      }
    }

    const result = evaluateExpression(cleanedExpression);

    if (result !== puzzle.target) {
      return {
        isValid: false,
        message: `Result ${result} does not match target ${puzzle.target}.`,
      };
    }

    return { isValid: true };
  } catch (error: any) {
    console.error('Validation error:', error);
    return {
      isValid: false,
      message: error.message || 'Invalid expression format.',
    };
  }
}
