import { getDateStringFromTimestamp } from 'core/utils/general';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { format } from 'date-fns';

export function evaluateExpression(arr) {
  // First handle multiplication and division (BODMAS Rule)
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '×' || arr[i] === '÷') {
      const num1 = arr[i - 1];
      const operator = arr[i];
      const num2 = arr[i + 1];

      let result;
      if (operator === '×') {
        result = num1 * num2;
      } else if (operator === '÷') {
        result = num1 / num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // Now handle addition and subtraction
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === '+' || arr[i] === '-') {
      const num1 = arr[i - 1];
      const operator = arr[i];
      const num2 = arr[i + 1];

      let result;
      if (operator === '+') {
        result = num1 + num2;
      } else if (operator === '-') {
        result = num1 - num2;
      }

      // Replace the operator and its operands with the result
      arr.splice(i - 1, 3, result);

      // Adjust the loop index since we modified the array
      i -= 2;
    }
  }

  // The final result should be the only value left in the array
  return arr[0];
}

export const getFooterItemFromCell = (cell) => ({
  id: cell.id,
  value: cell.value,
  type: cell.type,
  isFilled: true,
});

export const checkIsValidDate = (date) => {
  const currentDate = getDateStringFromTimestamp(getCurrentTimeWithOffset());
  const FirststJan2000Date = '2000-01-01';

  const isFutureDate = date > currentDate;
  const isBefore1Jan2000Date = date < FirststJan2000Date;

  return !isFutureDate && !isBefore1Jan2000Date;
};

export const getDefaultPuzzleDate = () => {
  const today = new Date(getCurrentTimeWithOffset());
  return format(today, 'yyyy-MM-dd');
};
