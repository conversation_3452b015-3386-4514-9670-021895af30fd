import React, { useCallback, useEffect } from 'react';
import { Text, View, ActivityIndicator, Dimensions } from 'react-native';
import ScrollView from '@/src/components/atoms/Scrollview';
import userReader from 'core/readers/userReader';
import dark from 'core/constants/themes/dark';
import _size from 'lodash/size';
import LinearGradient from '@/src/components/atoms/LinearGradient';
import _isNil from 'lodash/isNil';
import _isEqual from 'lodash/isEqual';
import _map from 'lodash/map';
import styles from './PuzzleLeaderboard.style';
import LeaderboardRow from '@/src/components/shared/LeaderboardRow';
import {
  NavigationState,
  Route,
  SceneRendererProps,
  TabBar,
  TabBarProps,
  TabView,
} from 'react-native-tab-view';
import {
  sceneKey,
  scenes,
} from '../../components/PuzzleResult/PuzzleResultLeaderboard/PuzzleResultLeaderboard';
import Header from '@/src/components/shared/Header';
import useGetGlobalPuzzleLeaderboard from '../../hooks/queries/useGetGlobalPuzzleLeaderboard';
import { useLocalSearchParams } from 'expo-router';
import useGetFriendsPuzzleLeaderboard from '../../hooks/queries/useGetFriendsPuzzleLeaderboard';
import Loading from '@/src/components/atoms/Loading';
import useGetInstitutionLeaderbaordById from '../../hooks/queries/useGetInstitutionPuzzleLeaderboard';
import {
  CompletionStatus,
  Participant,
} from '../../readers/puzzleLeaderboardReader';
import _filter from 'lodash/filter';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import DemotionIcon from '@/src/components/svg/Icons/DemotionIcon';

const LeaderboardPage = () => {
  const initialLayout = { width: Dimensions.get('window').width };
  const [index, setIndex] = React.useState(0);
  const searchParams = useLocalSearchParams();
  const { id, puzzleType } = searchParams;
  const {
    fetchGlobalPuzzleLeaderboard,
    participants: globalLeaderboard,
    loading: globalLoading,
    loadMore: loadMoreGlobal,
    hasMore: hasMoreGlobal,
    isLeaderboardEmpty: isGlobalLeaderboardEmpty,
  } = useGetGlobalPuzzleLeaderboard({
    puzzleId: id as string,
  });
  const {
    fetchFriendsLeaderboardById,
    participants: friendsLeaderboard,
    loading: friendsLoading,
    loadMore: loadMoreFriends,
    hasMore: hasMoreFriends,
    isLeaderboardEmpty: isFriendsLeaderboardEmpty,
  } = useGetFriendsPuzzleLeaderboard({
    puzzleId: id as string,
  });
  const {
    fetchInstitutionLeaderboardById,
    participants: institutionLeaderboard,
    loading: institutionLoading,
    loadMore: loadMoreInstitution,
    hasMore: hasMoreInstitution,
    isLeaderboardEmpty: isInstitutionLeaderboardEmpty,
  } = useGetInstitutionLeaderbaordById({
    puzzleId: id as string,
  });
  const userRank = 50;

  const filterPlayedFriends: Participant[] = _filter(
    friendsLeaderboard,
    (participant) => {
      return participant.completionStatus === CompletionStatus.COMPLETED;
    },
  );

  const filterNotPlayedFriends: Participant[] = _filter(
    friendsLeaderboard,
    (participant) => {
      return participant.completionStatus === CompletionStatus.NOT_PLAYED;
    },
  );

  const renderParticipant = useCallback(
    ({ item, index }: { item: Participant; index: number }) => {
      return (
        <LeaderboardRow
          index={index}
          participant={item}
          puzzleType={puzzleType}
        />
      );
    },
    [puzzleType],
  );

  useEffect(() => {
    if (index === 0) {
      fetchFriendsLeaderboardById();
    } else if (index === 1) {
      fetchInstitutionLeaderboardById();
    } else if (index === 2) {
      fetchGlobalPuzzleLeaderboard();
    }
  }, [index]);

  const renderLoadingComponent = () => {
    return (
      <View style={{ flex: 1, width: '100%' }}>
        <ActivityIndicator size="large" color={dark.colors.streak} />
      </View>
    );
  };

  const renderCurrentRankContainer = useCallback(() => {
    return (
      <View style={styles.rankContainer}>
        <Text
          style={styles.rankStyle}
        >{`YOUR CURRENT RANK   ${userRank}`}</Text>
      </View>
    );
  }, []);

  const handleScroll = useCallback(
    (event: any, loadMore?: () => void, hasMore?: boolean) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const paddingToBottom = 20;
      const isCloseToBottom =
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom;

      if (isCloseToBottom && hasMore && loadMore) {
        loadMore();
      }
    },
    [],
  );

  const renderScrollViewList = useCallback(
    ({
      data,
      isLoading,
      isListEmpty,
      notPlayedData,
      loadMore,
      hasMore,
    }: {
      data: Participant[];
      isLoading: boolean;
      isListEmpty: boolean;
      notPlayedData?: Participant[];
      loadMore?: () => void;
      hasMore?: boolean;
    }) => {
      if (isLoading && isListEmpty) {
        return <Loading />;
      }
      if (isListEmpty && !isLoading) {
        return (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No Participants</Text>
          </View>
        );
      }
      return (
        <ScrollView
          style={{ flex: 1 }}
          onScroll={(event) => handleScroll(event, loadMore, hasMore)}
          scrollEventThrottle={20}
        >
          {_map(data, (item, index) => (
            <View key={`${userReader.id(item?.user)}-${index}`}>
              {renderParticipant({ item, index })}
            </View>
          ))}
          {hasMore && renderLoadingComponent()}
          {_size(notPlayedData) > 0 && (
            <View>
              <View style={styles.notPlayedContainer}>
                <DemotionIcon />
                <View>
                  <TextWithShadow
                    text={'NOT PLAYED YET'}
                    textStyle={styles.notPlayed}
                  />
                </View>
                <DemotionIcon />
              </View>
              {_map(notPlayedData, (item, index) => (
                <View key={`not-played-${userReader.id(item?.user)}-${index}`}>
                  {renderParticipant({ item, index })}
                </View>
              ))}
              <View style={{ height: 60 }} />
            </View>
          )}
        </ScrollView>
      );
    },
    [renderParticipant, handleScroll],
  );

  const renderScene = useCallback(
    ({ route }: SceneRendererProps & { route: Route }) => {
      switch (route.key) {
        case sceneKey.friends:
          return renderScrollViewList({
            data: filterPlayedFriends,
            isLoading: friendsLoading,
            isListEmpty: isFriendsLeaderboardEmpty,
            notPlayedData: filterNotPlayedFriends,
            loadMore: loadMoreFriends,
            hasMore: hasMoreFriends,
          });
        case sceneKey.college:
          return renderScrollViewList({
            data: institutionLeaderboard,
            isLoading: institutionLoading,
            isListEmpty: isInstitutionLeaderboardEmpty,
            loadMore: loadMoreInstitution,
            hasMore: hasMoreInstitution,
          });
        case sceneKey.global:
          return renderScrollViewList({
            data: globalLeaderboard,
            isLoading: globalLoading,
            isListEmpty: isGlobalLeaderboardEmpty,
            loadMore: loadMoreGlobal,
            hasMore: hasMoreGlobal,
          });
        default:
          return null;
      }
    },
    [
      renderScrollViewList,
      friendsLoading,
      institutionLoading,
      globalLoading,
      filterPlayedFriends,
      filterNotPlayedFriends,
      institutionLeaderboard,
      globalLeaderboard,
      isGlobalLeaderboardEmpty,
      isFriendsLeaderboardEmpty,
      isInstitutionLeaderboardEmpty,
      loadMoreFriends,
      loadMoreInstitution,
      loadMoreGlobal,
      hasMoreFriends,
      hasMoreInstitution,
      hasMoreGlobal,
    ],
  );

  const renderTabBar = (props: TabBarProps<Route>) => (
    <View style={{ width: '100%', paddingHorizontal: 20 }}>
      <TabBar
        {...(props as any)}
        indicatorStyle={styles.indicator}
        style={styles.tabBar}
        activeColor={dark.colors.text}
        inactiveColor={dark.colors.textDark}
      />
    </View>
  );

  const renderLazyPlaceholder = ({ route }: { route: Route }) => (
    <View key={route.key} />
  );

  const renderHeader = () => {
    return (
      <View>
        <Header title="Leaderboard" />
      </View>
    );
  };

  return (
    <View
      style={{ flex: 1, width: '100%', maxWidth: 500, alignSelf: 'center' }}
    >
      <View style={{ flex: 1 }}>
        {renderHeader()}
        <TabView
          navigationState={{ index, routes: scenes } as NavigationState<Route>}
          renderScene={renderScene}
          renderTabBar={renderTabBar}
          onIndexChange={setIndex}
          commonOptions={{
            labelStyle: { fontSize: 12, fontFamily: 'Montserrat-700' },
          }}
          initialLayout={initialLayout}
          lazy
          renderLazyPlaceholder={renderLazyPlaceholder}
        />
      </View>
      <LinearGradient
        colors={dark.colors.lightToDark}
        style={styles.bottomGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      ></LinearGradient>
      {/* {!_isNil(userRank) && renderCurrentRankContainer()} */}
    </View>
  );
};

export default React.memo(LeaderboardPage);
