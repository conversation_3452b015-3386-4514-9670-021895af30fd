import { useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import { checkIsValidDate } from 'modules/puzzles/utils/puzzleUtils';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import HectocPuzzleHeader from 'modules/puzzles/components/HectocPuzzleHeader';
import HectocPuzzle from './HectocPuzzle';

const TRACKED_ATTEMPT_FOR_DATE = {};

const HectocPuzzleContainer = () => {
  const { date } = useLocalSearchParams();

  const isValidDate = checkIsValidDate(date);

  useEffect(() => {
    if (TRACKED_ATTEMPT_FOR_DATE[date]) return;
    TRACKED_ATTEMPT_FOR_DATE[date] = true;
    Analytics.track(ANALYTICS_EVENTS.KEN_KEN_PUZZLE.ATTEMPTING_KEN_KEN_PUZZLE, {
      date,
      puzzleType: PUZZLE_TYPES.HECTOC_PUZZLE,
    });
  }, [date]);

  if (!isValidDate) {
    return (
      <View style={{ flex: 1 }}>
        <HectocPuzzleHeader />
        <ErrorView errorMessage="Sorry, Puzzle is not available for selected date" />
      </View>
    );
  }

  return <HectocPuzzle date={date} />;
};

export default React.memo(HectocPuzzleContainer);
