import React from 'react';
import { View } from 'react-native';
import { Image } from 'expo-image';
import crossMathPuzzleIcon from 'assets/images/puzzle/crossMathPuzzleIcon.png';
import { CrossMathInstructions } from '../../../../components/PuzzleInstruction';

const PuzzleHomePageHeader = () => (
  <View
    style={{
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 16,
      alignItems: 'center',
      padding: 16,
    }}
  >
    <Image source={crossMathPuzzleIcon} style={{ width: 32, height: 32 }} />
    <CrossMathInstructions />
  </View>
);

export default React.memo(PuzzleHomePageHeader);
