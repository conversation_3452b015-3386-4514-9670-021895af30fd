import React, { useCallback } from 'react';
import { ScrollView, View } from 'react-native';
import { PUZZLE_GAME_TYPES } from 'modules/home/<USER>/puzzleGameTypes';
import _map from 'lodash/map';
import GameType from 'modules/home/<USER>/GameConfigs/GameType';
import { router } from 'expo-router';

const PUZZLE_PRACTICE_TYPES = [
  PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_ENDLESS_PRACTICE,
];

const PracticeTabScene = () => {
  const handleGameTypeSelect = useCallback((gameType) => {
    if (gameType === PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_ENDLESS_PRACTICE) {
      router.push(`/puzzle/rush`);
    }
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <ScrollView>
        <View
          style={{
            paddingHorizontal: 16,
            gap: 15,
            height: 'auto',
            alignItems: 'center',
            paddingTop: 20,
          }}
        >
          {_map(PUZZLE_PRACTICE_TYPES, (gameType) => (
            <GameType
              key={gameType}
              gameType={gameType}
              isSelected={false}
              setSelectedGameType={() => handleGameTypeSelect(gameType)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(PracticeTabScene);
