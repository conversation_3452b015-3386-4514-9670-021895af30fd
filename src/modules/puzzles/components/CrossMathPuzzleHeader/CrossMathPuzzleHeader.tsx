import React, { useCallback } from 'react';
import Header from 'shared/Header';
import { View } from 'react-native';
import PuzzleHeader from 'modules/puzzles/components/PuzzleHeader';
import CrossMathPuzzleQuestion from 'shared/CrossMathPuzzleQuestion/CrossMathPuzzleQuestion';

const CrossMathPuzzleHeader = ({ showTimer = false }) => {
  const renderTrailingComponent = useCallback(
    () => (
      <View style={{ flexDirection: 'row', gap: 8 }}>
        {showTimer ? <CrossMathPuzzleQuestion.Timer /> : <View />}
      </View>
    ),
    [showTimer],
  );

  return (
    <View style={{ width: '100%' }}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      <PuzzleHeader />
    </View>
  );
};

export default React.memo(CrossMathPuzzleHeader);
