import React from 'react';
import { Image, Text, View } from 'react-native';
import { getFormattedDate } from 'modules/dailyChallenge/hooks/useLeaderboardSwitcher';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import dark from '@/src/core/constants/themes/dark';
import CrossWordsIcon from 'assets/images/puzzle/crosswordIcon.png';
import GradientCard from 'molecules/GradientCard';
import { router } from 'expo-router';
import useDailyPuzzleCardStyles from './DailyPuzzleCard.style';
import Pressable from '@/src/components/atoms/Pressable';

const DailyPuzzleCard = () => {
  const { isMobile: isCompactDevice } = useMediaQuery();
  const styles = useDailyPuzzleCardStyles();

  const onPressed = () => {
    router.push(`/puzzle-home?tab=DAILY_CHALLENGE`);
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE
        .CLICKED_ON_SOLVE_CROSS_MATH_PUZZLE_CARD_HOME_PAGE,
    );
    Analytics.track(
      ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_PUZZLE_DAILY_CHALLENGE,
    );
  };

  return (
    <Pressable onPress={onPressed}>
      <View>
        <GradientCard
          gradientColor={dark.colors.textDark}
          borderColor={dark.colors.primary}
        >
          <View
            style={[
              styles.container,
              !isCompactDevice && { backgroundColor: 'transparent' },
            ]}
          >
            <View style={{ position: 'relative' }}>
              <View style={styles.imageContainer}>
                <Image
                  source={CrossWordsIcon}
                  style={{ height: 24, width: 24, zIndex: 5, opacity: 1 }}
                />
              </View>
            </View>
            <View style={styles.contentContainer}>
              <Text style={styles.titleText}>{"DAILY PUZZLE"}</Text>
              <Text style={styles.subTitle}>
                {getFormattedDate({ isToday: true })}
              </Text>
            </View>
          </View>
        </GradientCard>
      </View>
    </Pressable>
  );
};

export default React.memo(DailyPuzzleCard);
