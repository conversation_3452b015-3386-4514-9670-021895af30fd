import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Dimensions, View } from 'react-native';
import {
  TabView,
  TabBar,
  SceneRendererProps,
  NavigationState,
  TabBarProps,
  Route,
} from 'react-native-tab-view';
import dark from '@/src/core/constants/themes/dark';
import styles from './PuzzleResultLeaderboard.style';
import MinifiedPuzzleLeaderboard from './components/MinifiedPuzzleLeaderboard';
import useGetMinifiedFriendsPuzzleLeaderboard from '../../../hooks/queries/useGetMinifiedFriendsPuzzleLeaderboard';
import useGetMinifiedGlobalPuzzleLeaderboard from '../../../hooks/queries/useGetMinifiedGlobalPuzzleLeaderboard';
import useGetMinifiedInstitutionPuzzleLeaderboard from '../../../hooks/queries/useGetMinifiedInstitutionPuzzleLeaderboard';
import { PuzzleTypes } from '../../../types/puzzleType';

const initialLayout = { width: Dimensions.get('window').width };

interface Props {
  puzzleId: string;
  puzzleType: PuzzleTypes;
}
export const sceneKey = {
  friends: 'FRIENDS',
  college: 'COLLEGE',
  global: 'GLOBAL',
};

export const scenes: Route[] = [
  { key: sceneKey.friends, title: 'FRIENDS' },
  { key: sceneKey.college, title: 'COLLEGE' },
  { key: sceneKey.global, title: 'GLOBAL' },
];

const ROW_HEIGHT = 80;
const HEADER_HEIGHT = 80;
const MIN_HEIGHT = 80;
const MAX_HEIGHT = 800;

const PuzzleResultLeaderboard: React.FC<Props> = (props) => {
  const { puzzleId, puzzleType } = props;
  const [index, setIndex] = useState(0);
  const { fetchMinifiedFriendsPuzzleLeaderboardQuery, loading, leaderboard } =
    useGetMinifiedFriendsPuzzleLeaderboard({ puzzleId });
  const {
    fetchMinifiedGlobalPuzzleLeaderboardQuery,
    loading: globalLoading,
    leaderboard: globalLeaderboard,
  } = useGetMinifiedGlobalPuzzleLeaderboard({ puzzleId });
  const {
    fetchMinifiedInstitutionPuzzleLeaderboardQuery,
    loading: institutionLoading,
    leaderboard: institutionLeaderboard,
  } = useGetMinifiedInstitutionPuzzleLeaderboard({ puzzleId });

  useEffect(() => {
    if (index === 0) {
      fetchMinifiedFriendsPuzzleLeaderboardQuery();
    } else if (index === 1) {
      fetchMinifiedInstitutionPuzzleLeaderboardQuery();
    } else if (index === 2) {
      fetchMinifiedGlobalPuzzleLeaderboardQuery();
    }
  }, [index]);

  type Route = { key: string; title: string };

  const renderScene = useCallback(
    ({ route }: SceneRendererProps & { route: Route }) => {
      switch (route.key) {
        case sceneKey.friends:
          return (
            <MinifiedPuzzleLeaderboard
              data={leaderboard?.participants}
              loading={loading}
              puzzleType={puzzleType}
            />
          );
        case sceneKey.college:
          return (
            <MinifiedPuzzleLeaderboard
              data={institutionLeaderboard?.participants}
              loading={institutionLoading}
              puzzleType={puzzleType}
            />
          );
        case sceneKey.global:
          return (
            <MinifiedPuzzleLeaderboard
              data={globalLeaderboard?.participants}
              loading={globalLoading}
              puzzleType={puzzleType}
            />
          );
        default:
          return null;
      }
    },
    [
      leaderboard,
      loading,
      institutionLeaderboard,
      institutionLoading,
      globalLeaderboard,
      globalLoading,
      puzzleType,
    ],
  );

  const renderLazyPlaceholder = ({ route }: { route: Route }) => (
    <View key={route.key} />
  );

  const renderTabBar = (props: TabBarProps<Route>) => (
    <View style={{ width: '100%', paddingHorizontal: 20 }}>
      <TabBar
        {...(props as any)}
        indicatorStyle={styles.indicator}
        style={styles.tabBar}
        activeColor={dark.colors.text}
        inactiveColor={dark.colors.textDark}
      />
    </View>
  );

  const participantCount = useMemo(() => {
    if (index === 0) return leaderboard?.participants?.length || 0;
    if (index === 1) return institutionLeaderboard?.participants?.length || 0;
    if (index === 2) return globalLeaderboard?.participants?.length || 0;
    return 0;
  }, [index, leaderboard, institutionLeaderboard, globalLeaderboard]);

  const dynamicHeight = useMemo(
    () =>
      Math.min(
        Math.max(participantCount * ROW_HEIGHT + HEADER_HEIGHT, MIN_HEIGHT),
        MAX_HEIGHT,
      ),
    [participantCount],
  );

  return (
    <View style={{ height: dynamicHeight, width: '100%' }}>
      <TabView
        navigationState={{ index, routes: scenes } as NavigationState<Route>}
        renderScene={renderScene}
        renderTabBar={renderTabBar}
        onIndexChange={setIndex}
        commonOptions={{
          labelStyle: { fontSize: 12, fontFamily: 'Montserrat-700' },
        }}
        initialLayout={initialLayout}
        lazy
        renderLazyPlaceholder={renderLazyPlaceholder}
      />
    </View>
  );
};

export default React.memo(PuzzleResultLeaderboard);
