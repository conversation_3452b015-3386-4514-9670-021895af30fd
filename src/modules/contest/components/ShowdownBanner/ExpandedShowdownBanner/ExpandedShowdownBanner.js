import React, { useCallback, useEffect, useMemo } from 'react';
import { useRouter } from 'expo-router';
import PropTypes from 'prop-types';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import useMediaQuery from 'core/hooks/useMediaQuery';
import ShowdownBannerMobile from 'assets/images/showdown_banner_mobile.png';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import getCurrentTimeWithOffset from '@/src/core/utils/getCurrentTimeWithOffset';
import { getShowdownPropertiesToTrack } from '@/src/modules/showdown/utils/showdownEvents';
import CarouselImageBanner from '@/src/components/shared/CarouselImageBanner';

let viewedFeaturedContestBanner = false;

const ExpandedShowdownBanner = ({ showdown, eventProperties = EMPTY_OBJECT, type }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { _id: showdownId, name, description, startTime, endTime } = showdown;

  const router = useRouter();

  const timeLeftToStart = useCountDownTimer({ targetTime: startTime });

  const isLive = useMemo(() => {
    const now = getCurrentTimeWithOffset();
    return (
      now >= new Date(startTime).getTime() && now <= new Date(endTime).getTime()
    );
  }, [startTime, endTime]);

  const hasEnded = useMemo(() => {
    const now = getCurrentTimeWithOffset();
    return now > new Date(endTime).getTime();
  }, [endTime]);

  const onKnowMorePressed = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.CLICKED_ON_SHOWDOWN_BANNER, {
      ...getShowdownPropertiesToTrack({ showdown }),
      ...eventProperties,
    });
    router.push(`/showdown/${showdownId}`);
  }, [router, showdown, showdownId]);

  useEffect(() => {
    if (!viewedFeaturedContestBanner) {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.VIEWED_FEATURED_CONTEST_BANNER, {
        ...getShowdownPropertiesToTrack({ showdown }),
        ...eventProperties,
      });
      viewedFeaturedContestBanner = true;
    }
  }, []);

  return (
    <CarouselImageBanner
      backgroundImage={
        isCompactMode ? ShowdownBannerMobile : '/images/showdownBanner.png'
      }
      onPress={onKnowMorePressed}
    />
  );
};

ExpandedShowdownBanner.propTypes = {
  showdown: PropTypes.object,
};

export default React.memo(ExpandedShowdownBanner);
