import React, { useCallback, useEffect, useMemo } from 'react';
import { useRouter } from 'expo-router';
import { Image, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import getCurrentTimeWithOffset from '@/src/core/utils/getCurrentTimeWithOffset';
import { getShowdownPropertiesToTrack } from '@/src/modules/showdown/utils/showdownEvents';
import { TouchableOpacity } from 'react-native-gesture-handler';
import ShowdownNameImage from '@/assets/images/banners/sumday_showdown.png';
import starIcon from '@/assets/images/icons/star_icon.png';
import useCompactShowdownBannerStyles from './CompactShowdownBanner.style';
import StartsInTimer from '../../StartsInTimer';

let viewedFeaturedContestBanner = false;

const CompactShowdownBanner = ({
  showdown,
  eventProperties = EMPTY_OBJECT,
  type,
}) => {
  const { _id: showdownId, name, description, startTime, endTime } = showdown;
  const styles = useCompactShowdownBannerStyles();
  const router = useRouter();

  const timeLeftToStart = useCountDownTimer({ targetTime: startTime });

  const isLive = useMemo(() => {
    const now = getCurrentTimeWithOffset();
    return (
      now >= new Date(startTime).getTime() && now <= new Date(endTime).getTime()
    );
  }, [startTime, endTime]);

  const hasEnded = useMemo(() => {
    const now = getCurrentTimeWithOffset();
    return now > new Date(endTime).getTime();
  }, [endTime]);

  const onKnowMorePressed = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.CLICKED_ON_SHOWDOWN_BANNER, {
      ...getShowdownPropertiesToTrack({ showdown }),
      ...eventProperties,
    });
    router.push(`/showdown/${showdownId}`);
  }, [router, showdown, showdownId]);

  useEffect(() => {
    if (!viewedFeaturedContestBanner) {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.VIEWED_FEATURED_CONTEST_BANNER, {
        ...getShowdownPropertiesToTrack({ showdown }),
        ...eventProperties,
      });
      viewedFeaturedContestBanner = true;
    }
  }, []);

  const renderBannerContent = () => {
    if (!isLive) {
      return <StartsInTimer contest={showdown} />;
    }
    return (
      <View style={[styles.contentContainer]}>
        <Text style={styles.isLiveText}>LIVE </Text>
        <Text style={styles.playNowText}>PLAY NOW </Text>
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.bannerContainer}
      onPress={onKnowMorePressed}
    >
      <Image source={ShowdownNameImage} style={styles.contestImage} />
      <Image source={starIcon} style={styles.starIcon1} />
      <Image source={starIcon} style={styles.startIcon2} />
      <Image source={starIcon} style={styles.starIcon3} />
      {renderBannerContent()}
    </TouchableOpacity>
  );
};

CompactShowdownBanner.propTypes = {
  showdown: PropTypes.object,
};

export default React.memo(CompactShowdownBanner);
