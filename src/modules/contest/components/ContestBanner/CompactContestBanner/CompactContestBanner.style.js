import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '@/src/core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    contentContainer: {
      flex: 1,
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
    isLiveText: {
      color: dark.colors.textLight,
      fontFamily: 'Montserrat-900',
      letterSpacing: 7,
      fontSize: 8,
      marginTop: 8,
    },
    playNowText: {
      color: dark.colors.secondary,
      fontFamily: 'Montserrat-800',
      letterSpacing: 2,
      fontSize: 10,
      marginTop: 10,
    },
    bannerContainer: {
      width: '100%',
      backgroundColor: 'black',
      height: 90,
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
    starIcon1: {
      position: 'absolute',
      left: 100,
      top: 14,
      width: 16,
      height: 16,
    },
    startIcon2: {
      position: 'absolute',
      right: 100,
      top: 14,
      width: 18,
      height: 18,
    },
    starIcon3: {
      position: 'absolute',
      left: 70,
      top: 40,
      width: 20,
      height: 20,
    },
    image: {
      width: 120,
      height: 18.5,
      marginTop: 12,
    },
  });

const useCompactContestBannerStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useCompactContestBannerStyles;
