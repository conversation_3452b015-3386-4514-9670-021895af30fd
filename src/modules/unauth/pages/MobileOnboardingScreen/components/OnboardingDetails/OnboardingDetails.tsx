import React, { useEffect, useRef, useState } from 'react';
import { Platform, View } from 'react-native';
import Animated, {
  configureReanimatedLogger,
  ReanimatedLogLevel,
  withTiming,
} from 'react-native-reanimated';

import LinearGradient from 'atoms/LinearGradient';
import { useRouter } from 'expo-router';
import { RiveRef } from 'rive-react-native';

import {
  getInitialAnimationState,
  getInitialStep,
  useOnboardingAnimation,
} from '../../hooks';
import ButtonSection from '../ButtonSection';
import ContentSection from '../ContentSection';
import RiveAnimationContainer from '../RiveAnimationContainer';
import styles from './OnboardingDetails.style';
import { ButtonType, LandingDetailsProps, ScreenData } from './types';

configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false });

const OnboardingDetails = (props: LandingDetailsProps) => {
  const AnimatedLinearGradient = Animated.createAnimatedComponent(
    LinearGradient,
  ) as any;
  const router = useRouter();
  const riveRef = useRef<RiveRef>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // State management
  const { screenData } = props;
  const [currentStep, setCurrentStep] = useState(() =>
    getInitialStep(screenData, props.alreadyLogin),
  );
  const [currentAnimation, setCurrentAnimation] = useState<ScreenData>(() =>
    getInitialAnimationState(screenData, props.alreadyLogin),
  );
  const [isTextExiting, setIsTextExiting] = useState(false);

  // Button disable state (1 second delay)
  const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(false);
  const nextButtonTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isPreviousButtonDisabled, setIsPreviousButtonDisabled] =
    useState(false);

  // Animation controller
  const {
    buttonsAnimatedStyle,
    textAnimatedStyle,
    gradientAnimatedStyle,
    textTranslateY,
    textTranslateX,
    textOpacity,
    textAnimationConfig,
  } = useOnboardingAnimation({
    currentAnimation,
    currentStep,
    buttonType: props.buttonType,
    isTextExiting,
  });

  useEffect(() => {
    setCurrentAnimation(screenData[currentStep - 1]);
  }, [currentStep]);

  useEffect(() => {
    if (props.alreadyLogin) {
      riveRef.current?.fireState('State Machine 1', 'trigger_n');
      setCurrentAnimation(screenData[screenData.length - 1]);
      setCurrentStep(screenData.length);

      setTimeout(() => {
        riveRef.current?.fireState('State Machine 1', 'trigger_n');
        setCurrentAnimation(screenData[screenData.length - 1]);
        setCurrentStep(screenData.length);
      }, 100);
    }
  }, []);

  useEffect(
    () => () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (nextButtonTimeoutRef.current) {
        clearTimeout(nextButtonTimeoutRef.current);
        nextButtonTimeoutRef.current = null;
      }
    },
    [],
  );

  const setRiveTriggerSafely = async (triggerName: string, path: string) => {
    try {
      if (!riveRef.current) return;

      if (Platform.OS === 'ios') {
        riveRef.current.fireState('State Machine 1', 'trigger_1');
        await new Promise((res) => setTimeout(res, 200));
        riveRef.current?.fireStateAtPath(triggerName, path);
      } else {
        riveRef.current?.fireStateAtPath(triggerName, path);
      }
    } catch (err) {
      console.warn('Failed to fire nested trigger:', err);
    }
  };

  const handleBackPress = async () => {
    if (isPreviousButtonDisabled) return;
    setIsPreviousButtonDisabled(true);
    setTimeout(() => {
      setIsPreviousButtonDisabled(false);
    }, 1500);

    if (props.alreadyLogin) {
      router.back();
      return;
    }

    if (!riveRef.current || currentStep <= 1) {
      router.back();
      return;
    }

    // Text exit animation
    setIsTextExiting(true);
    const exitPosition =
      textAnimationConfig.afterAnimationDirection === 'bottom-to-top'
        ? -textAnimationConfig.exitDistance
        : textAnimationConfig.exitDistance;

    textTranslateY.value = withTiming(exitPosition, {
      duration: textAnimationConfig.exitDuration,
    });
    textTranslateX.value = withTiming(
      textAnimationConfig.exitHorizontalOffset,
      {
        duration: textAnimationConfig.exitDuration,
      },
    );
    textOpacity.value = withTiming(0, {
      duration: textAnimationConfig.exitDuration,
    });

    await new Promise((resolve) =>
      setTimeout(resolve, textAnimationConfig.exitDuration),
    );

    const step = currentStep - 1;
    setCurrentStep(step);
    setCurrentAnimation(props.screenData[step]);

    setIsTextExiting(false);

    // Fire Rive animation
    if (step === 1) {
      riveRef.current?.fireState('State Machine 1', 'trigger_1');
      setTimeout(() => {
        riveRef.current?.fireState('State Machine 1', 'trigger_1');
      }, 100);
    } else if (step === lastScreen) {
      // await setRiveTriggerSafely('trigger_re_n', 'phone_n');
    } else if (Platform.OS === 'ios') {
      riveRef.current?.fireStateAtPath('trigger_1', 'trigger_re');
      setTimeout(() => {
        riveRef.current?.fireState('State Machine 1', 'trigger_re');
      }, 100);
    } else {
      riveRef.current?.fireState('State Machine 1', 'trigger_re');
    }
  };

  const toggleRiveInput = async () => {
    if (isNextButtonDisabled) return;

    setIsNextButtonDisabled(true);
    nextButtonTimeoutRef.current = setTimeout(
      () => {
        setIsNextButtonDisabled(false);
      },
      currentStep === 1 ? 2500 : 2000,
    );

    try {
      if (!riveRef.current) return;

      if (
        currentAnimation.resourceName === 'compi' ||
        currentAnimation.resourceName === 'duels'
      ) {
        setIsTextExiting(true);

        const exitPosition =
          textAnimationConfig.afterAnimationDirection === 'bottom-to-top'
            ? -textAnimationConfig.exitDistance // Move up (negative)
            : textAnimationConfig.exitDistance; // Move down (positive)

        textTranslateY.value = withTiming(exitPosition, {
          duration: textAnimationConfig.exitDuration,
        });
        textTranslateX.value = withTiming(
          textAnimationConfig.exitHorizontalOffset,
          {
            duration: textAnimationConfig.exitDuration,
          },
        );
        textOpacity.value = withTiming(0, {
          duration: textAnimationConfig.exitDuration,
        });

        await new Promise((resolve) =>
          setTimeout(resolve, textAnimationConfig.exitDuration),
        );
      }

      if (currentStep === 1) {
        await setRiveTriggerSafely('trigger_n', 'lines_n');
      } else if (lastScreen === currentStep) {
        await setRiveTriggerSafely('trigger_n', 'phone_n');
      } else {
        riveRef.current.fireState('State Machine 1', 'trigger_1');
      }

      if (lastScreen - 1 === currentStep) {
        setTimeout(async () => {
          await setRiveTriggerSafely('trigger_n', 'phone_n');
        }, 1000);
      }

      if (currentStep === 1) {
        riveRef.current.fireState('State Machine 1', 'trigger_1');
        await new Promise((res) => setTimeout(res, 700));
      }
      if (props.screenData?.[currentStep]) {
        setCurrentAnimation(props.screenData[currentStep]);
      }
      setCurrentStep((prev) => prev + 1);

      // Reset text exit state
      setIsTextExiting(false);
    } catch (error) {
      console.error('Error in toggleRiveInput:', error);
    }
  };

  const handleGetStartedPress = async () => {
    await setRiveTriggerSafely('trigger_n', 'cta_screen_n');
    setTimeout(async () => {
      props.getStartedOnPress && props.getStartedOnPress();
    }, 500);
  };

  const handleAlreadyLoginPress = async () => {
    await setRiveTriggerSafely('trigger_n', 'cta_screen_n');
    setTimeout(async () => {
      props.alreadyLoginOnPress && props.alreadyLoginOnPress();
    }, 500);
  };

  const lastScreen = props.screenData?.length - 1;

  return (
    <>
      <View style={styles.container}>
        <RiveAnimationContainer
          riveRef={riveRef}
          currentStep={currentStep}
          screenData={screenData}
          alreadyLogin={props.alreadyLogin}
          showBackButton={props.showBackButton}
          onBackPress={handleBackPress}
          isPreviousButtonDisabled={isPreviousButtonDisabled}
        />
      </View>
      <Animated.View
        style={[
          styles.bottomContainer,
          {
            paddingBottom:
              props.buttonType === ButtonType.GET_STARTED ? 26 : 51,
          },
          buttonsAnimatedStyle,
        ]}
      >
        <ContentSection
          currentAnimation={currentAnimation}
          shouldAnimateText
          textAnimatedStyle={textAnimatedStyle}
        />
        <ButtonSection
          buttonType={props.buttonType}
          currentStep={currentStep}
          lastScreen={lastScreen}
          currentAnimation={currentAnimation}
          onGetStartedPress={handleGetStartedPress}
          onNextPress={toggleRiveInput}
          onAlreadyLoginPress={handleAlreadyLoginPress}
          nextOnPress={props.nextOnPress}
          textAnimatedStyle={textAnimatedStyle}
        />
      </Animated.View>
      <AnimatedLinearGradient
        colors={['#00000000', '#000000']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={[styles.gradientStyle, gradientAnimatedStyle]}
      />
    </>
  );
};

export default React.memo(OnboardingDetails);

export { ButtonType };
