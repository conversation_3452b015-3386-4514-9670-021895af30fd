import { useEffect, useRef } from 'react';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { ScreenData, TextAnimationConfig } from '../components/OnboardingDetails/types';
import {
  ANIMATION_CONSTANTS,
  getButtonAnimationDelay,
  getTextAnimationConfig,
  shouldAnimateButtons,
  shouldAnimateText as shouldAnimateTextConfig,
  shouldAnimateLinearGradient as shouldAnimateLinearGradientConfig,
} from './animationConfig';

interface AnimationControllerProps {
  currentAnimation: ScreenData;
  currentStep: number;
  buttonType?: string;
  isTextExiting: boolean;
}

interface AnimationControllerReturn {
  buttonsAnimatedStyle: any;
  textAnimatedStyle: any;
  gradientAnimatedStyle: any;
  textTranslateY: any;
  textTranslateX: any;
  textOpacity: any;
  shouldAnimate: boolean;
  shouldAnimateText: boolean;
  shouldAnimateLinearGradient: boolean;
  textAnimationConfig: TextAnimationConfig;
}

export const useOnboardingAnimation = ({
  currentAnimation,
  currentStep,
  buttonType,
  isTextExiting,
}: AnimationControllerProps): AnimationControllerReturn => {
  const shouldAnimate = shouldAnimateButtons(buttonType, currentAnimation);
  const shouldAnimateText = shouldAnimateTextConfig();
  
  const lastScreenRef = useRef<string | null>(null);
  const isNewScreen = lastScreenRef.current !== currentAnimation.resourceName;
  
  if (isNewScreen) {
    lastScreenRef.current = currentAnimation.resourceName;
  }
  
  const shouldAnimateLinearGradient = shouldAnimateLinearGradientConfig(isNewScreen);
  
  const textAnimationConfig = getTextAnimationConfig(currentStep);
  const buttonAnimationDelay = getButtonAnimationDelay(currentAnimation);

  // Button animations
  const buttonsTranslateY = useSharedValue(
    shouldAnimate ? ANIMATION_CONSTANTS.bottomStartPosition : 0,
  );
  const buttonsOpacity = useSharedValue(shouldAnimate ? 0 : 1);

  // Text animations
  const textTranslateY = useSharedValue(
    shouldAnimateText
      ? textAnimationConfig.direction === 'bottom-to-top'
        ? textAnimationConfig.distance
        : -textAnimationConfig.distance
      : 0,
  );
  const textTranslateX = useSharedValue(0);
  const textOpacity = useSharedValue(shouldAnimateText ? 0 : 1);

  // Gradient animations
  const gradientTranslateY = useSharedValue(
    shouldAnimateLinearGradient ? ANIMATION_CONSTANTS.bottomStartLinearPosition : 0,
  );
  const gradientOpacity = useSharedValue(shouldAnimateLinearGradient ? 0 : 1);

  // Button animation effects
  useEffect(() => {
    if (shouldAnimate) {
      buttonsTranslateY.value = ANIMATION_CONSTANTS.bottomStartPosition;
      buttonsOpacity.value = 0;

      setTimeout(() => {
        buttonsTranslateY.value = withTiming(0, {
          duration: ANIMATION_CONSTANTS.animationDuration,
        });
        buttonsOpacity.value = withTiming(1, { 
          duration: ANIMATION_CONSTANTS.animationDuration 
        });
      }, buttonAnimationDelay);
    } else {
      buttonsTranslateY.value = 0;
      buttonsOpacity.value = 1;
    }
  }, [currentAnimation.resourceName, currentStep, shouldAnimate]);

  // Text animation effects
  useEffect(() => {
    if (shouldAnimateText && !isTextExiting) {
      const initialPosition =
        textAnimationConfig.direction === 'bottom-to-top'
          ? textAnimationConfig.distance
          : -textAnimationConfig.distance;

      textTranslateY.value = initialPosition;
      textTranslateX.value = 0;
      textOpacity.value = 0;

      setTimeout(() => {
        textTranslateY.value = withTiming(0, {
          duration: textAnimationConfig.duration,
        });
        textOpacity.value = withTiming(1, {
          duration: textAnimationConfig.duration,
        });
      }, textAnimationConfig.startDelay);
    } else if (!shouldAnimateText) {
      textTranslateY.value = 0;
      textTranslateX.value = 0;
      textOpacity.value = 1;
    }
  }, [
    currentAnimation.resourceName,
    currentStep,
    shouldAnimateText,
    isTextExiting,
  ]);

  // Gradient animation effects - animate when navigating to a new screen
  useEffect(() => {
    if (shouldAnimateLinearGradient) {
      gradientTranslateY.value = ANIMATION_CONSTANTS.bottomStartLinearPosition;
      gradientOpacity.value = 0;

      setTimeout(() => {
        gradientTranslateY.value = withTiming(0, {
          duration: ANIMATION_CONSTANTS.animationDuration,
        });
        gradientOpacity.value = withTiming(1, { 
          duration: ANIMATION_CONSTANTS.animationDuration 
        });
      }, buttonAnimationDelay);
    } else {
      gradientTranslateY.value = 0;
      gradientOpacity.value = 1;
    }
  }, [shouldAnimateLinearGradient]); // Only depend on shouldAnimateLinearGradient

  const buttonsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: buttonsTranslateY.value }],
    opacity: buttonsOpacity.value,
  }));

  const textAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: textTranslateY.value },
      { translateX: textTranslateX.value },
    ],
    opacity: textOpacity.value,
  }));

  const gradientAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: gradientTranslateY.value }],
    opacity: gradientOpacity.value,
  }));

  return {
    buttonsAnimatedStyle,
    textAnimatedStyle,
    gradientAnimatedStyle,
    textTranslateY,
    textTranslateX,
    textOpacity,
    shouldAnimate,
    shouldAnimateText,
    shouldAnimateLinearGradient,
    textAnimationConfig
  };
};
