import { gql, useQuery, WatchQueryFetchPolicy } from '@apollo/client';
import { USER_PUBLIC_DETAIL_FRAGMENT } from '@/src/core/graphql/fragments/userPublicDetail';
import _get from 'lodash/get';

const GET_FEED_LIKED_USERS = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetFeedLikedUsers($feedId: ID!) {
    getFeedLikedUsers(feedId: $feedId) {
      ...UserPublicDetailFields
    }
  }
`;

const useGetFeedLikedUsers = ({
  feedId,
  fetchPolicy = 'network-only',
}: {
  feedId: string;
  fetchPolicy?: WatchQueryFetchPolicy;
}) => {
  const { loading, error, data } = useQuery(GET_FEED_LIKED_USERS, {
    fetchPolicy: fetchPolicy,
    notifyOnNetworkStatusChange: true,
    variables: {
      feedId: feedId,
    },
  });

  return {
    data: _get(data, 'getFeedLikedUsers', []),  
    loading,
    error,
  };
};

export default useGetFeedLikedUsers;
