import { View, Text } from 'react-native';
import React, { useCallback, useMemo } from 'react';
import styles from './CardFooter.style';
import dark from '@/src/core/constants/themes/dark';
import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import useFeedStore from '@/src/store/useFeedStore';
import Switch from '@/src/components/atoms/helpers/switch';
import { NOTIFICATIONS_EVENT_TYPES } from '@/src/core/constants/notificationTypes';
import { withOpacity } from '@/src/core/utils/colorUtils';
import Button from './Button';
import _debounce from 'lodash/debounce';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Pressable from '@/src/components/atoms/Pressable';
import { useRouter } from 'expo-router';

const LikeButton = React.memo(
  ({ onPress, isLiked }: { onPress: () => void; isLiked: boolean }) => {
    return (
      <Button
        onPress={onPress}
        iconConfig={{
          type: ICON_TYPES.MATERIAL_ICONS,
          name: 'celebration',
          size: 18,
          color: dark.colors.secondary,
        }}
        borderColor={
          isLiked
            ? withOpacity(dark.colors.secondary, 0.4)
            : dark.colors.d3ButtonBorderDark
        }
        label={isLiked ? 'CELEBRATED' : 'CELEBRATE'}
        width={isLiked ? 118 : 108}
      />
    );
  },
);

const ShareButton = React.memo(({ onPress }: { onPress: () => void }) => {
  return (
    <Button
      onPress={onPress}
      iconConfig={{
        type: ICON_TYPES.FEATHER,
        name: 'share',
        size: 18,
        color: dark.colors.textLight,
      }}
      borderColor={dark.colors.d3ButtonBorderDark}
      label="SHARE"
      width={108}
    />
  );
});

const FriendCelebration = ({
  isLiked,
  feedId,
  likesCount = 0,
  feedType,
}: {
  isLiked: boolean;
  feedId: string;
  likesCount: number;
  feedType: string;
}) => {
  
  const router = useRouter();

  const onPressCelebratedIcon = useCallback(async () => {
    Analytics.track(ANALYTICS_EVENTS.FEEDS.CLICK_ON_CELEBRATED_ICON);
    router.push(`/feed/${feedId}`);
  }, [feedId, feedType, router]);
  const updateLikeStatus = useFeedStore((state) => state.updateFeedLikeStatus);

  const debouncedLike = useMemo(
    () =>
      _debounce(async (id: string) => {
        await updateLikeStatus(id);
      }, 500),
    [updateLikeStatus],
  );

  const onPressLike = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.FEEDS.CLICK_ON_CELEBRATE, {
      celebration: feedType,
    });
    debouncedLike(feedId);
  }, [feedId, debouncedLike]);

  return (
    <View style={styles.celebrationFooter}>
      <Switch
        value={NOTIFICATIONS_EVENT_TYPES.FRIEND_CELEBRATION}
        components={{
          [NOTIFICATIONS_EVENT_TYPES.FRIEND_CELEBRATION]: (
            <LikeButton isLiked={isLiked} onPress={onPressLike} />
          ),
          [NOTIFICATIONS_EVENT_TYPES.MY_CELEBRATION]: (
            <ShareButton onPress={NULL_FUN} />
          ),
        }}
        defaultComponent={null}
      />
      {likesCount > 0 && (
        <Pressable
          onPress={onPressCelebratedIcon}
          style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}
        >
          <View
            style={{
              padding: 6,
              borderWidth: 2,
              borderColor: dark.colors.d3ButtonBorderDark,
              borderRadius: 17,
            }}
          >
            <Icon
              type={ICON_TYPES.MATERIAL_ICONS}
              name="celebration"
              size={20}
              color={dark.colors.secondary}
            />
          </View>
          <Text style={{ color: dark.colors.textLight }}>{likesCount}</Text>
        </Pressable>
      )}
    </View>
  );
};

export default React.memo(FriendCelebration);
