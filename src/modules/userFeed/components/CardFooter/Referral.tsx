import React, { useCallback } from 'react';

import Button from './Button';
import dark from '@/src/core/constants/themes/dark';
import { useRouter } from 'expo-router';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useFeedStore from '@/src/store/useFeedStore';
import useSendFriendRequest from '@/src/modules/friendsAndFollowers/hooks/mutations/useSendFriendRequest';
import feedReader, { Feed } from '../../reader/feedReader';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

interface ViewReferralProps {
  feedId: string;
  feed: Feed;
}

const ViewReferral: React.FC<ViewReferralProps> = React.memo((props) => {
  const { feed, feedId } = props;
  const router = useRouter();
  const { user } = useSession();
  const sentBy = feedReader.connectionRequestSentBy(feed);
  const { removeFeed, deleteFeed } = useFeedStore((state) => ({
    removeFeed: state.removeFeed,
    deleteFeed: state.deleteFeed,
  }));
  const { sendFriendRequest } = useSendFriendRequest();

  const onPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.FEEDS.CLICK_ON_ADD_FRIEND_REFERRAL);
    sendFriendRequest({
      receiverId: sentBy,
    });
    removeFeed(feedId);
    deleteFeed(feedId);
  }, [router, user]);

  return (
    <Button
      onPress={onPress}
      borderColor={dark.colors.d3ButtonBorderDark}
      label={'Add Friend'}
      width={100}
    />
  );
});

const ViewReferralContainer: React.FC<ViewReferralProps> = (props) => {
  const { feed } = props;

  if (!feed || typeof feed !== 'object' || Object.keys(feed).length === 0) {
    console.error('ViewReferralContainer: Invalid feed prop.');
    return null;
  }

  return <ViewReferral {...props} />;
};

export default ViewReferralContainer;
