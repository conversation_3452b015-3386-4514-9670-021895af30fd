import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

export const LikeButtonStyles = StyleSheet.create({
  touchableContainer: {
    overflow: 'hidden',
    borderRadius: 6,
  },
  buttonContainerBase: {
    alignSelf: 'flex-start',
    paddingVertical:4,
    borderWidth: 1,
    
    // width: 120,
    // height: 44,
    borderRadius: 6,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    flexDirection: 'row',
    position: 'relative',
    overflow: 'hidden',
  },
  buttonContainerActive: {
    backgroundColor: 'transparent', // Transparent to show our gradient simulation
  },
  buttonContainerInactive: {
    backgroundColor: '#2b2b2b',
    // borderWidth: 1,
    borderColor: '#3A3A3A',
  },
  gradientSimulation: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
  },
  gradientLeft: {
    flex: 1,
    backgroundColor: '#A9F99E',
  },
  gradientRight: {
    flex: 1,
    backgroundColor: '#45AED5',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 4,
    width: '100%',
    zIndex: 2,
  },
  buttonText: {
    fontSize: 10,
    width:32,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonTextActive: {
    color: '#292929',
  },
  buttonTextInactive: {
    color: '#BABABA',
  },
  ripple: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#A9F99E',
    zIndex: 1,
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#A9F99E',
    left: 30,
    top: '50%',
    zIndex: 3,
  },
});

export const buttonStyles = StyleSheet.create({
  borderComponentStyle: {
    position: 'absolute',
    bottom: -3,
  },
  labelStyle: {
    fontSize: 10,
    fontFamily: 'Montserrat-700',
    lineHeight: 12,
    color: dark.colors.textLight,
  },
  buttonStyle: {
    height: 38,
  },
  buttonContainerStyle: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    height: 38,
  },
  buttonContentStyle: {
    gap: 4,
  },
});

const styles = StyleSheet.create({
  container: {
    paddingLeft: 40,
    paddingRight: 20,
  },
  challengeButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  challengeButtonText: {
    lineHeight: 20,
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  likedButton: {
    borderColor: dark.colors.secondary,
  },
  likedButtonText: {
    color: dark.colors.secondary,
  },
  LikeButtonWrapper: {
    alignSelf: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  LikeButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  LikeButtonText: {
    lineHeight: 20,
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  LikeButtonIconsContainer: {
    width: 14,
    height: 14,
  },
  LikeButtonIcon: {
    width: 16,
    height: 16,
  },
  celebrationFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    // gap: 12,
    justifyContent: 'space-between',
    width: '100%',
  },
});

export default styles;
