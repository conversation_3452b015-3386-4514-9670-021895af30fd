/* eslint-disable react/require-default-props */
import { Image, StyleSheet } from 'react-native';
import React from 'react';
import { FEED_TYPES } from '@/src/core/constants/notificationTypes';
import CelebrationImage from 'assets/images/feed/fire.png';
import ConnectionImage from 'assets/images/feed/connect.webp';
import BadgeImage from 'assets/images/feed/badge.webp';
import puzzle from 'assets/images/feed/puzzle.webp';
import DailyChallengeImage from 'assets/images/feed/daily_challenge.webp';
import WelcomeImage from 'assets/images/feed/welcome.webp';
import { getLeagueImage, LeagueType } from '../../utils';

const getImage = (type: FEED_TYPES, leagueType?: LeagueType) => {
  switch (type) {
    case FEED_TYPES.CELEBRATION:
      return CelebrationImage;
    case FEED_TYPES.CONNECTION:
      return ConnectionImage;
    case FEED_TYPES.DAILY_CHALLENGE:
      return DailyChallengeImage;
    case FEED_TYPES.REFERRAL:
      return CelebrationImage;
    case FEED_TYPES.LEAGUE:
      return getLeagueImage(leagueType ?? 'RUBY');
    case FEED_TYPES.DAILY_PUZZLE:
      return puzzle;
    case FEED_TYPES.BADGE:
      return BadgeImage;
    case FEED_TYPES.WELCOME:
      return WelcomeImage;
    default:
      return CelebrationImage;
  }
};

const styles = StyleSheet.create({
  icon: {
    width: 60,
    height: 60,
  },
});

const SideImage = ({
  type,
  leagueType,
}: {
  type: FEED_TYPES;
  leagueType?: LeagueType;
}) => {
  const image = getImage(type, leagueType);
  return image ? <Image source={image} style={styles.icon} /> : null;
};

export default React.memo(SideImage);
