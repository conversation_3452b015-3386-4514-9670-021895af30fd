import PropTypes from 'prop-types';
import React, { useEffect, useRef } from 'react';
import { Animated, View } from 'react-native';
import { Button, Text } from '@rneui/themed';
import DotAnimation from 'shared/DotAnimation';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import RefreshGameButton from 'modules/game/components/RefreshGameButton';
import GameLobbyPlayerCards from '../../../../components/GameLobbyPlayerCards';
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame';
import styles from './WaitingForUserToJoin.style';

const TRACKED_EVENT_FOR_GAMES = {};

const WaitingForUserToJoin = ({ game }) => {
  const { _id: gameId } = game;
  const rotateValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        delay: 0,
        duration: 2000,
        useNativeDriver: true,
        isInteraction: true,
      }),
    );
    rotateAnimation.start();

    return () => rotateAnimation.stop();
  }, [rotateValue]);

  useEffect(() => {
    if (gameId && !TRACKED_EVENT_FOR_GAMES[gameId]) {
      Analytics.track(ANALYTICS_EVENTS.PLAY_WITH_FRIEND?.VIEW_PWF_WAITING_PAGE);
      TRACKED_EVENT_FOR_GAMES[gameId] = true;
    }
  }, [gameId]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId });

  return (
    <View style={[styles.container]}>
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          gap: 5,
        }}
      >
        <GameLobbyPlayerCards game={game} />
        <View style={{ paddingTop: 32 }}>
          <Animated.Text style={[styles.timer, { transform: [{ rotate }] }]}>
            ⏳
          </Animated.Text>
        </View>
        <Text style={styles.waitingText}>
          Waiting for other user to join the game
          <DotAnimation />
        </Text>
      </View>
      <RefreshGameButton />
      <View
        style={{
          marginBottom: 16,
          position: 'absolute',
          width: '100%',
          bottom: 0,
        }}
      >
        <Button type="clear" onPress={onPressLeaveGame}>
          <Text style={styles.cancel}>Cancel</Text>
        </Button>
      </View>
    </View>
  );
};

WaitingForUserToJoin.propTypes = {
  game: PropTypes.object.isRequired,
};

export default React.memo(WaitingForUserToJoin);
