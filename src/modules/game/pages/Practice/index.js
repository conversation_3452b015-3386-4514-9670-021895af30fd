import React from 'react'
import {Text} from '@rneui/themed'
import Practice from './Practice'

import _isEmpty from 'lodash/isEmpty'
import _isNil from 'lodash/isNil'
import Loading from 'atoms/Loading'
import usePracticeGameContext, {WithPracticeGameContext} from "modules/game/hooks/usePracticeGameContext";

const LoadPracticeGame = () => {
  const {
    game,
    gameMeta: {loading, error},
  } = usePracticeGameContext()

  if (loading || _isEmpty(game)) {
    return <Loading/>
  }

  if (error) {
    return <Text>Something went wrong</Text>
  }
  if (_isNil(game)) {
    return <Text>Something went wrong! Game not found</Text>
  }

  return <Practice game={game}/>
}

export default React.memo(WithPracticeGameContext(LoadPracticeGame))
