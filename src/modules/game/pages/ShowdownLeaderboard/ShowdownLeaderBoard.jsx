import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _size from 'lodash/size';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Platform, TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';
import { useRouter } from 'expo-router';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

import useMediaQuery from 'core/hooks/useMediaQuery';
import LinearGradientText from 'atoms/LinearGradientText';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import AntDesign from '@expo/vector-icons/AntDesign';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import _isArray from 'lodash/isArray';
import _isNil from 'lodash/isNil';
import _reduce from 'lodash/reduce';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import dark from '../../../../core/constants/themes/dark';
import PlayerResultCard from '../../components/PlayerResultCard';
import { useSession } from '../../../auth/containers/AuthProvider';
import Loading from '../../../../components/atoms/Loading';
import styles from './ShowdownLeaderboard.style';
import useGameContext from '../../hooks/useGameContext';

const RESULT_STATUS = {
  WIN: 'WIN',
  LOSS: 'LOSS',
  TIE: 'TIE',
};

const LABELS = {
  [RESULT_STATUS.WIN]: 'YOU WON !',
  [RESULT_STATUS.LOSS]: 'YOU LOST !',
  [RESULT_STATUS.TIE]: 'TIE !',
};

const Results = () => {
  const { game, players } = useGameContext();
  const { user, userId } = useSession();
  const { isMobile } = useMediaQuery();
  const router = useRouter();
  const gameRef = useRef(game);
  const nextGameIntervalRef = useRef(null);
  const { config: gameConfig, leaderBoard, showdownGameConfig } = game;
  const timeLeftToStart = useCountDownTimer({
    targetTime: showdownGameConfig?.nextGameStartsAt ?? new Date(),
  });

  const isNativeDevice = Platform.OS === 'android' || Platform.OS === 'ios';

  const scores = useMemo(() => {
    if (
      !_isNil(showdownGameConfig?.showdownGamePlayer) &&
      _isArray(showdownGameConfig?.showdownGamePlayer)
    ) {
      const score = _reduce(
        showdownGameConfig?.showdownGamePlayer,
        (result, player) => {
          if (player?.userId === userId) {
            result = { ...result, user: player };
          } else {
            result = { ...result, opponent: player };
          }
          return result;
        },
        {},
      );
      return score;
    }
    return {
      user: { wins: 0, isWinner: false, isTie: false },
      opponent: { wins: 0, isWinner: false, isTie: false },
    };
  }, [showdownGameConfig?.showdownGamePlayer, userId]);

  const { timeLimit } = gameConfig;

  const adaptedPlayers = useMemo(
    () =>
      _map(players, (player) => {
        const { _id: playerId } = player;
        const leaderBoardEntry = _find(
          leaderBoard,
          (entry) => entry?.userId === playerId,
        );
        const { ratingChange, rank, correct, statikCoinsEarned } =
          leaderBoardEntry ?? {};

        return {
          ...player,
          ratingChange,
          score: correct,
          isWinner: rank === 1,
          statikCoinsEarned,
        };
      }),
    [players, leaderBoard],
  );

  const currentPlayer = useMemo(
    () => _find(adaptedPlayers, (player) => player?._id === user?._id),
    [adaptedPlayers, user],
  );

  const isCurrPlayerWinner = useMemo(
    () => currentPlayer?.isWinner,
    [currentPlayer],
  );

  const currentPlayerStatus = useMemo(() => {
    if (_get(scores, ['user', 'isTie'], false)) {
      return RESULT_STATUS.TIE;
    }
    if (_get(scores, ['user', 'isWinner'], false)) {
      return RESULT_STATUS.WIN;
    }
    return RESULT_STATUS.LOSS;
  }, [scores]);

  const navigateToNewGame = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_NEW_GAME_ON_RESULT_MODAL,
    );
    if (showdownGameConfig?.isRoundEnded) {
      return router.replace(`/showdown/${game?.showdownId}`);
    }
    router.replace(`/showdown/play/${showdownGameConfig?.nextGameId}`);
  }, [router, timeLimit]);

  const renderPlayer = ({ player }) => (
    <View style={styles.playerContainer}>
      <PlayerResultCard user={player} />
    </View>
  );

  useEffect(() => {
    const { gameType, config } = gameRef.current ?? {};
    Analytics.track(ANALYTICS_EVENTS.RESULT_PAGE_SHOWN, {
      gameType,
      ...config,
    });
  }, []);

  useEffect(() => {
    if (showdownGameConfig?.nextGameStartsAt) {
      if (nextGameIntervalRef.current) {
        clearInterval(nextGameIntervalRef.current);
      }
      nextGameIntervalRef.current = setInterval(() => {
        if (
          new Date(showdownGameConfig?.nextGameStartsAt).getTime() -
            getCurrentTimeWithOffset() <
          5
        ) {
          router.replace(`/showdown/play/${showdownGameConfig?.nextGameId}`);
        }
      }, 1000);
    }
    return () => {
      clearInterval(nextGameIntervalRef.current);
    };
  }, [showdownGameConfig]);

  if (_size(players) !== 2) {
    return <Loading />;
  }

  return (
    <View
      style={[
        styles.container,
        { alignItems: 'center', justifyContent: 'center' },
      ]}
    >
      <View style={styles.cardContainer}>
        <View style={styles.headerContainer}>
          {!isMobile && (
            <TouchableOpacity
              onPress={navigateToNewGame}
              style={{ paddingHorizontal: 8 }}
            >
              <View>
                <AntDesign
                  name="close"
                  size={24}
                  color="white"
                  // onPress={goBack}
                />
              </View>
            </TouchableOpacity>
          )}
          <View
            style={{
              flex: 1,
              textAlign: 'center',
              flexDirection: 'row',
              justifyContent: 'center',
            }}
          >
            {currentPlayer?.isWinner ? (
              <LinearGradientText
                textStyle={{
                  fontSize: 19,
                  fontFamily: 'Montserrat-700',
                  textAlign: 'center',
                }}
                colors={dark.colors.orangeGradientTextColors}
              >
                {isNativeDevice ? (
                  <Text
                    style={{
                      fontSize: 19,
                      fontFamily: 'Montserrat-700',
                      textAlign: 'center',
                    }}
                  >
                    {LABELS[currentPlayerStatus]}
                  </Text>
                ) : (
                  LABELS[currentPlayerStatus]
                )}
              </LinearGradientText>
            ) : (
              <Text style={[styles.looseText]}>
                {LABELS[currentPlayerStatus]}
              </Text>
            )}
          </View>
        </View>
        <View
          style={{
            alignItems: 'center',
            position: 'relative',
            top: -26,
          }}
        >
          <Text style={styles.subTitle}> Keep Up the Speed !</Text>
        </View>

        <View style={styles.resultsContainer}>
          {renderPlayer({ player: adaptedPlayers[0] })}
          <View style={styles.scoresContainer}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 14,
              }}
            >
              <MaterialCommunityIcons
                name="timer-outline"
                size={20}
                color="white"
              />
              <Text style={styles.gameTime}>
                {` ${_toNumber(timeLimit) / 60}:00`}
              </Text>
            </View>
            <Text style={styles.scoreText}>
              {scores?.user?.wins ?? 0} - {scores?.opponent?.wins ?? 0}
            </Text>
          </View>
          {renderPlayer({ player: adaptedPlayers[1] })}
        </View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
            marginBottom: 8,
          }}
        >
          <View style={styles.currentUserRatingUpdateContainer}>
            <Text style={styles.yourRatingLabel}>YOUR RATING</Text>
            <View style={styles.ratingUpdate}>
              <Text style={styles.currentUserRating}>
                {_get(currentPlayer, 'rating', 0) +
                  _toNumber(currentPlayer.ratingChange)}
              </Text>
              <Text
                style={
                  isCurrPlayerWinner ? styles.ratingGain : styles.ratingLoss
                }
              >
                {isCurrPlayerWinner ? '+' : '-'}
                {Math.abs(currentPlayer.ratingChange)}
              </Text>
            </View>
          </View>

          <View style={styles.currentUserRatingUpdateContainer}>
            <Text style={styles.yourRatingLabel}>COINS</Text>
            <View style={styles.ratingUpdate}>
              <Text style={styles.currentUserRating}>
                {_toNumber(_get(currentPlayer, 'statikCoins', 0)) +
                  _toNumber(currentPlayer?.statikCoinsEarned)}
              </Text>
              <Text style={styles.ratingGain}>
                +{Math.abs(_toNumber(currentPlayer?.statikCoinsEarned))}
              </Text>
            </View>
          </View>
        </View>
        <View
          style={[
            styles.actionsContainer,
            !isMobile && { paddingHorizontal: 16 },
          ]}
        >
          <TouchableOpacity
            style={styles.actionButtonContainer}
            onPress={navigateToNewGame}
          >
            <View>
              <Text style={styles.playAgainLabel}>
                {showdownGameConfig?.isRoundEnded
                  ? 'Jump to Dashboard'
                  : `Join Game ${showdownGameConfig?.totalGamesPlayed + 1} (${timeLeftToStart && timeLeftToStart?.slice(3)})`}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
      {/* <JoinDiscordButton /> */}
    </View>
  );
};

export default React.memo(Results);
