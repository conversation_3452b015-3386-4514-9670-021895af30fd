import React, { useMemo } from 'react';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import { getComponentFactoryForGame } from 'modules/game/utils/getComponentFactoryForGame';
import PlayGameMainLayoutWrapper from 'modules/game/components/PlayGameMainLayoutWrapper';
import InActiveGame from '../../components/InActiveGame/InActiveGame';
import useGameContext from '../../hooks/useGameContext';
import { isInactiveGame } from '../../utils/gameUtils';

const GamePlayPageContainer = () => {
  const { game, gameMeta } = useGameContext();

  const { loading } = gameMeta ?? EMPTY_OBJECT;

  const COMPONENT_FACTORY = useMemo(
    () => getComponentFactoryForGame(game),
    [game],
  );

  if (_isNil(game)) {
    if (loading) {
      return <Loading label="Loading Game" />;
    }
    return <ErrorView errorMessage="Something went wrong!" />;
  }

  if (isInactiveGame({ game })) {
    return <InActiveGame game={game} />;
  }

  return <PlayGameMainLayoutWrapper COMPONENT_FACTORY={COMPONENT_FACTORY} />;
};

export default React.memo(GamePlayPageContainer);
