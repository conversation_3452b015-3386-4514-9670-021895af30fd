import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import _find from 'lodash/find';
import _toString from 'lodash/toString';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _values from 'lodash/values';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import gameReader from 'core/readers/gameReader';
import _head from 'lodash/head';
import _keys from 'lodash/keys';
import _get from 'lodash/get';
import useGameContext from './useGameContext';

const useFastestFingerGameQuestionState = () => {
  const { game, submitAnswer } = useGameContext();

  const { user, userId } = useSession();
  const {
    questions: allQuestions = EMPTY_ARRAY,
    _id: gameId,
    leaderBoard = EMPTY_ARRAY,
  } = game;

  const initialValue = _reduce(
    allQuestions,
    (acc, questionObject) => {
      const { submissions, question } = questionObject;
      const submissionByCurrentUser = _filter(
        submissions,
        (submission) => submission?.userId === user?._id,
      );

      const { id } = question;
      acc[id] = {
        question,
        hasSolved: false,
        incorrectAttempts: 0,
      };

      if (!_isEmpty(submissionByCurrentUser)) {
        const { id } = question;
        acc[id].hasSolved = true;
      }
      return acc;
    },
    {},
  );

  const [questions, setQuestions] = useState(initialValue);

  const currentQuestionId =
    gameReader.currentQuestionId(game) ?? _head(_keys(questions));

  const questionsRef = useRef(questions);
  questionsRef.current = questions;

  const updateQuestion = useCallback(
    ({ qid, key, value }) => {
      const prevQuestionState = questions[qid];
      if (_isEmpty(prevQuestionState)) return;

      setQuestions((prevState) => ({
        ...prevState,
        [qid]: {
          ...prevState[qid],
          [key]: value,
        },
      }));
    },
    [setQuestions, questions],
  );

  const [solvedAllQuestions, setSolvedAllQuestions] = useState(false);

  const handleCorrectAnswerSubmitted = useCallback(
    ({ question, value }) => {
      const { id: questionId } = question;
      updateQuestion({ qid: questionId, key: 'hasSolved', value: true });

      const incorrectAttempts = questions[questionId]?.incorrectAttempts ?? 0;

      submitAnswer({
        gameId,
        questionId,
        submittedValue: value,
        timeOfSubmission: getCurrentTimeWithOffset(),
        isCorrect: true,
        incorrectAttempts,
        userId,
      });
    },
    [updateQuestion, questions, submitAnswer, gameId, userId],
  );

  const handleIncorrectAnswerSubmitted = useCallback(
    ({ question }) => {
      const { id: questionId } = question;
      const currentAttempts = questions[questionId]?.incorrectAttempts || 0;
      const updatedIncorrectAttempts = currentAttempts + 1;
      updateQuestion({
        qid: questionId,
        key: 'incorrectAttempts',
        value: updatedIncorrectAttempts,
      });
    },
    [questions, updateQuestion],
  );

  const handleSubmitAnswer = useCallback(
    ({ questionId, value }) => {
      const questionObj = _find(
        _values(questions),
        (questionObj) => questionObj?.question?.id === questionId,
      );
      const { question } = questionObj;
      if (_isEmpty(question)) {
        return false;
      }
      const { answers } = question;
      if (answers?.[0] === _toString(value)) {
        handleCorrectAnswerSubmitted({
          question,
          value,
        });
        return true;
      }
      handleIncorrectAnswerSubmitted({
        question,
        value,
      });

      return false;
    },
    [questions, handleCorrectAnswerSubmitted, handleIncorrectAnswerSubmitted],
  );

  const playersScores = useMemo(() => {
    const scores = {};
    for (let i = 0; i < _size(leaderBoard); i++) {
      scores[leaderBoard?.[i]?.userId] = leaderBoard?.[i]?.correct;
    }
    return scores;
  }, [leaderBoard]);

  useEffect(() => {
    if (_isEmpty(questionsRef.current) && !_isEmpty(initialValue)) {
      setQuestions(initialValue);
    }
  }, [initialValue]);

  return {
    currentQuestion: _get(questions, [currentQuestionId, 'question']),
    submitAnswer: handleSubmitAnswer,
    playersScores,
    incorrectAttempts: _get(questions, [
      currentQuestionId,
      'incorrectAttempts',
    ]),
    solvedAllQuestions,
  };
};

export default useFastestFingerGameQuestionState;
