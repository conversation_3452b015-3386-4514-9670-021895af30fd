import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark'; // Assuming dark theme is used

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 24,
    marginHorizontal: 16,
  },
  scrollContentContainer: {
    paddingBottom: 20,
  },
  actionButtonsContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    gap: 20,
  },

  actionButtonLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
    textAlign: 'left',
  },
  buttonContentStyles: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: dark.colors.background,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  buttonContainerStyle:{
    justifyContent: 'flex-start',
    height: 50
  },
  sectionContainer: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-500',
    color: dark.colors.text,
    textTransform: 'uppercase',
  },
  viewAllText: {
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    color: dark.colors.secondary,
  },
  emptyStateText: {
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    textAlign: 'center',
    marginVertical: 20,
  },
});
