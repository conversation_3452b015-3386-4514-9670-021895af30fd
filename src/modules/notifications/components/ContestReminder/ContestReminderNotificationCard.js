import React, { useCallback } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import useContestReminderNotificationCardStyles from './ContestReminderNotificationCard.style'
import UserImage from 'atoms/UserImage'

const ContestReminderNotificationCard = () => {
    const styles = useContestReminderNotificationCardStyles()

    const { isViewed } = props
    const { showCTAs, handlePress } = EMPTY_OBJECT
    // const { infoText }
    const handleOnContestReminderPress = useCallback(() => {
        console.info('TYTY')
    }, [])

    return (
        <View
            style={[
                styles.container,
                isViewed && { backgroundColor: 'transparent' },
            ]}
        >
            <UserImage style={styles.container} rounded={false} />
            <View style={styles.infoColumn}>
                <Text style={styles.infoText}>{infoText}</Text>
                {showCTAs && (
                    <View>
                        <TouchableOpacity
                            onPress={handleOnContestReminderPress}
                        >
                            <Text style={styles.buttonText}>Join Now</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
            <View>
                <Text style={styles.durationText}>5m</Text>
            </View>
        </View>
    )
}

export default React.memo(ContestReminderNotificationCard)
