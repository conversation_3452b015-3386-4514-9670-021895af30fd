import React, { useCallback } from 'react'
import { Text, TouchableOpacity, View } from 'react-native'
import UserImage from 'atoms/UserImage'
import useFriendRequestNotificationCardStyles from './FriendRequestNotificationCard.style'
import dark from '../../../../core/constants/themes/dark'

const FriendRequestNotificationCard = () => {
    const styles = useFriendRequestNotificationCardStyles()

    const { isViewed } = props
    const { showCTAs, handlePress } = EMPTY_OBJECT
    // const { infoText }
    const handleOnAcceptRequestPress = useCallback(() => {
        console.info('TYTY')
    }, [])

    const handleOnRejectRequestPress = useCallback(() => {
        console.info('TYTY')
    }, [])

    return (
        <View
            style={[
                styles.container,
                isViewed && { backgroundColor: 'transparent' },
            ]}
        >
            <UserImage style={styles.container} rounded={false} />
            <View style={styles.infoColumn}>
                <Text style={styles.infoText}>{infoText}</Text>
                {showCTAs && (
                    <View>
                        <TouchableOpacity onPress={handleOnRejectRequestPress}>
                            <Text
                                style={[
                                    styles.buttonText,
                                    { color: dark.colors.textDark },
                                ]}
                            >
                                Reject
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={handleOnAcceptRequestPress}>
                            <Text style={[styles.buttonText]}>Accept</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
            <View>
                <Text style={styles.durationText}>5m</Text>
                {/* Icon will come here */}
            </View>
        </View>
    )
}

export default React.memo(FriendRequestNotificationCard)
