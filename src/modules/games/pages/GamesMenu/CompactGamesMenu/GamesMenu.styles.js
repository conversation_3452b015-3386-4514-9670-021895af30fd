import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  gameModesContainer: {
    height: 80,
    flexDirection: 'row',
    gap: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    marginHorizontal: 'auto',
    width: '100%',
    marginBottom: 20,
  },
  howtoplaylabel: {
    color: dark.colors.textLight,
    fontSize: 12,
  },
  howtoplaybutton: {
    borderRadius: 20,
    width: 100,
    height: 40,
  },
});

export default styles;
