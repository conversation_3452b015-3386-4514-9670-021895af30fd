import Header from '@/src/components/shared/Header';
import React, { useCallback, useState } from 'react';
import { View } from 'react-native';
import _map from 'lodash/map';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import { useRouter } from 'expo-router';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import {
  BLITZ_GAME_TYPES,
  CLASSICAL_GAME_TYPES,
  GAME_MODE,
  gameModes,
  MEMORY_GAME_TYPES,
} from '../../../constants/gameModes';
import styles from './GamesMenu.styles';
import GamesModeSelectionCard from '../../../components/GamesModeSelectionCard';
import GameModeSubSection from '../../../components/GameModeSubSection';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import useGoBack from '@/src/navigator/hooks/useGoBack';

const GamesMenu = ({ gameMode }) => {
  const [selectedMode, setSelectedMode] = useState(gameMode);
  const router = useRouter();
  const { goBack } = useGoBack();

  const handleSelectMode = useCallback((mode) => {
    router.setParams({
      gameMode: mode,
    });
    setSelectedMode(mode);
  }, []);

  let gameTypes = BLITZ_GAME_TYPES;
  switch (selectedMode) {
    case GAME_MODE.BLITZ:
      Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_BLITZ_TAB);
      gameTypes = BLITZ_GAME_TYPES;
      break;
    case GAME_MODE.CLASSICAL:
      Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_CLASSIC_TAB);
      gameTypes = CLASSICAL_GAME_TYPES;
      break;
    case GAME_MODE.MEMORY:
      Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_MEMORY_TAB);
      gameTypes = MEMORY_GAME_TYPES;
      break;
    default:
      Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_VS_FRIENDS_TAB);
  }

  const renderGameModesOption = useCallback(
    (mode) => {
      const { key } = mode;
      const isSelected = selectedMode === key;
      return (
        <GamesModeSelectionCard
          key={key}
          mode={mode}
          isSelected={isSelected}
          onPress={handleSelectMode}
        />
      );
    },
    [handleSelectMode, selectedMode],
  );

  const onPressGoBack = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_BACK);
    goBack();
  }, [goBack]);

  const navigateToRatingInformation = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PLAYNOW.CLICKED_ON_RATING_BUTTON);
    router.push('/games/information/');
  }, [router]);

  const renderRatingInformationButton = () => (
    <InteractiveSecondaryButton
      iconConfig={{
        name: 'infocirlceo',
        type: ICON_TYPES.ANT_DESIGN,
        size: 12,
      }}
      label="Ratings"
      labelStyle={styles.howtoplaylabel}
      buttonContainerStyle={[styles.howtoplaybutton]}
      onPress={() => navigateToRatingInformation()}
    />
  );

  return (
    <View style={{ flex: 1 }}>
      <Header
        style
        renderTrailingComponent={renderRatingInformationButton}
        goBack={onPressGoBack}
      />
      <View style={styles.gameModesContainer}>
        {_map(gameModes, (mode) => renderGameModesOption(mode))}
      </View>
      <View style={{ flex: 1 }}>
        <GameModeSubSection selectedMode={selectedMode} gameTypes={gameTypes} />
      </View>
    </View>
  );
};

export default React.memo(GamesMenu);
