import { Dimensions, StyleSheet } from "react-native";
import dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        maxHeight: 450,
        flex: 1,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        // gap: 44,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 16,
        // overflow:"hidden",
        width:"100%",
        paddingBottom: 16,
    },
    image: {
        height: 200,
        width: 300,
    },
    infoIconBox: {
        height: 36,
        width: 36,
        borderRadius: 18,
        backgroundColor: dark.colors.cardBackground,
        justifyContent: "center",
        alignItems: 'center'
    },
    infoText: {
        fontFamily: "Montserrat-400",
        lineHeight: 16,
        fontSize: 14,
        color: 'white',
        textAlign:"center"
    },
    leadingText: {
        fontFamily: "Montserrat-700",
        lineHeight: 16,
        fontSize: 15,
        color: dark.colors.secondary,
        textAlign: "center"
    },
    infoTextBold: {
        fontFamily: "Montserrat-700",
    },
    infoDetailRow: {
        flexDirection: 'row',
        gap: 12,
        alignItems: "center"
    },
    button: {
        width: Math.min(320, Dimensions.get("window").width - 32)
    }
})

export default styles