import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useEffect, useState } from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { Redirect } from 'expo-router';
import Loading from 'atoms/Loading';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import CompactResolutionPledge from './Compact';
import ExpandedResolutionPledge from './Expanded';
import useHandleCommitGoal from '../../hooks/useHandleCommitGoal';
import useCheckIfPledgeTaken from '../../hooks/queries/checkIfPledgeTaken';
import { useSession } from '../../../auth/containers/AuthProvider';

const ResolutionPledgePage = () => {
  const [selectedDuration, setSelectedDuration] = useState(10);

  const handleOnPledgeDurationChange = useCallback(
    ({ duration }) => {
      setSelectedDuration(duration);
    },
    [setSelectedDuration],
  );

  const { handleOnCommitGoalPressed, isCommitingGoalFuncInQueue } =
    useHandleCommitGoal();

  const onSubmitPressed = useCallback(() => {
    handleOnCommitGoalPressed({ duration: selectedDuration });
  }, [selectedDuration]);

  const { isMobile: isCompactMode } = useMediaQuery();

  const ComponentToBeRendered = isCompactMode
    ? CompactResolutionPledge
    : ExpandedResolutionPledge;

  return (
    <ComponentToBeRendered
      animationUrl={RIVE_ANIMATIONS.BRAIN_RESOLUTION}
      selectedDuration={selectedDuration}
      onDurationChange={handleOnPledgeDurationChange}
      isSubmiting={isCommitingGoalFuncInQueue}
      onSubmitPress={onSubmitPressed}
    />
  );
};

const ResolutionPledgeContainer = () => {
  const { session, isLoading, user } = useSession();

  const { checkIfPledgeTaken, loading } = useCheckIfPledgeTaken();

  const [isPledgeTaken, setIsPledgeTaken] = useState(false);

  useEffect(() => {
    if (session && !_isEmpty(user)) {
      checkIfPledgeTaken().then((data) => {
        setIsPledgeTaken(!_isEmpty(data?.getUserResolution));
      });
    }
  }, [session, user]);

  if (loading || isLoading) {
    return <Loading label="" />;
  }

  if (session && !_isEmpty(user) && isPledgeTaken) {
    return <Redirect href="/home" />;
  }

  return <ResolutionPledgePage />;
};

export default React.memo(ResolutionPledgeContainer);
