import { gql, useLazyQuery } from '@apollo/client';
import { useCallback, useState } from 'react';
import _trim from 'lodash/trim';
import _size from 'lodash/size';

const GET_USER_BY_REFERRAL_CODE = gql`
  query GetUserByReferralCode($referralCode: String!) {
    getUserByReferralCode(referralCode: $referralCode) {
      _id
      name
      username
      profileImageUrl
      badge
    }
  }
`;

const useGetUserByReferralCode = () => {
  const [getUserByReferralCodeQuery, { loading, error }] = useLazyQuery(
    GET_USER_BY_REFERRAL_CODE,
    {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first',
    },
  );

  const [user, setUser] = useState(null);

  const getUserByReferralCode = useCallback(
    async (referralCode) => {
      const trimmedReferralCode = _trim(referralCode);
      if (!trimmedReferralCode || _size(trimmedReferralCode) < 3) {
        setUser(null);
        return;
      }

      try {
        const result = await getUserByReferralCodeQuery({
          variables: { referralCode: trimmedReferralCode },
        });

        if (result.error) {
          setUser(null);
          return;
        }

        const userData = result.data?.getUserByReferralCode;
        if (userData) {
          setUser(userData);
        } else {
          setUser(null);
        }
      } catch (err) {
        setUser(null);
      }
    },
    [getUserByReferralCodeQuery],
  );

  const clearUser = useCallback(() => {
    setUser(null);
  }, []);

  return {
    getUserByReferralCode,
    clearUser,
    user,
    loading,
    error,
  };
};

export default useGetUserByReferralCode;
