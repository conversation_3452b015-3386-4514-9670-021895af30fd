import React from 'react';
import { View } from 'react-native';
import useKeyboardHandler from '@/src/core/hooks/useKeyboardHandler';
import { useUsernameState } from '../hooks/useUsernameState';
import { useUsernameActions } from '../hooks/useUsernameActions';
import { UsernameHeader } from './UsernameHeader';
import { UsernameContent } from './UsernameContent';
import { UsernameFooter } from './UsernameFooter';
import { useUsernameStyles } from '../Username.style';

interface UsernameContainerProps {
  onContinue?: (username: string) => void;
  currentStep?: number;
  totalSteps?: number;
  loggedInUser?: any;
}

const getTitleText = (isAvailable: boolean, hasChecked: boolean): string => {
  if (hasChecked && !isAvailable) {
    return 'oops, cant call you that'.toUpperCase();
  }
  return 'WHAT SHOULD WE CALL YOU?';
};

export const UsernameContainer: React.FC<UsernameContainerProps> = ({
  onContinue,
  currentStep = 1,
  totalSteps = 3,
  loggedInUser,
}) => {
  const styles = useUsernameStyles();
  const { keyboardHeight } = useKeyboardHandler();

  const initialUsername = loggedInUser?.username || '';
  const usernameState = useUsernameState({ initialUsername });
  const usernameActions = useUsernameActions({
    username: usernameState.username,
    canContinue: usernameState.canContinue,
    onContinue,
  });

  const title = getTitleText(
    usernameState.validation.isAvailable,
    usernameState.validation.hasChecked,
  );
  const buttonLabel = usernameActions.getButtonLabel(
    usernameState.cantProceed,
    usernameState.canContinue,
  );

  return (
    <View style={styles.container}>
      <UsernameHeader currentStep={currentStep} totalSteps={totalSteps} />

      <UsernameContent
        title={title}
        username={usernameState.username}
        onChangeText={usernameState.setUsername}
        hasError={!!usernameState.validation.error}
        statusMessage={usernameState.status.message}
        statusColor={usernameState.status.color}
      />

      <UsernameFooter
        buttonLabel={buttonLabel}
        canContinue={usernameState.canContinue}
        isAvailable={usernameState.validation.isAvailable}
        keyboardHeight={keyboardHeight}
        onPress={usernameActions.handleContinue}
      />
    </View>
  );
};
