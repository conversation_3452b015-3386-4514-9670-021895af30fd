import React from 'react';
import { View, Platform } from 'react-native';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import dark from '@/src/core/constants/themes/dark';
import { useUsernameStyles } from '../Username.style';

interface UsernameFooterProps {
  buttonLabel: string;
  canContinue: boolean;
  isAvailable: boolean;
  keyboardHeight: number;
  onPress: () => void;
}

export const UsernameFooter: React.FC<UsernameFooterProps> = ({
  buttonLabel,
  canContinue,
  isAvailable,
  keyboardHeight,
  onPress,
}) => {
  const styles = useUsernameStyles();

  const footerStyle = [
    styles.footer,
    {
      position: 'absolute' as const,
      bottom: Platform.OS === 'ios'
        ? keyboardHeight > 0
          ? keyboardHeight - 40
          : keyboardHeight
        : 0,
      width: '100%',
      marginBottom: 16,
    },
  ];

  const buttonContainerStyle = [
    styles.newUserGetStartedButton,
    { opacity: canContinue ? 1 : 0.5 },
  ];

  const buttonStyle = [
    styles.newUserGetStartedButtonStyle,
    {
      borderColor: isAvailable
        ? dark.colors.secondary
        : dark.colors.errorRed,
    },
  ];

  const buttonBorderBackgroundStyle = {
    backgroundColor: isAvailable
      ? dark.colors.victoryColor
      : dark.colors.errorRed,
  };

  return (
    <View style={footerStyle}>
      <InteractivePrimaryButton
        label={buttonLabel}
        buttonContainerStyle={buttonContainerStyle}
        buttonStyle={buttonStyle}
        labelStyle={styles.newUserGetStartedLabel}
        onPress={canContinue ? onPress : () => {}}
        buttonBorderBackgroundStyle={buttonBorderBackgroundStyle}
      />
    </View>
  );
};
