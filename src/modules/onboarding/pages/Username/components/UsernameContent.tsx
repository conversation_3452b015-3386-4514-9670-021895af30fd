import React from 'react';
import { ImageBackground, Text, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import BackgroundImage from '@/assets/images/backgrounds/background_blocks.png';
import { UsernameInput } from './UsernameInput';
import { UsernameStatus } from './UsernameStatus';
import { useUsernameStyles } from '../Username.style';

interface UsernameContentProps {
  title: string;
  username: string;
  onChangeText: (text: string) => void;
  hasError: boolean;
  statusMessage: string;
  statusColor: string;
}

export const UsernameContent: React.FC<UsernameContentProps> = ({
  title,
  username,
  onChangeText,
  hasError,
  statusMessage,
  statusColor,
}) => {
  const styles = useUsernameStyles();

  return (
    <KeyboardAwareScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      enableOnAndroid
      keyboardOpeningTime={250}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <ImageBackground
        style={{ flex: 1 }}
        imageStyle={[
          { opacity: 0.5 },
          { transform: [{ rotateX: '-900deg' }, { rotateY: '0deg' }] },
        ]}
        source={BackgroundImage}
        resizeMode="stretch"
      >
        <View style={styles.content}>
          <Text style={styles.title}>{title}</Text>

          <UsernameInput
            username={username}
            onChangeText={onChangeText}
            hasError={hasError}
          />

          <UsernameStatus message={statusMessage} color={statusColor} />
        </View>
      </ImageBackground>
    </KeyboardAwareScrollView>
  );
};
