import React, { useEffect } from 'react';
import { Keyboard, Text, TextInput, View } from 'react-native';

import LinearGradient from 'atoms/LinearGradient';
import { ReferralInputViewProps } from '../types';

const ReferralInputView: React.FC<ReferralInputViewProps> = ({
  styles,
  referralCode,
  isValidReferralCode,
  hasError,
  onReferralCodeChange,
}) => {
  useEffect(() => {
    if (isValidReferralCode) {
      Keyboard.dismiss();
    }
  }, [isValidReferralCode]);
  return (
    <>
      <View style={[styles.referralInputContainer]}>
        <TextInput
          style={styles.textInputStyle}
          value={referralCode || ''}
          onChangeText={onReferralCodeChange}
          textAlign="center"
          autoFocus
        />
        <LinearGradient
          style={styles.textInputBottomBorder}
          colors={hasError ? ['#D24A4A', '#D24A4A'] : ['#A9F99E', '#00D9FF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        />
      </View>
      {hasError && (
        <Text style={styles.error}>
          {"this doesn't seem right\ncheck again".toUpperCase()}
        </Text>
      )}
    </>
  );
};

export default ReferralInputView;
