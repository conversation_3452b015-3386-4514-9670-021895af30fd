import React from 'react';
import { Platform, Pressable, Text, View } from 'react-native';

import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import { ReferralActionsViewProps } from '../types';

const ReferralActionsView: React.FC<ReferralActionsViewProps> = ({
  styles,
  showReferralInput,
  isKeyboardVisible,
  keyboardHeight,
  sendFriendRequestSuccess,
  isValidReferralCode,
  onSkip,
  onSubmit,
}) => {
  const renderMainActions = () => {
    if (!showReferralInput || isKeyboardVisible) return null;

    return (
      <View style={{ paddingHorizontal: 16 }}>
        <View style={styles.footer}>
          <View style={styles.buttonContainer}>
            {isValidReferralCode && (
              <InteractivePrimaryButton
                onPress={onSubmit}
                label="YES, PROCEED"
                buttonContainerStyle={styles.buttonContainerStyle}
                buttonStyle={styles.continueButton}
                labelStyle={styles.continueButtonText}
                buttonBorderBackgroundStyle={styles.continueBackground}
              />
            )}
            {!sendFriendRequestSuccess && (
              <Pressable style={styles.skipButton} onPress={onSkip}>
                <Text style={styles.noReferralCode}>SKIP FOR NOW</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderKeyboardActions = () => {
    if (!sendFriendRequestSuccess && isKeyboardVisible) {
      return (
        <Pressable
          style={[
            styles.skipButton,
            {
              position: 'absolute',
              bottom:
                Platform.OS === 'ios'
                  ? keyboardHeight > 0
                    ? keyboardHeight - 30
                    : keyboardHeight
                  : 10,
              marginBottom: 16,
            },
          ]}
          onPress={onSkip}
        >
          <Text style={styles.noReferralCode}>SKIP FOR NOW</Text>
        </Pressable>
      );
    }
    return null;
  };

  return (
    <>
      {renderMainActions()}
      {renderKeyboardActions()}
    </>
  );
};

export default ReferralActionsView;
