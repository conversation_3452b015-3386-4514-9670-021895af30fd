import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'
import { withOpacity } from '../../../core/utils/colorUtils'

const styles = StyleSheet.create({
  optionRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    width: '100%',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  hoveredOptionRowContainer: {
    backgroundColor: withOpacity(dark.colors.secondary, 0.05),
    borderRadius: 4,
  },
  optionRowContent: {
    flexDirection: 'row',
    gap: 12,
  },
  badgeContainer: {
    position: 'absolute',
    top: 0,
    left: 10,
    zIndex: 1,
    borderWidth: 0,
    borderRadius: 8,
    width: 4,
    height: 4,
  },
  optionLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-500',
  },
  modalContent: {
    borderRadius: 10,
    minWidth: 200,
    // alignItems: 'flex-start',
    backgroundColor: dark.colors.tertiary,
    flex: 1,
    width: '100%',
  },
})

export default styles
