const { withTamagui } = require('@tamagui/metro-plugin');
const path = require('path');
const { getSentryExpoConfig } = require('@sentry/react-native/metro');

// Determine if we're in production mode
const isProduction = process.env.NODE_ENV === 'production';

const config = getSentryExpoConfig(__dirname, {
  isCSSEnabled: true,
});

// Add .riv to asset extensions
config.resolver.assetExts.push('riv');

config.resolver.resolveRequest = (context, moduleName, platform) => {
  if (moduleName === 'axios') {
    return {
      filePath: path.resolve(__dirname, 'node_modules/axios/index.js'),
      type: 'sourceFile',
    };
  }
  return context.resolveRequest(context, moduleName, platform);
};

// Configure minification for production
if (isProduction) {
  // Use Terser for minification in production
  config.transformer.minifierPath = 'metro-minify-terser';
  config.transformer.minifierConfig = {
    // Terser options
    compress: {
      // Remove console.log in production
      drop_console: true,
      // More aggressive optimizations
      passes: 2,
    },
    mangle: {
      // Mangle variable names
      toplevel: true,
    },
    output: {
      // Remove comments and whitespace
      comments: false,
      // Beautify output (false = uglify)
      beautify: false,
    },
  };
}

module.exports = withTamagui(config, {
  components: ['tamagui'],
  config: './tamagui.config.ts',
  outputCSS: './tamagui-web.css',
});
