{"name": "matiks", "version": "1.0.0", "main": "expo-router/entry", "packageManager": "yarn@1.22.19", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "expo start", "start-local:android": "npx expo start --localhost --android", "start-local:ios": "npx expo start --localhost --ios", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:web": "NODE_ENV=production expo export -p web", "build-android:local": "eas build --profile development --platform android --local", "build-ios:local": "eas build --profile development --platform ios --local", "build-android:preview": "eas build --profile preview --platform android", "build-ios:preview": "eas build --profile preview --platform ios", "build-android:prod": "eas build --profile production --platform android", "build-ios:prod": "eas build --profile production --platform ios", "update:prod": "eas update --channel production --clear-cache --message \"Adding Practice modules\" --platform android", "submit:ios": "eas submit -p ios", "submit:android": "eas submit -p android", "lint-all": "eslint src/core/hooks/useColorScheme --ext .js,.jsx,.ts,.tsx", "lint": "npm run lint-all -- --fix", "validate": "lint-staged --config .lintstagedrc.json", "postinstall": "patch-package && node ./__quickfix/quickfix.js", "eas-build-on-success": "npx bugsnag-eas-build-on-success"}, "dependencies": {"@apollo/client": "^3.13.8", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@bugsnag/expo": "^53.0.0", "@charmy.tech/react-native-stroke-text": "^1.2.3", "@expo/match-media": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.3", "@growthbook/growthbook-react": "^1.5.1", "@lottiefiles/dotlottie-react": "^0.14.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/analytics": "^22.2.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/crashlytics": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.11.0", "@react-oauth/google": "^0.12.1", "@rive-app/react-canvas": "^4.21.3", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@sentry/react-native": "^6.15.1", "@shopify/flash-list": "1.7.6", "@tamagui/babel-plugin": "^1.126.13", "@tamagui/config": "^1.126.13", "@tamagui/metro-plugin": "^1.126.13", "apollo-link": "^1.2.14", "apollo-upload-client": "17.0.0", "bson": "^4.7.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "^53.0.9", "expo-apple-authentication": "~7.2.4", "expo-application": "~6.1.4", "expo-asset": "~11.1.5", "expo-audio": "~0.4.6", "expo-blur": "~14.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-media-library": "~17.1.6", "expo-modules-core": "~2.4.0", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-updates": "~0.28.13", "expo-video": "~2.2.0", "graphql": "^16.8.1", "graphql-ws": "^6.0.5", "hoist-non-react-statics": "^3.3.2", "html2canvas": "^1.4.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "katex": "^0.16.19", "lodash": "^4.17.21", "lottie-react-native": "^7.1.0", "mime": "^4.0.4", "mixpanel-react-native": "^3.0.5", "modal-enhanced-react-native-web": "^0.2.0", "numeral": "^2.0.6", "opentype.js": "^1.3.4", "patch-package": "^8.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.53.2", "react-native": "0.79.3", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-copilot": "^3.3.3", "react-native-device-info": "^14.0.0", "react-native-element-dropdown": "^2.12.2", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-masked-view": "^0.2.0", "react-native-modal": "^14.0.0-rc.1", "react-native-pager-view": "6.7.1", "react-native-range-slider-expo": "^1.4.3", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.0.3", "react-native-sticky-parallax-header": "^1.1.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.1", "react-native-uuid": "^2.0.2", "react-native-view-shot": "4.0.3", "react-native-web": "^0.20.0", "react-native-webengage": "^1.5.2", "react-native-webview": "13.13.5", "react-responsive": "^10.0.0", "react-responsive-carousel": "^3.2.23", "react-text-gradients": "^1.0.2", "react-virtuoso": "^4.6.0", "rive-react-native": "9.2.0", "sp-react-native-in-app-updates": "^1.4.0", "tamagui": "^1.126.13", "tslib": "^2.8.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-typescript": "^7.27.0", "@bugsnag/plugin-expo-eas-sourcemaps": "^53.0.0", "@bugsnag/source-maps": "^2.3.3", "@playwright/test": "^1.52.0", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.15", "@types/node": "^22.15.2", "@types/opentype.js": "^1.3.8", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "@welldone-software/why-did-you-render": "^10.0.1", "eslint": "^9.28.0", "eslint-config-airbnb": "19.0.4", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-perfectionist": "^4.14.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "jest-react-native": "^18.0.0", "lint-staged": "^16.1.0", "metro-minify-terser": "^0.82.2", "prettier": "^3.2.5", "prop-types": "^15.8.1", "react-test-renderer": "19.0.0", "terser": "^5.39.0", "typescript": "~5.8.3"}, "resolutions": {"tslib": "^2.8.1"}, "private": true}