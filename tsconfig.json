{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["./*"], "atoms/*": ["./src/components/atoms/*"], "shared/*": ["./src/components/shared/*"], "assets/*": ["./assets/*"], "core/*": ["./src/core/*"], "modules/*": ["./src/modules/*"], "navigator/*": ["./src/navigator/*"], "molecules/*": ["./src/components/molecules/*"], "store/*": ["./src/store/*"], "wasm/*": ["./src/wasm/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.jsx", "**/*.js", "src/types/*.d.ts"]}