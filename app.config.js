export default {
  expo: {
    name: 'Matik<PERSON>',
    slug: 'matiks',
    version: '1.8.000',
    orientation: 'portrait',
    icon: './assets/images/icon.png',
    scheme: 'maths-some',
    userInterfaceStyle: 'dark',
    splash: {
      image: './assets/images/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#1E1E1E',
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.matiks.app',
      associatedDomains: ['applinks:matiks.in', 'applinks:matiks.com'],
      plugins: ['expo-apple-authentication'],
      newArchEnabled: false, // Explicitly disable New Arch
    },
    android: {
      newArchEnabled: false,
      adaptiveIcon: {
        foregroundImage: './assets/images/adaptive-icon.png',
        backgroundColor: '#1E1E1E',
      },
      package: 'com.matiks.app',
      googleServicesFile: './android/app/google-services.json',
      intentFilters: [
        {
          action: 'VIEW',
          autoVerify: true,
          data: [
            {
              scheme: 'https',
              host: 'www.matiks.in',
              pathPrefix: '/',
            },
            {
              scheme: 'https',
              host: 'matiks.in',
              pathPrefix: '/',
            },
          ],
          category: ['BROWSABLE', 'DEFAULT'],
        },
      ],
    },
    web: {
      bundler: 'metro',
      favicon: './assets/images/favicon.ico',
    },
    plugins: [
      'expo-asset',
      [
        'expo-router',
        {
          origin: 'https://www.matiks.com',
          asyncRoutes: {
            web: true,
            android: false,
            ios: false,
            default: 'development',
          },
        },
      ],
      [
        'expo-audio',
        {
          microphonePermission:
            'Allow $(PRODUCT_NAME) to access your microphone.',
        },
      ],
      [
        'expo-video',
        {
          supportsBackgroundPlayback: true,
          supportsPictureInPicture: true,
        },
      ],
      [
        '@sentry/react-native/expo',
        {
          url: 'https://sentry.io/',
          project: 'react-native',
          organization: 'matikscom',
        },
      ],
      [
        'expo-secure-store',
        {
          faceIDPermission:
            'Allow $(PRODUCT_NAME) to access your Face ID biometric data.',
        },
      ],
      'expo-localization',
      '@react-native-google-signin/google-signin',
      [
        'expo-notifications',
        {
          icon: './assets/images/icon.png',
        },
      ],
      [
        'expo-font',
        {
          fonts: [
            './assets/fonts/Montserrat-Black.ttf',
            './assets/fonts/Montserrat-Bold.ttf',
            './assets/fonts/Montserrat-ExtraBold.ttf',
            './assets/fonts/Montserrat-ExtraLight.ttf',
            './assets/fonts/Montserrat-Light.ttf',
            './assets/fonts/Montserrat-Medium.ttf',
            './assets/fonts/Montserrat-Regular.ttf',
            './assets/fonts/Montserrat-SemiBold.ttf',
            './assets/fonts/Montserrat-Thin.ttf',
            './assets/fonts/Montserrat-Regular.ttf',
            './assets/fonts/BebasNeue-Regular.ttf',
          ],
        },
      ],
      '@bugsnag/plugin-expo-eas-sourcemaps',
    ],
    extra: {
      router: {
        origin: false,
      },
      eas: {
        projectId: 'cc9f2f7d-9233-4cb8-ae22-c1af7c19a3c3',
      },
      bugsnag: {
        apiKey: process.env.EXPO_PUBLIC_BUGSNAG_API_KEY,
      },
    },
    owner: 'matiks',
    runtimeVersion: '1.0.5',
    updates: {
      url: 'https://u.expo.dev/cc9f2f7d-9233-4cb8-ae22-c1af7c19a3c3',
    },
  },
};
